!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("@firebase/app")):"function"==typeof define&&define.amd?define(["@firebase/app"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).firebase)}(this,function(gm){"use strict";try{!function(){function t(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var e=t(gm),r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)};function n(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var o=function(){return(o=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};function y(t,s,a,u){return new(a=a||Promise)(function(n,e){function r(t){try{o(u.next(t))}catch(t){e(t)}}function i(t){try{o(u.throw(t))}catch(t){e(t)}}function o(t){var e;t.done?n(t.value):((e=t.value)instanceof a?e:new a(function(t){t(e)})).then(r,i)}o((u=u.apply(t,s||[])).next())})}function g(n,r){var i,o,s,a={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]},t={next:e(0),throw:e(1),return:e(2)};return"function"==typeof Symbol&&(t[Symbol.iterator]=function(){return this}),t;function e(e){return function(t){return function(e){if(i)throw new TypeError("Generator is already executing.");for(;a;)try{if(i=1,o&&(s=2&e[0]?o.return:e[0]?o.throw||((s=o.return)&&s.call(o),0):o.next)&&!(s=s.call(o,e[1])).done)return s;switch(o=0,(e=s?[2&e[0],s.value]:e)[0]){case 0:case 1:s=e;break;case 4:return a.label++,{value:e[1],done:!1};case 5:a.label++,o=e[1],e=[0];continue;case 7:e=a.ops.pop(),a.trys.pop();continue;default:if(!(s=0<(s=a.trys).length&&s[s.length-1])&&(6===e[0]||2===e[0])){a=0;continue}if(3===e[0]&&(!s||e[1]>s[0]&&e[1]<s[3])){a.label=e[1];break}if(6===e[0]&&a.label<s[1]){a.label=s[1],s=e;break}if(s&&a.label<s[2]){a.label=s[2],a.ops.push(e);break}s[2]&&a.ops.pop(),a.trys.pop();continue}e=r.call(n,a)}catch(t){e=[6,t],o=0}finally{i=s=0}if(5&e[0])throw e[1];return{value:e[0]?e[1]:void 0,done:!0}}([e,t])}}}function s(t,e){for(var n=0,r=e.length,i=t.length;n<r;n++,i++)t[i]=e[n];return t}var a={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray:function(t,e){if(!Array.isArray(t))throw Error("encodeByteArray takes an array as a parameter");this.init_();for(var n=e?this.byteToCharMapWebSafe_:this.byteToCharMap_,r=[],i=0;i<t.length;i+=3){var o=t[i],s=i+1<t.length,a=s?t[i+1]:0,u=i+2<t.length,c=u?t[i+2]:0,h=(15&a)<<2|c>>6,c=63&c;u||(c=64,s||(h=64)),r.push(n[o>>2],n[(3&o)<<4|a>>4],n[h],n[c])}return r.join("")},encodeString:function(t,e){return this.HAS_NATIVE_SUPPORT&&!e?btoa(t):this.encodeByteArray(function(t){for(var e=[],n=0,r=0;r<t.length;r++){var i=t.charCodeAt(r);i<128?e[n++]=i:(i<2048?e[n++]=i>>6|192:(55296==(64512&i)&&r+1<t.length&&56320==(64512&t.charCodeAt(r+1))?(i=65536+((1023&i)<<10)+(1023&t.charCodeAt(++r)),e[n++]=i>>18|240,e[n++]=i>>12&63|128):e[n++]=i>>12|224,e[n++]=i>>6&63|128),e[n++]=63&i|128)}return e}(t),e)},decodeString:function(t,e){return this.HAS_NATIVE_SUPPORT&&!e?atob(t):function(t){for(var e=[],n=0,r=0;n<t.length;){var i,o,s,a=t[n++];a<128?e[r++]=String.fromCharCode(a):191<a&&a<224?(o=t[n++],e[r++]=String.fromCharCode((31&a)<<6|63&o)):239<a&&a<365?(i=((7&a)<<18|(63&(o=t[n++]))<<12|(63&(s=t[n++]))<<6|63&t[n++])-65536,e[r++]=String.fromCharCode(55296+(i>>10)),e[r++]=String.fromCharCode(56320+(1023&i))):(o=t[n++],s=t[n++],e[r++]=String.fromCharCode((15&a)<<12|(63&o)<<6|63&s))}return e.join("")}(this.decodeStringToByteArray(t,e))},decodeStringToByteArray:function(t,e){this.init_();for(var n=e?this.charToByteMapWebSafe_:this.charToByteMap_,r=[],i=0;i<t.length;){var o=n[t.charAt(i++)],s=i<t.length?n[t.charAt(i)]:0,a=++i<t.length?n[t.charAt(i)]:64,u=++i<t.length?n[t.charAt(i)]:64;if(++i,null==o||null==s||null==a||null==u)throw Error();r.push(o<<2|s>>4),64!==a&&(r.push(s<<4&240|a>>2),64!==u&&r.push(a<<6&192|u))}return r},init_:function(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(var t=0;t<this.ENCODED_VALS.length;t++)this.byteToCharMap_[t]=this.ENCODED_VALS.charAt(t),this.charToByteMap_[this.byteToCharMap_[t]]=t,this.byteToCharMapWebSafe_[t]=this.ENCODED_VALS_WEBSAFE.charAt(t),(this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[t]]=t)>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(t)]=t,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(t)]=t)}}};function h(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}var i,u="FirebaseError",c=(n(l,i=Error),l);function l(t,e,n){e=i.call(this,e)||this;return e.code=t,e.customData=n,e.name=u,Object.setPrototypeOf(e,l.prototype),Error.captureStackTrace&&Error.captureStackTrace(e,f.prototype.create),e}var f=(d.prototype.create=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];var r,i=e[0]||{},o=this.service+"/"+t,t=this.errors[t],t=t?(r=i,t.replace(m,function(t,e){var n=r[e];return null!=n?String(n):"<"+e+"?>"})):"Error",t=this.serviceName+": "+t+" ("+o+").";return new c(o,t,i)},d);function d(t,e,n){this.service=t,this.serviceName=e,this.errors=n}var p,m=/\{\$([^}]+)}/g;function v(t){return t&&t._delegate?t._delegate:t}(D=p=p||{})[D.DEBUG=0]="DEBUG",D[D.VERBOSE=1]="VERBOSE",D[D.INFO=2]="INFO",D[D.WARN=3]="WARN",D[D.ERROR=4]="ERROR",D[D.SILENT=5]="SILENT";function w(t,e){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];if(!(e<t.logLevel)){var i=(new Date).toISOString(),o=T[e];if(!o)throw new Error("Attempted to log a message with an invalid logType (value: "+e+")");console[o].apply(console,s(["["+i+"]  "+t.name+":"],n))}}var b={debug:p.DEBUG,verbose:p.VERBOSE,info:p.INFO,warn:p.WARN,error:p.ERROR,silent:p.SILENT},E=p.INFO,T=((be={})[p.DEBUG]="log",be[p.VERBOSE]="log",be[p.INFO]="info",be[p.WARN]="warn",be[p.ERROR]="error",be),I=(Object.defineProperty(_.prototype,"logLevel",{get:function(){return this._logLevel},set:function(t){if(!(t in p))throw new TypeError('Invalid value "'+t+'" assigned to `logLevel`');this._logLevel=t},enumerable:!1,configurable:!0}),_.prototype.setLogLevel=function(t){this._logLevel="string"==typeof t?b[t]:t},Object.defineProperty(_.prototype,"logHandler",{get:function(){return this._logHandler},set:function(t){if("function"!=typeof t)throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=t},enumerable:!1,configurable:!0}),Object.defineProperty(_.prototype,"userLogHandler",{get:function(){return this._userLogHandler},set:function(t){this._userLogHandler=t},enumerable:!1,configurable:!0}),_.prototype.debug=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this._userLogHandler&&this._userLogHandler.apply(this,s([this,p.DEBUG],t)),this._logHandler.apply(this,s([this,p.DEBUG],t))},_.prototype.log=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this._userLogHandler&&this._userLogHandler.apply(this,s([this,p.VERBOSE],t)),this._logHandler.apply(this,s([this,p.VERBOSE],t))},_.prototype.info=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this._userLogHandler&&this._userLogHandler.apply(this,s([this,p.INFO],t)),this._logHandler.apply(this,s([this,p.INFO],t))},_.prototype.warn=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this._userLogHandler&&this._userLogHandler.apply(this,s([this,p.WARN],t)),this._logHandler.apply(this,s([this,p.WARN],t))},_.prototype.error=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this._userLogHandler&&this._userLogHandler.apply(this,s([this,p.ERROR],t)),this._logHandler.apply(this,s([this,p.ERROR],t))},_);function _(t){this.name=t,this._logLevel=E,this._logHandler=w,this._userLogHandler=null}var S=function(t,e){return(S=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)};function A(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return{value:(t=t&&r>=t.length?void 0:t)&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}var D,N="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},C={},k=N||self;function R(){}function x(t){var e=typeof t;return"array"==(e="object"!=e?e:t?Array.isArray(t)?"array":e:"null")||"object"==e&&"number"==typeof t.length}function O(t){var e=typeof t;return"object"==e&&null!=t||"function"==e}var L="closure_uid_"+(1e9*Math.random()>>>0),P=0;function M(t,e,n){return t.call.apply(t.bind,arguments)}function F(e,n,t){if(!e)throw Error();if(2<arguments.length){var r=Array.prototype.slice.call(arguments,2);return function(){var t=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(t,r),e.apply(n,t)}}return function(){return e.apply(n,arguments)}}function V(t,e,n){return(V=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?M:F).apply(null,arguments)}function U(e){var n=Array.prototype.slice.call(arguments,1);return function(){var t=n.slice();return t.push.apply(t,arguments),e.apply(this,t)}}function q(t,o){function e(){}e.prototype=o.prototype,t.Z=o.prototype,t.prototype=new e,(t.prototype.constructor=t).Vb=function(t,e,n){for(var r=Array(arguments.length-2),i=2;i<arguments.length;i++)r[i-2]=arguments[i];return o.prototype[e].apply(t,r)}}function B(){this.s=this.s,this.o=this.o}var j={};B.prototype.s=!1,B.prototype.na=function(){var t;!this.s&&(this.s=!0,this.M(),0)&&(t=this,t=Object.prototype.hasOwnProperty.call(t,L)&&t[L]||(t[L]=++P),delete j[t])},B.prototype.M=function(){if(this.o)for(;this.o.length;)this.o.shift()()};var K=Array.prototype.indexOf?function(t,e){return Array.prototype.indexOf.call(t,e,void 0)}:function(t,e){if("string"==typeof t)return"string"!=typeof e||1!=e.length?-1:t.indexOf(e,0);for(var n=0;n<t.length;n++)if(n in t&&t[n]===e)return n;return-1},G=Array.prototype.forEach?function(t,e,n){Array.prototype.forEach.call(t,e,n)}:function(t,e,n){for(var r=t.length,i="string"==typeof t?t.split(""):t,o=0;o<r;o++)o in i&&e.call(n,i[o],o,t)};function Q(){return Array.prototype.concat.apply([],arguments)}function H(t){var e=t.length;if(0<e){for(var n=Array(e),r=0;r<e;r++)n[r]=t[r];return n}return[]}function z(t){return/^[\s\xa0]*$/.test(t)}var W,Y=String.prototype.trim?function(t){return t.trim()}:function(t){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(t)[1]};function X(t,e){return-1!=t.indexOf(e)}function $(t,e){return t<e?-1:e<t?1:0}t:{var J=k.navigator;if(J){J=J.userAgent;if(J){W=J;break t}}W=""}function Z(t,e,n){for(var r in t)e.call(n,t[r],r,t)}function tt(t){var e,n={};for(e in t)n[e]=t[e];return n}var et="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function nt(t){for(var e,n,r=1;r<arguments.length;r++){for(e in n=arguments[r])t[e]=n[e];for(var i=0;i<et.length;i++)e=et[i],Object.prototype.hasOwnProperty.call(n,e)&&(t[e]=n[e])}}function rt(t){return rt[" "](t),t}rt[" "]=R;var it,ot=X(W,"Opera"),st=X(W,"Trident")||X(W,"MSIE"),at=X(W,"Edge"),ut=at||st,ct=X(W,"Gecko")&&!(X(W.toLowerCase(),"webkit")&&!X(W,"Edge"))&&!(X(W,"Trident")||X(W,"MSIE"))&&!X(W,"Edge"),ht=X(W.toLowerCase(),"webkit")&&!X(W,"Edge");function lt(){var t=k.document;return t?t.documentMode:void 0}t:{var ft="",dt=(dt=W,ct?/rv:([^\);]+)(\)|;)/.exec(dt):at?/Edge\/([\d\.]+)/.exec(dt):st?/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(dt):ht?/WebKit\/(\S+)/.exec(dt):ot?/(?:Version)[ \/]?(\S+)/.exec(dt):void 0);if(dt&&(ft=dt?dt[1]:""),st){dt=lt();if(null!=dt&&dt>parseFloat(ft)){it=String(dt);break t}}it=ft}var pt={};function yt(){return t=function(){for(var t=0,e=Y(String(it)).split("."),n=Y("9").split("."),r=Math.max(e.length,n.length),i=0;0==t&&i<r;i++)for(var o=e[i]||"",s=n[i]||"";o=/(\d*)(\D*)(.*)/.exec(o)||["","","",""],s=/(\d*)(\D*)(.*)/.exec(s)||["","","",""],(0!=o[0].length||0!=s[0].length)&&(t=$(0==o[1].length?0:parseInt(o[1],10),0==s[1].length?0:parseInt(s[1],10))||$(0==o[2].length,0==s[2].length)||$(o[2],s[2]),o=o[3],s=s[3],0==t););return 0<=t},e=pt,Object.prototype.hasOwnProperty.call(e,9)?e[9]:e[9]=t(9);var t,e}var gt=k.document&&st&&(lt()||parseInt(it,10))||void 0,mt=function(){if(!k.addEventListener||!Object.defineProperty)return!1;var t=!1,e=Object.defineProperty({},"passive",{get:function(){t=!0}});try{k.addEventListener("test",R,e),k.removeEventListener("test",R,e)}catch(t){}return t}();function vt(t,e){this.type=t,this.g=this.target=e,this.defaultPrevented=!1}function wt(t,e){if(vt.call(this,t?t.type:""),this.relatedTarget=this.g=this.target=null,this.button=this.screenY=this.screenX=this.clientY=this.clientX=0,this.key="",this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1,this.state=null,this.pointerId=0,this.pointerType="",this.i=null,t){var n=this.type=t.type,r=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:null;if(this.target=t.target||t.srcElement,this.g=e,e=t.relatedTarget){if(ct){t:{try{rt(e.nodeName);var i=!0;break t}catch(t){}i=!1}i||(e=null)}}else"mouseover"==n?e=t.fromElement:"mouseout"==n&&(e=t.toElement);this.relatedTarget=e,r?(this.clientX=void 0!==r.clientX?r.clientX:r.pageX,this.clientY=void 0!==r.clientY?r.clientY:r.pageY,this.screenX=r.screenX||0,this.screenY=r.screenY||0):(this.clientX=void 0!==t.clientX?t.clientX:t.pageX,this.clientY=void 0!==t.clientY?t.clientY:t.pageY,this.screenX=t.screenX||0,this.screenY=t.screenY||0),this.button=t.button,this.key=t.key||"",this.ctrlKey=t.ctrlKey,this.altKey=t.altKey,this.shiftKey=t.shiftKey,this.metaKey=t.metaKey,this.pointerId=t.pointerId||0,this.pointerType="string"==typeof t.pointerType?t.pointerType:bt[t.pointerType]||"",this.state=t.state,(this.i=t).defaultPrevented&&wt.Z.h.call(this)}}vt.prototype.h=function(){this.defaultPrevented=!0},q(wt,vt);var bt={2:"touch",3:"pen",4:"mouse"};wt.prototype.h=function(){wt.Z.h.call(this);var t=this.i;t.preventDefault?t.preventDefault():t.returnValue=!1};var Et="closure_listenable_"+(1e6*Math.random()|0),Tt=0;function It(t,e,n,r,i){this.listener=t,this.proxy=null,this.src=e,this.type=n,this.capture=!!r,this.ia=i,this.key=++Tt,this.ca=this.fa=!1}function _t(t){t.ca=!0,t.listener=null,t.proxy=null,t.src=null,t.ia=null}function St(t){this.src=t,this.g={},this.h=0}function At(t,e){var n,r,i,o=e.type;o in t.g&&(n=t.g[o],(i=0<=(r=K(n,e)))&&Array.prototype.splice.call(n,r,1),i&&(_t(e),0==t.g[o].length&&(delete t.g[o],t.h--)))}function Dt(t,e,n,r){for(var i=0;i<t.length;++i){var o=t[i];if(!o.ca&&o.listener==e&&o.capture==!!n&&o.ia==r)return i}return-1}St.prototype.add=function(t,e,n,r,i){var o=t.toString();(t=this.g[o])||(t=this.g[o]=[],this.h++);var s=Dt(t,e,r,i);return-1<s?(e=t[s],n||(e.fa=!1)):((e=new It(e,this.src,o,!!r,i)).fa=n,t.push(e)),e};var Nt="closure_lm_"+(1e6*Math.random()|0),Ct={};function kt(t,e,n,r,i){if(r&&r.once)return function t(e,n,r,i,o){if(Array.isArray(n)){for(var s=0;s<n.length;s++)t(e,n[s],r,i,o);return null}r=Ft(r);return e&&e[Et]?e.O(n,r,O(i)?!!i.capture:!!i,o):Rt(e,n,r,!0,i,o)}(t,e,n,r,i);if(Array.isArray(e)){for(var o=0;o<e.length;o++)kt(t,e[o],n,r,i);return null}return n=Ft(n),t&&t[Et]?t.N(e,n,O(r)?!!r.capture:!!r,i):Rt(t,e,n,!1,r,i)}function Rt(t,e,n,r,i,o){if(!e)throw Error("Invalid event type");var s,a=O(i)?!!i.capture:!!i,u=Pt(t);if(u||(t[Nt]=u=new St(t)),(n=u.add(e,n,r,a,o)).proxy)return n;if(s=Lt,(n.proxy=r=function t(e){return s.call(t.src,t.listener,e)}).src=t,r.listener=n,t.addEventListener)void 0===(i=!mt?a:i)&&(i=!1),t.addEventListener(e.toString(),r,i);else if(t.attachEvent)t.attachEvent(Ot(e.toString()),r);else{if(!t.addListener||!t.removeListener)throw Error("addEventListener and attachEvent are unavailable.");t.addListener(r)}return n}function xt(t){var e,n,r;"number"!=typeof t&&t&&!t.ca&&((e=t.src)&&e[Et]?At(e.i,t):(n=t.type,r=t.proxy,e.removeEventListener?e.removeEventListener(n,r,t.capture):e.detachEvent?e.detachEvent(Ot(n),r):e.addListener&&e.removeListener&&e.removeListener(r),(n=Pt(e))?(At(n,t),0==n.h&&(n.src=null,e[Nt]=null)):_t(t)))}function Ot(t){return t in Ct?Ct[t]:Ct[t]="on"+t}function Lt(t,e){var n,r;return t=!!t.ca||(e=new wt(e,this),n=t.listener,r=t.ia||t.src,t.fa&&xt(t),n.call(r,e))}function Pt(t){return(t=t[Nt])instanceof St?t:null}var Mt="__closure_events_fn_"+(1e9*Math.random()>>>0);function Ft(e){return"function"==typeof e?e:(e[Mt]||(e[Mt]=function(t){return e.handleEvent(t)}),e[Mt])}function Vt(){B.call(this),this.i=new St(this),(this.P=this).I=null}function Ut(t,e){var n,r=t.I;if(r)for(n=[];r;r=r.I)n.push(r);if(t=t.P,r=e.type||e,"string"==typeof e?e=new vt(e,t):e instanceof vt?e.target=e.target||t:(s=e,nt(e=new vt(r,t),s)),s=!0,n)for(var i=n.length-1;0<=i;i--)var o=e.g=n[i],s=qt(o,r,!0,e)&&s;if(s=qt(o=e.g=t,r,!0,e)&&s,s=qt(o,r,!1,e)&&s,n)for(i=0;i<n.length;i++)s=qt(o=e.g=n[i],r,!1,e)&&s}function qt(t,e,n,r){if(!(e=t.i.g[String(e)]))return!0;e=e.concat();for(var i=!0,o=0;o<e.length;++o){var s,a,u=e[o];u&&!u.ca&&u.capture==n&&(s=u.listener,a=u.ia||u.src,u.fa&&At(t.i,u),i=!1!==s.call(a,r)&&i)}return i&&!r.defaultPrevented}q(Vt,B),Vt.prototype[Et]=!0,Vt.prototype.removeEventListener=function(t,e,n,r){!function t(e,n,r,i,o){if(Array.isArray(n))for(var s=0;s<n.length;s++)t(e,n[s],r,i,o);else i=O(i)?!!i.capture:!!i,r=Ft(r),e&&e[Et]?(e=e.i,(n=String(n).toString())in e.g&&-1<(r=Dt(s=e.g[n],r,i,o))&&(_t(s[r]),Array.prototype.splice.call(s,r,1),0==s.length&&(delete e.g[n],e.h--))):(e=e&&Pt(e))&&(n=e.g[n.toString()],(r=(e=-1)<(e=n?Dt(n,r,i,o):e)?n[e]:null)&&xt(r))}(this,t,e,n,r)},Vt.prototype.M=function(){if(Vt.Z.M.call(this),this.i){var t,e=this.i;for(t in e.g){for(var n=e.g[t],r=0;r<n.length;r++)_t(n[r]);delete e.g[t],e.h--}}this.I=null},Vt.prototype.N=function(t,e,n,r){return this.i.add(String(t),e,!1,n,r)},Vt.prototype.O=function(t,e,n,r){return this.i.add(String(t),e,!0,n,r)};var Bt=k.JSON.stringify;var jt=(Kt.prototype.add=function(t,e){var n=Gt.get();n.set(t,e),this.h?this.h.next=n:this.g=n,this.h=n},Kt);function Kt(){this.h=this.g=null}var Gt=(Qt.prototype.get=function(){var t;return 0<this.h?(this.h--,t=this.g,this.g=t.next,t.next=null):t=this.i(),t},new Qt(function(){return new zt},function(t){return t.reset()}));function Qt(t,e){this.i=t,this.j=e,this.h=0,this.g=null}var Ht,zt=(Wt.prototype.set=function(t,e){this.h=t,this.g=e,this.next=null},Wt.prototype.reset=function(){this.next=this.g=this.h=null},Wt);function Wt(){this.next=this.g=this.h=null}function Yt(t,e){var n;Ht||(n=k.Promise.resolve(void 0),Ht=function(){n.then(Jt)}),Xt||(Ht(),Xt=!0),$t.add(t,e)}var Xt=!1,$t=new jt;function Jt(){for(var t;e=t=void 0,e=null,(t=$t).g&&(e=t.g,t.g=t.g.next,t.g||(t.h=null),e.next=null),t=e;){try{t.h.call(t.g)}catch(t){!function(t){k.setTimeout(function(){throw t},0)}(t)}var e=Gt;e.j(t),e.h<100&&(e.h++,t.next=e.g,e.g=t)}Xt=!1}function Zt(t,e){Vt.call(this),this.h=t||1,this.g=e||k,this.j=V(this.kb,this),this.l=Date.now()}function te(t){t.da=!1,t.S&&(t.g.clearTimeout(t.S),t.S=null)}function ee(t,e,n){if("function"==typeof t)n&&(t=V(t,n));else{if(!t||"function"!=typeof t.handleEvent)throw Error("Invalid listener argument");t=V(t.handleEvent,t)}return 2147483647<Number(e)?-1:k.setTimeout(t,e||0)}q(Zt,Vt),(D=Zt.prototype).da=!1,D.S=null,D.kb=function(){var t;this.da&&(0<(t=Date.now()-this.l)&&t<.8*this.h?this.S=this.g.setTimeout(this.j,this.h-t):(this.S&&(this.g.clearTimeout(this.S),this.S=null),Ut(this,"tick"),this.da&&(te(this),this.start())))},D.start=function(){this.da=!0,this.S||(this.S=this.g.setTimeout(this.j,this.h),this.l=Date.now())},D.M=function(){Zt.Z.M.call(this),te(this),delete this.g};var ne,re=(function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}S(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}(ie,ne=B),ie.prototype.l=function(t){this.h=arguments,this.g?this.i=!0:function t(e){e.g=ee(function(){e.g=null,e.i&&(e.i=!1,t(e))},e.j);var n=e.h;e.h=null,e.m.apply(null,n)}(this)},ie.prototype.M=function(){ne.prototype.M.call(this),this.g&&(k.clearTimeout(this.g),this.g=null,this.i=!1,this.h=null)},ie);function ie(t,e){var n=ne.call(this)||this;return n.m=t,n.j=e,n.h=null,n.i=!1,n.g=null,n}function oe(t){B.call(this),this.h=t,this.g={}}q(oe,B);var se=[];function ae(t,e,n,r){Array.isArray(n)||(n&&(se[0]=n.toString()),n=se);for(var i=0;i<n.length;i++){var o=kt(e,n[i],r||t.handleEvent,!1,t.h||t);if(!o)break;t.g[o.key]=o}}function ue(t){Z(t.g,function(t,e){this.g.hasOwnProperty(e)&&xt(t)},t),t.g={}}function ce(){this.g=!0}function he(t,e,n,r){t.info(function(){return"XMLHTTP TEXT ("+e+"): "+function(t,e){if(!t.g)return e;if(!e)return null;try{var n=JSON.parse(e);if(n)for(t=0;t<n.length;t++)if(Array.isArray(n[t])){var r=n[t];if(!(r.length<2)){var i=r[1];if(Array.isArray(i)&&!(i.length<1)){var o=i[0];if("noop"!=o&&"stop"!=o&&"close"!=o)for(var s=1;s<i.length;s++)i[s]=""}}}return Bt(n)}catch(t){return e}}(t,n)+(r?" "+r:"")})}oe.prototype.M=function(){oe.Z.M.call(this),ue(this)},oe.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented")},ce.prototype.Aa=function(){this.g=!1},ce.prototype.info=function(){};var le={},fe=null;function de(){return fe=fe||new Vt}function pe(t){vt.call(this,le.Ma,t)}function ye(){var t=de();Ut(t,new pe(t))}function ge(t,e){vt.call(this,le.STAT_EVENT,t),this.stat=e}function me(t){var e=de();Ut(e,new ge(e,t))}function ve(t,e){vt.call(this,le.Na,t),this.size=e}function we(t,e){if("function"!=typeof t)throw Error("Fn must not be null and must be a function");return k.setTimeout(function(){t()},e)}le.Ma="serverreachability",q(pe,vt),le.STAT_EVENT="statevent",q(ge,vt),le.Na="timingevent",q(ve,vt);var be={NO_ERROR:0,lb:1,yb:2,xb:3,sb:4,wb:5,zb:6,Ja:7,TIMEOUT:8,Cb:9},N={qb:"complete",Mb:"success",Ka:"error",Ja:"abort",Eb:"ready",Fb:"readystatechange",TIMEOUT:"timeout",Ab:"incrementaldata",Db:"progress",tb:"downloadprogress",Ub:"uploadprogress"};function Ee(){}function Te(t){return t.h||(t.h=t.i())}function Ie(){}Ee.prototype.h=null;jt={OPEN:"a",pb:"b",Ka:"c",Bb:"d"};function _e(){vt.call(this,"d")}function Se(){vt.call(this,"c")}function Ae(){}function De(t,e,n,r){this.l=t,this.j=e,this.m=n,this.X=r||1,this.V=new oe(this),this.P=ke,this.W=new Zt(t=ut?125:void 0),this.H=null,this.i=!1,this.s=this.A=this.v=this.K=this.F=this.Y=this.B=null,this.D=[],this.g=null,this.C=0,this.o=this.u=null,this.N=-1,this.I=!1,this.O=0,this.L=null,this.aa=this.J=this.$=this.U=!1,this.h=new Ne}function Ne(){this.i=null,this.g="",this.h=!1}q(_e,vt),q(Se,vt),q(Ae,Ee),Ae.prototype.g=function(){return new XMLHttpRequest},Ae.prototype.i=function(){return{}};var Ce=new Ae,ke=45e3,Re={},xe={};function Oe(t,e,n){t.K=1,t.v=en(Ye(e)),t.s=n,t.U=!0,Le(t,null)}function Le(t,e){t.F=Date.now(),Fe(t),t.A=Ye(t.v);var s,a,u,c,h,l,n=t.A,r=t.X;Array.isArray(r)||(r=[String(r)]),yn(n.h,"t",r),t.C=0,n=t.l.H,t.h=new Ne,t.g=gr(t.l,n?e:null,!t.s),0<t.O&&(t.L=new re(V(t.Ia,t,t.g),t.O)),ae(t.V,t.g,"readystatechange",t.gb),e=t.H?tt(t.H):{},t.s?(t.u||(t.u="POST"),e["Content-Type"]="application/x-www-form-urlencoded",t.g.ea(t.A,t.u,t.s,e)):(t.u="GET",t.g.ea(t.A,t.u,null,e)),ye(),s=t.j,a=t.u,u=t.A,c=t.m,h=t.X,l=t.s,s.info(function(){if(s.g)if(l)for(var t="",e=l.split("&"),n=0;n<e.length;n++){var r,i,o=e[n].split("=");1<o.length&&(r=o[0],o=o[1],t=2<=(i=r.split("_")).length&&"type"==i[1]?t+(r+"=")+o+"&":t+(r+"=redacted&"))}else t=null;else t=l;return"XMLHTTP REQ ("+c+") [attempt "+h+"]: "+a+"\n"+u+"\n"+t})}function Pe(t){return t.g&&("GET"==t.u&&2!=t.K&&t.l.Ba)}function Me(t,e,n){for(var r,i,o,s,a,u=!0;!t.I&&t.C<n.length;){if(o=n,a=s=void 0,s=(i=t).C,(r=-1==(a=o.indexOf("\n",s))?xe:(s=Number(o.substring(s,a)),isNaN(s)?Re:(a+=1)+s>o.length?xe:(o=o.substr(a,s),i.C=a+s,o)))==xe){4==e&&(t.o=4,me(14),u=!1),he(t.j,t.m,null,"[Incomplete Response]");break}if(r==Re){t.o=4,me(15),he(t.j,t.m,n,"[Invalid Chunk]"),u=!1;break}he(t.j,t.m,r,null),je(t,r)}Pe(t)&&r!=xe&&r!=Re&&(t.h.g="",t.C=0),4!=e||0!=n.length||t.h.h||(t.o=1,me(16),u=!1),t.i=t.i&&u,u?0<n.length&&!t.aa&&(t.aa=!0,(e=t.l).g==t&&e.$&&!e.L&&(e.h.info("Great, no buffering proxy detected. Bytes received: "+n.length),ur(e),e.L=!0,me(11))):(he(t.j,t.m,n,"[Invalid Chunked Response]"),Be(t),qe(t))}function Fe(t){t.Y=Date.now()+t.P,Ve(t,t.P)}function Ve(t,e){if(null!=t.B)throw Error("WatchDog timer not null");t.B=we(V(t.eb,t),e)}function Ue(t){t.B&&(k.clearTimeout(t.B),t.B=null)}function qe(t){0==t.l.G||t.I||lr(t.l,t)}function Be(t){Ue(t);var e=t.L;e&&"function"==typeof e.na&&e.na(),t.L=null,te(t.W),ue(t.V),t.g&&(e=t.g,t.g=null,e.abort(),e.na())}function je(t,e){try{var n=t.l;if(0!=n.G&&(n.g==t||In(n.i,t)))if(n.I=t.N,!t.J&&In(n.i,t)&&3==n.G){try{var r=n.Ca.g.parse(e)}catch(y){r=null}if(Array.isArray(r)&&3==r.length){var i=r;if(0==i[0]){t:if(!n.u){if(n.g){if(!(n.g.F+3e3<t.F))break t;hr(n),Zn(n)}ar(n),me(18)}}else n.ta=i[1],0<n.ta-n.U&&i[2]<37500&&n.N&&0==n.A&&!n.v&&(n.v=we(V(n.ab,n),6e3));if(Tn(n.i)<=1&&n.ka){try{n.ka()}catch(y){}n.ka=void 0}}else dr(n,11)}else if(!t.J&&n.g!=t||hr(n),!z(e))for(i=n.Ca.g.parse(e),e=0;e<i.length;e++){var o,s,a,u,c,h,l,f,d,p,y=i[e];n.U=y[0],y=y[1],2==n.G?"c"==y[0]?(n.J=y[1],n.la=y[2],null!=(o=y[3])&&(n.ma=o,n.h.info("VER="+n.ma)),null!=(s=y[4])&&(n.za=s,n.h.info("SVER="+n.za)),null!=(a=y[5])&&"number"==typeof a&&0<a&&(r=1.5*a,n.K=r,n.h.info("backChannelRequestTimeoutMs_="+r)),r=n,(u=t.g)&&(!(c=u.g?u.g.getResponseHeader("X-Client-Wire-Protocol"):null)||!(h=r.i).g&&(X(c,"spdy")||X(c,"quic")||X(c,"h2"))&&(h.j=h.l,h.g=new Set,h.h&&(_n(h,h.h),h.h=null)),!r.D||(l=u.g?u.g.getResponseHeader("X-HTTP-Session-Id"):null)&&(r.sa=l,tn(r.F,r.D,l))),n.G=3,n.j&&n.j.xa(),n.$&&(n.O=Date.now()-t.F,n.h.info("Handshake RTT: "+n.O+"ms")),f=t,(r=n).oa=yr(r,r.H?r.la:null,r.W),f.J?(Sn(r.i,f),d=f,(p=r.K)&&d.setTimeout(p),d.B&&(Ue(d),Fe(d)),r.g=f):sr(r),0<n.l.length&&nr(n)):"stop"!=y[0]&&"close"!=y[0]||dr(n,7):3==n.G&&("stop"==y[0]||"close"==y[0]?"stop"==y[0]?dr(n,7):Jn(n):"noop"!=y[0]&&n.j&&n.j.wa(y),n.A=0)}ye()}catch(y){}}function Ke(t,e){if(t.forEach&&"function"==typeof t.forEach)t.forEach(e,void 0);else if(x(t)||"string"==typeof t)G(t,e,void 0);else{if(t.T&&"function"==typeof t.T)var n=t.T();else if(t.R&&"function"==typeof t.R)n=void 0;else if(x(t)||"string"==typeof t)for(var n=[],r=t.length,i=0;i<r;i++)n.push(i);else for(i in n=[],r=0,t)n[r++]=i;for(var i=(r=function(t){if(t.R&&"function"==typeof t.R)return t.R();if("string"==typeof t)return t.split("");if(x(t)){for(var e=[],n=t.length,r=0;r<n;r++)e.push(t[r]);return e}for(r in e=[],n=0,t)e[n++]=t[r];return e}(t)).length,o=0;o<i;o++)e.call(void 0,r[o],n&&n[o],t)}}function Ge(t,e){this.h={},this.g=[],this.i=0;var n=arguments.length;if(1<n){if(n%2)throw Error("Uneven number of arguments");for(var r=0;r<n;r+=2)this.set(arguments[r],arguments[r+1])}else if(t)if(t instanceof Ge)for(n=t.T(),r=0;r<n.length;r++)this.set(n[r],t.get(n[r]));else for(r in t)this.set(r,t[r])}function Qe(t){if(t.i!=t.g.length){for(var e=0,n=0;e<t.g.length;){var r=t.g[e];He(t.h,r)&&(t.g[n++]=r),e++}t.g.length=n}if(t.i!=t.g.length){for(var i={},n=e=0;e<t.g.length;)He(i,r=t.g[e])||(i[t.g[n++]=r]=1),e++;t.g.length=n}}function He(t,e){return Object.prototype.hasOwnProperty.call(t,e)}(D=De.prototype).setTimeout=function(t){this.P=t},D.gb=function(t){t=t.target;var e=this.L;e&&3==zn(t)?e.l():this.Ia(t)},D.Ia=function(t){try{if(t==this.g)t:{var e=zn(this.g),n=this.g.Da();this.g.ba();if(!(e<3)&&(3!=e||ut||this.g&&(this.h.h||this.g.ga()||Wn(this.g)))){this.I||4!=e||7==n||ye(),Ue(this);var r=this.g.ba();this.N=r;e:if(Pe(this)){var i=Wn(this.g);t="";var o=i.length,s=4==zn(this.g);if(!this.h.i){if("undefined"==typeof TextDecoder){Be(this),qe(this);var a="";break e}this.h.i=new k.TextDecoder}for(n=0;n<o;n++)this.h.h=!0,t+=this.h.i.decode(i[n],{stream:s&&n==o-1});i.splice(0,o),this.h.g+=t,this.C=0,a=this.h.g}else a=this.g.ga();if(this.i=200==r,l=this.j,f=this.u,d=this.A,p=this.m,y=this.X,g=e,m=r,l.info(function(){return"XMLHTTP RESP ("+p+") [ attempt "+y+"]: "+f+"\n"+d+"\n"+g+" "+m}),this.i){if(this.$&&!this.J){e:{if(this.g){var u,c=this.g;if((u=c.g?c.g.getResponseHeader("X-HTTP-Initial-Response"):null)&&!z(u)){var h=u;break e}}h=null}if(!(r=h)){this.i=!1,this.o=3,me(12),Be(this),qe(this);break t}he(this.j,this.m,r,"Initial handshake response via X-HTTP-Initial-Response"),this.J=!0,je(this,r)}this.U?(Me(this,e,a),ut&&this.i&&3==e&&(ae(this.V,this.W,"tick",this.fb),this.W.start())):(he(this.j,this.m,a,null),je(this,a)),4==e&&Be(this),this.i&&!this.I&&(4==e?lr(this.l,this):(this.i=!1,Fe(this)))}else 400==r&&0<a.indexOf("Unknown SID")?(this.o=3,me(12)):(this.o=0,me(13)),Be(this),qe(this)}}}catch(e){}var l,f,d,p,y,g,m},D.fb=function(){var t,e;this.g&&(t=zn(this.g),e=this.g.ga(),this.C<e.length&&(Ue(this),Me(this,t,e),this.i&&4!=t&&Fe(this)))},D.cancel=function(){this.I=!0,Be(this)},D.eb=function(){this.B=null;var t,e,n=Date.now();0<=n-this.Y?(t=this.j,e=this.A,t.info(function(){return"TIMEOUT: "+e}),2!=this.K&&(ye(),me(17)),Be(this),this.o=2,qe(this)):Ve(this,this.Y-n)},(D=Ge.prototype).R=function(){Qe(this);for(var t=[],e=0;e<this.g.length;e++)t.push(this.h[this.g[e]]);return t},D.T=function(){return Qe(this),this.g.concat()},D.get=function(t,e){return He(this.h,t)?this.h[t]:e},D.set=function(t,e){He(this.h,t)||(this.i++,this.g.push(t)),this.h[t]=e},D.forEach=function(t,e){for(var n=this.T(),r=0;r<n.length;r++){var i=n[r],o=this.get(i);t.call(e,o,i,this)}};var ze=/^(?:([^:/?#.]+):)?(?:\/\/(?:([^\\/?#]*)@)?([^\\/?#]*?)(?::([0-9]+))?(?=[\\/?#]|$))?([^?#]+)?(?:\?([^#]*))?(?:#([\s\S]*))?$/;function We(t,e){var n;this.i=this.s=this.j="",this.m=null,this.o=this.l="",this.g=!1,t instanceof We?(this.g=void 0!==e?e:t.g,Xe(this,t.j),this.s=t.s,$e(this,t.i),Je(this,t.m),this.l=t.l,e=t.h,(n=new ln).i=e.i,e.g&&(n.g=new Ge(e.g),n.h=e.h),Ze(this,n),this.o=t.o):t&&(n=String(t).match(ze))?(this.g=!!e,Xe(this,n[1]||"",!0),this.s=nn(n[2]||""),$e(this,n[3]||"",!0),Je(this,n[4]),this.l=nn(n[5]||"",!0),Ze(this,n[6]||"",!0),this.o=nn(n[7]||"")):(this.g=!!e,this.h=new ln(null,this.g))}function Ye(t){return new We(t)}function Xe(t,e,n){t.j=n?nn(e,!0):e,t.j&&(t.j=t.j.replace(/:$/,""))}function $e(t,e,n){t.i=n?nn(e,!0):e}function Je(t,e){if(e){if(e=Number(e),isNaN(e)||e<0)throw Error("Bad port number "+e);t.m=e}else t.m=null}function Ze(t,e,n){var r,i;e instanceof ln?(t.h=e,r=t.h,(i=t.g)&&!r.j&&(fn(r),r.i=null,r.g.forEach(function(t,e){var n=e.toLowerCase();e!=n&&(dn(this,e),yn(this,n,t))},r)),r.j=i):(n||(e=rn(e,cn)),t.h=new ln(e,t.g))}function tn(t,e,n){t.h.set(e,n)}function en(t){return tn(t,"zx",Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^Date.now()).toString(36)),t}function nn(t,e){return t?e?decodeURI(t.replace(/%25/g,"%2525")):decodeURIComponent(t):""}function rn(t,e,n){return"string"==typeof t?(t=encodeURI(t).replace(e,on),t=n?t.replace(/%25([0-9a-fA-F]{2})/g,"%$1"):t):null}function on(t){return"%"+((t=t.charCodeAt(0))>>4&15).toString(16)+(15&t).toString(16)}We.prototype.toString=function(){var t=[],e=this.j;e&&t.push(rn(e,sn,!0),":");var n=this.i;return!n&&"file"!=e||(t.push("//"),(e=this.s)&&t.push(rn(e,sn,!0),"@"),t.push(encodeURIComponent(String(n)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),null!=(n=this.m)&&t.push(":",String(n))),(n=this.l)&&(this.i&&"/"!=n.charAt(0)&&t.push("/"),t.push(rn(n,"/"==n.charAt(0)?un:an,!0))),(n=this.h.toString())&&t.push("?",n),(n=this.o)&&t.push("#",rn(n,hn)),t.join("")};var sn=/[#\/\?@]/g,an=/[#\?:]/g,un=/[#\?]/g,cn=/[#\?@]/g,hn=/#/g;function ln(t,e){this.h=this.g=null,this.i=t||null,this.j=!!e}function fn(n){n.g||(n.g=new Ge,n.h=0,n.i&&function(t,e){if(t){t=t.split("&");for(var n=0;n<t.length;n++){var r,i=t[n].indexOf("="),o=null;0<=i?(r=t[n].substring(0,i),o=t[n].substring(i+1)):r=t[n],e(r,o?decodeURIComponent(o.replace(/\+/g," ")):"")}}}(n.i,function(t,e){n.add(decodeURIComponent(t.replace(/\+/g," ")),e)}))}function dn(t,e){fn(t),e=gn(t,e),He(t.g.h,e)&&(t.i=null,t.h-=t.g.get(e).length,He((t=t.g).h,e)&&(delete t.h[e],t.i--,t.g.length>2*t.i&&Qe(t)))}function pn(t,e){return fn(t),e=gn(t,e),He(t.g.h,e)}function yn(t,e,n){dn(t,e),0<n.length&&(t.i=null,t.g.set(gn(t,e),H(n)),t.h+=n.length)}function gn(t,e){return e=String(e),e=t.j?e.toLowerCase():e}(D=ln.prototype).add=function(t,e){fn(this),this.i=null,t=gn(this,t);var n=this.g.get(t);return n||this.g.set(t,n=[]),n.push(e),this.h+=1,this},D.forEach=function(n,r){fn(this),this.g.forEach(function(t,e){G(t,function(t){n.call(r,t,e,this)},this)},this)},D.T=function(){fn(this);for(var t=this.g.R(),e=this.g.T(),n=[],r=0;r<e.length;r++)for(var i=t[r],o=0;o<i.length;o++)n.push(e[r]);return n},D.R=function(t){fn(this);var e=[];if("string"==typeof t)pn(this,t)&&(e=Q(e,this.g.get(gn(this,t))));else{t=this.g.R();for(var n=0;n<t.length;n++)e=Q(e,t[n])}return e},D.set=function(t,e){return fn(this),this.i=null,pn(this,t=gn(this,t))&&(this.h-=this.g.get(t).length),this.g.set(t,[e]),this.h+=1,this},D.get=function(t,e){return t&&0<(t=this.R(t)).length?String(t[0]):e},D.toString=function(){if(this.i)return this.i;if(!this.g)return"";for(var t=[],e=this.g.T(),n=0;n<e.length;n++)for(var r=e[n],i=encodeURIComponent(String(r)),r=this.R(r),o=0;o<r.length;o++){var s=i;""!==r[o]&&(s+="="+encodeURIComponent(String(r[o]))),t.push(s)}return this.i=t.join("&")};var mn=function(t,e){this.h=t,this.g=e};function vn(t){this.l=t||bn,t=k.PerformanceNavigationTiming?0<(t=k.performance.getEntriesByType("navigation")).length&&("hq"==t[0].nextHopProtocol||"h2"==t[0].nextHopProtocol):!!(k.g&&k.g.Ea&&k.g.Ea()&&k.g.Ea().Zb),this.j=t?this.l:1,this.g=null,1<this.j&&(this.g=new Set),this.h=null,this.i=[]}var wn,bn=10;function En(t){return t.h||t.g&&t.g.size>=t.j}function Tn(t){return t.h?1:t.g?t.g.size:0}function In(t,e){return t.h?t.h==e:t.g&&t.g.has(e)}function _n(t,e){t.g?t.g.add(e):t.h=e}function Sn(t,e){t.h&&t.h==e?t.h=null:t.g&&t.g.has(e)&&t.g.delete(e)}function An(t){var e,n;if(null!=t.h)return t.i.concat(t.h.D);if(null==t.g||0===t.g.size)return H(t.i);var r=t.i;try{for(var i=A(t.g.values()),o=i.next();!o.done;o=i.next())var s=o.value,r=r.concat(s.D)}catch(t){e={error:t}}finally{try{o&&!o.done&&(n=i.return)&&n.call(i)}finally{if(e)throw e.error}}return r}function Dn(){}function Nn(){this.g=new Dn}function Cn(t,e,n,r,i){try{e.onload=null,e.onerror=null,e.onabort=null,e.ontimeout=null,i(r)}catch(t){}}function kn(t){this.l=t.$b||null,this.j=t.ib||!1}function Rn(t,e){Vt.call(this),this.D=t,this.u=e,this.m=void 0,this.readyState=xn,this.status=0,this.responseType=this.responseText=this.response=this.statusText="",this.onreadystatechange=null,this.v=new Headers,this.h=null,this.C="GET",this.B="",this.g=!1,this.A=this.j=this.l=null}vn.prototype.cancel=function(){var e,t;if(this.i=An(this),this.h)this.h.cancel(),this.h=null;else if(this.g&&0!==this.g.size){try{for(var n=A(this.g.values()),r=n.next();!r.done;r=n.next())r.value.cancel()}catch(t){e={error:t}}finally{try{r&&!r.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}this.g.clear()}},Dn.prototype.stringify=function(t){return k.JSON.stringify(t,void 0)},Dn.prototype.parse=function(t){return k.JSON.parse(t,void 0)},q(kn,Ee),kn.prototype.g=function(){return new Rn(this.l,this.j)},kn.prototype.i=(wn={},function(){return wn}),q(Rn,Vt);var xn=0;function On(t){t.j.read().then(t.Sa.bind(t)).catch(t.ha.bind(t))}function Ln(t){t.readyState=4,t.l=null,t.j=null,t.A=null,Pn(t)}function Pn(t){t.onreadystatechange&&t.onreadystatechange.call(t)}(D=Rn.prototype).open=function(t,e){if(this.readyState!=xn)throw this.abort(),Error("Error reopening a connection");this.C=t,this.B=e,this.readyState=1,Pn(this)},D.send=function(t){if(1!=this.readyState)throw this.abort(),Error("need to call open() first. ");this.g=!0;var e={headers:this.v,method:this.C,credentials:this.m,cache:void 0};t&&(e.body=t),(this.D||k).fetch(new Request(this.B,e)).then(this.Va.bind(this),this.ha.bind(this))},D.abort=function(){this.response=this.responseText="",this.v=new Headers,this.status=0,this.j&&this.j.cancel("Request was aborted."),1<=this.readyState&&this.g&&4!=this.readyState&&(this.g=!1,Ln(this)),this.readyState=xn},D.Va=function(t){if(this.g&&(this.l=t,this.h||(this.status=this.l.status,this.statusText=this.l.statusText,this.h=t.headers,this.readyState=2,Pn(this)),this.g&&(this.readyState=3,Pn(this),this.g)))if("arraybuffer"===this.responseType)t.arrayBuffer().then(this.Ta.bind(this),this.ha.bind(this));else if(void 0!==k.ReadableStream&&"body"in t){if(this.j=t.body.getReader(),this.u){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');this.response=[]}else this.response=this.responseText="",this.A=new TextDecoder;On(this)}else t.text().then(this.Ua.bind(this),this.ha.bind(this))},D.Sa=function(t){var e;this.g&&(this.u&&t.value?this.response.push(t.value):this.u||(e=t.value||new Uint8Array(0),(e=this.A.decode(e,{stream:!t.done}))&&(this.response=this.responseText+=e)),(t.done?Ln:Pn)(this),3==this.readyState&&On(this))},D.Ua=function(t){this.g&&(this.response=this.responseText=t,Ln(this))},D.Ta=function(t){this.g&&(this.response=t,Ln(this))},D.ha=function(){this.g&&Ln(this)},D.setRequestHeader=function(t,e){this.v.append(t,e)},D.getResponseHeader=function(t){return this.h&&this.h.get(t.toLowerCase())||""},D.getAllResponseHeaders=function(){if(!this.h)return"";for(var t=[],e=this.h.entries(),n=e.next();!n.done;)n=n.value,t.push(n[0]+": "+n[1]),n=e.next();return t.join("\r\n")},Object.defineProperty(Rn.prototype,"withCredentials",{get:function(){return"include"===this.m},set:function(t){this.m=t?"include":"same-origin"}});var Mn=k.JSON.parse;function Fn(t){Vt.call(this),this.headers=new Ge,this.u=t||null,this.h=!1,this.C=this.g=null,this.H="",this.m=0,this.j="",this.l=this.F=this.v=this.D=!1,this.B=0,this.A=null,this.J=Vn,this.K=this.L=!1}q(Fn,Vt);var Vn="",Un=/^https?$/i,qn=["POST","PUT"];function Bn(t){return"content-type"==t.toLowerCase()}function jn(t,e){t.h=!1,t.g&&(t.l=!0,t.g.abort(),t.l=!1),t.j=e,t.m=5,Kn(t),Qn(t)}function Kn(t){t.D||(t.D=!0,Ut(t,"complete"),Ut(t,"error"))}function Gn(t){if(t.h&&void 0!==C&&(!t.C[1]||4!=zn(t)||2!=t.ba()))if(t.v&&4==zn(t))ee(t.Fa,0,t);else if(Ut(t,"readystatechange"),4==zn(t)){t.h=!1;try{var e,n,r,i,o=t.ba();t:switch(o){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var s=!0;break t;default:s=!1}if((e=s)||((n=0===o)&&(!(i=String(t.H).match(ze)[1]||null)&&k.self&&k.self.location&&(i=(r=k.self.location.protocol).substr(0,r.length-1)),n=!Un.test(i?i.toLowerCase():"")),e=n),e)Ut(t,"complete"),Ut(t,"success");else{t.m=6;try{var a=2<zn(t)?t.g.statusText:""}catch(t){a=""}t.j=a+" ["+t.ba()+"]",Kn(t)}}finally{Qn(t)}}}function Qn(t,e){if(t.g){Hn(t);var n=t.g,r=t.C[0]?R:null;t.g=null,t.C=null,e||Ut(t,"ready");try{n.onreadystatechange=r}catch(t){}}}function Hn(t){t.g&&t.K&&(t.g.ontimeout=null),t.A&&(k.clearTimeout(t.A),t.A=null)}function zn(t){return t.g?t.g.readyState:0}function Wn(t){try{if(!t.g)return null;if("response"in t.g)return t.g.response;switch(t.J){case Vn:case"text":return t.g.responseText;case"arraybuffer":if("mozResponseArrayBuffer"in t.g)return t.g.mozResponseArrayBuffer}return null}catch(t){return null}}function Yn(t,e,n){t:{for(r in n){var r=!1;break t}r=!0}var i;r||(i="",Z(n,function(t,e){i+=e,i+=":",i+=t,i+="\r\n"}),n=i,"string"==typeof t?null!=n&&encodeURIComponent(String(n)):tn(t,e,n))}function Xn(t,e,n){return n&&n.internalChannelParams&&n.internalChannelParams[t]||e}function $n(t){this.za=0,this.l=[],this.h=new ce,this.la=this.oa=this.F=this.W=this.g=this.sa=this.D=this.aa=this.o=this.P=this.s=null,this.Za=this.V=0,this.Xa=Xn("failFast",!1,t),this.N=this.v=this.u=this.m=this.j=null,this.X=!0,this.I=this.ta=this.U=-1,this.Y=this.A=this.C=0,this.Pa=Xn("baseRetryDelayMs",5e3,t),this.$a=Xn("retryDelaySeedMs",1e4,t),this.Ya=Xn("forwardChannelMaxRetries",2,t),this.ra=Xn("forwardChannelRequestTimeoutMs",2e4,t),this.qa=t&&t.xmlHttpFactory||void 0,this.Ba=t&&t.Yb||!1,this.K=void 0,this.H=t&&t.supportsCrossDomainXhr||!1,this.J="",this.i=new vn(t&&t.concurrentRequestLimit),this.Ca=new Nn,this.ja=t&&t.fastHandshake||!1,this.Ra=t&&t.Wb||!1,t&&t.Aa&&this.h.Aa(),t&&t.forceLongPolling&&(this.X=!1),this.$=!this.ja&&this.X&&t&&t.detectBufferingProxy||!1,this.ka=void 0,this.O=0,this.L=!1,this.B=null,this.Wa=!t||!1!==t.Xb}function Jn(t){var e,n;tr(t),3==t.G&&(e=t.V++,tn(n=Ye(t.F),"SID",t.J),tn(n,"RID",e),tn(n,"TYPE","terminate"),ir(t,n),(e=new De(t,t.h,e,void 0)).K=2,e.v=en(Ye(n)),n=!1,!(n=k.navigator&&k.navigator.sendBeacon?k.navigator.sendBeacon(e.v.toString(),""):n)&&k.Image&&((new Image).src=e.v,n=!0),n||(e.g=gr(e.l,null),e.g.ea(e.v)),e.F=Date.now(),Fe(e)),pr(t)}function Zn(t){t.g&&(ur(t),t.g.cancel(),t.g=null)}function tr(t){Zn(t),t.u&&(k.clearTimeout(t.u),t.u=null),hr(t),t.i.cancel(),t.m&&("number"==typeof t.m&&k.clearTimeout(t.m),t.m=null)}function er(t,e){t.l.push(new mn(t.Za++,e)),3==t.G&&nr(t)}function nr(t){En(t.i)||t.m||(t.m=!0,Yt(t.Ha,t),t.C=0)}function rr(t,e){var n=e?e.m:t.V++,r=Ye(t.F);tn(r,"SID",t.J),tn(r,"RID",n),tn(r,"AID",t.U),ir(t,r),t.o&&t.s&&Yn(r,t.o,t.s),n=new De(t,t.h,n,t.C+1),null===t.o&&(n.H=t.s),e&&(t.l=e.D.concat(t.l)),e=or(t,n,1e3),n.setTimeout(Math.round(.5*t.ra)+Math.round(.5*t.ra*Math.random())),_n(t.i,n),Oe(n,r,e)}function ir(t,n){t.j&&Ke({},function(t,e){tn(n,e,t)})}function or(t,e,n){n=Math.min(t.l.length,n);var r=t.j?V(t.j.Oa,t.j,t):null;t:for(var i=t.l,o=-1;;){var s=["count="+n];-1==o?0<n?(o=i[0].h,s.push("ofs="+o)):o=0:s.push("ofs="+o);for(var a=!0,u=0;u<n;u++){var c=i[u].h,h=i[u].g;if((c-=o)<0)o=Math.max(0,i[u].h-100),a=!1;else try{!function(t,r,e){var i=e||"";try{Ke(t,function(t,e){var n=t;O(t)&&(n=Bt(t)),r.push(i+e+"="+encodeURIComponent(n))})}catch(t){throw r.push(i+"type="+encodeURIComponent("_badmap")),t}}(h,s,"req"+c+"_")}catch(t){r&&r(h)}}if(a){r=s.join("&");break t}}return t=t.l.splice(0,n),e.D=t,r}function sr(t){t.g||t.u||(t.Y=1,Yt(t.Ga,t),t.A=0)}function ar(t){return!(t.g||t.u||3<=t.A)&&(t.Y++,t.u=we(V(t.Ga,t),fr(t,t.A)),t.A++,1)}function ur(t){null!=t.B&&(k.clearTimeout(t.B),t.B=null)}function cr(t){t.g=new De(t,t.h,"rpc",t.Y),null===t.o&&(t.g.H=t.s),t.g.O=0;var e=Ye(t.oa);tn(e,"RID","rpc"),tn(e,"SID",t.J),tn(e,"CI",t.N?"0":"1"),tn(e,"AID",t.U),ir(t,e),tn(e,"TYPE","xmlhttp"),t.o&&t.s&&Yn(e,t.o,t.s),t.K&&t.g.setTimeout(t.K);var n=t.g;t=t.la,n.K=1,n.v=en(Ye(e)),n.s=null,n.U=!0,Le(n,t)}function hr(t){null!=t.v&&(k.clearTimeout(t.v),t.v=null)}function lr(t,e){var n,r,i,o=null;if(t.g==e){hr(t),ur(t),t.g=null;var s=2}else{if(!In(t.i,e))return;o=e.D,Sn(t.i,e),s=1}if(t.I=e.N,0!=t.G)if(e.i)1==s?(o=e.s?e.s.length:0,e=Date.now()-e.F,n=t.C,Ut(s=de(),new ve(s,o)),nr(t)):sr(t);else if(3==(n=e.o)||0==n&&0<t.I||(1!=s||(i=e,Tn((r=t).i)>=r.i.j-(r.m?1:0)||(r.m?(r.l=i.D.concat(r.l),0):1==r.G||2==r.G||r.C>=(r.Xa?0:r.Ya)||(r.m=we(V(r.Ha,r,i),fr(r,r.C)),r.C++,0))))&&(2!=s||!ar(t)))switch(o&&0<o.length&&(e=t.i,e.i=e.i.concat(o)),n){case 1:dr(t,5);break;case 4:dr(t,10);break;case 3:dr(t,6);break;default:dr(t,2)}}function fr(t,e){var n=t.Pa+Math.floor(Math.random()*t.$a);return t.j||(n*=2),n*e}function dr(t,e){var n,r,i,o;t.h.info("Error code "+e),2==e?(r=null,t.j&&(r=null),o=V(t.jb,t),r||(r=new We("//www.google.com/images/cleardot.gif"),k.location&&"http"==k.location.protocol||Xe(r,"https"),en(r)),n=r.toString(),r=o,o=new ce,k.Image?((i=new Image).onload=U(Cn,o,i,"TestLoadImage: loaded",!0,r),i.onerror=U(Cn,o,i,"TestLoadImage: error",!1,r),i.onabort=U(Cn,o,i,"TestLoadImage: abort",!1,r),i.ontimeout=U(Cn,o,i,"TestLoadImage: timeout",!1,r),k.setTimeout(function(){i.ontimeout&&i.ontimeout()},1e4),i.src=n):r(!1)):me(2),t.G=0,t.j&&t.j.va(e),pr(t),tr(t)}function pr(t){t.G=0,t.I=-1,t.j&&(0==An(t.i).length&&0==t.l.length||(t.i.i.length=0,H(t.l),t.l.length=0),t.j.ua())}function yr(t,e,n){var r,i,o,s,a,u=(s=n)instanceof We?Ye(s):new We(s,void 0);return""!=u.i?(e&&$e(u,e+"."+u.i),Je(u,u.m)):(a=k.location,r=a.protocol,i=e?e+"."+a.hostname:a.hostname,o=+a.port,s=n,a=new We(null,void 0),r&&Xe(a,r),i&&$e(a,i),o&&Je(a,o),s&&(a.l=s),u=a),t.aa&&Z(t.aa,function(t,e){tn(u,e,t)}),e=t.D,n=t.sa,e&&n&&tn(u,e,n),tn(u,"VER",t.ma),ir(t,u),u}function gr(t,e,n){if(e&&!t.H)throw Error("Can't create secondary domain capable XhrIo object.");return(e=n&&t.Ba&&!t.qa?new Fn(new kn({ib:!0})):new Fn(t.qa)).L=t.H,e}function mr(){}function vr(){if(st&&!(10<=Number(gt)))throw Error("Environmental error: no available transport.")}function wr(t,e){Vt.call(this),this.g=new $n(e),this.l=t,this.h=e&&e.messageUrlParams||null,t=e&&e.messageHeaders||null,e&&e.clientProtocolHeaderRequired&&(t?t["X-Client-Protocol"]="webchannel":t={"X-Client-Protocol":"webchannel"}),this.g.s=t,t=e&&e.initMessageHeaders||null,e&&e.messageContentType&&(t?t["X-WebChannel-Content-Type"]=e.messageContentType:t={"X-WebChannel-Content-Type":e.messageContentType}),e&&e.ya&&(t?t["X-WebChannel-Client-Profile"]=e.ya:t={"X-WebChannel-Client-Profile":e.ya}),this.g.P=t,(t=e&&e.httpHeadersOverwriteParam)&&!z(t)&&(this.g.o=t),this.A=e&&e.supportsCrossDomainXhr||!1,this.v=e&&e.sendRawJson||!1,(e=e&&e.httpSessionIdParam)&&!z(e)&&(this.g.D=e,null!==(t=this.h)&&e in t&&(e in(t=this.h)&&delete t[e])),this.j=new Tr(this)}function br(t){_e.call(this);var e=t.__sm__;if(e){t:{for(var n in e){t=n;break t}t=void 0}(this.i=t)&&(t=this.i,e=null!==e&&t in e?e[t]:void 0),this.data=e}else this.data=t}function Er(){Se.call(this),this.status=1}function Tr(t){this.g=t}(D=Fn.prototype).ea=function(t,e,n,r){if(this.g)throw Error("[goog.net.XhrIo] Object is active with another request="+this.H+"; newUri="+t);e=e?e.toUpperCase():"GET",this.H=t,this.j="",this.m=0,this.D=!1,this.h=!0,this.g=(this.u||Ce).g(),this.C=this.u?Te(this.u):Te(Ce),this.g.onreadystatechange=V(this.Fa,this);try{this.F=!0,this.g.open(e,String(t),!0),this.F=!1}catch(t){return void jn(this,t)}t=n||"";var i,o=new Ge(this.headers);r&&Ke(r,function(t,e){o.set(e,t)}),r=function(t){t:{for(var e=Bn,n=t.length,r="string"==typeof t?t.split(""):t,i=0;i<n;i++)if(i in r&&e.call(void 0,r[i],i,t)){e=i;break t}e=-1}return e<0?null:"string"==typeof t?t.charAt(e):t[e]}(o.T()),n=k.FormData&&t instanceof k.FormData,0<=K(qn,e)&&!r&&!n&&o.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8"),o.forEach(function(t,e){this.g.setRequestHeader(e,t)},this),this.J&&(this.g.responseType=this.J),"withCredentials"in this.g&&this.g.withCredentials!==this.L&&(this.g.withCredentials=this.L);try{Hn(this),0<this.B&&((this.K=(i=this.g,st&&yt()&&"number"==typeof i.timeout&&void 0!==i.ontimeout))?(this.g.timeout=this.B,this.g.ontimeout=V(this.pa,this)):this.A=ee(this.pa,this.B,this)),this.v=!0,this.g.send(t),this.v=!1}catch(t){jn(this,t)}},D.pa=function(){void 0!==C&&this.g&&(this.j="Timed out after "+this.B+"ms, aborting",this.m=8,Ut(this,"timeout"),this.abort(8))},D.abort=function(t){this.g&&this.h&&(this.h=!1,this.l=!0,this.g.abort(),this.l=!1,this.m=t||7,Ut(this,"complete"),Ut(this,"abort"),Qn(this))},D.M=function(){this.g&&(this.h&&(this.h=!1,this.l=!0,this.g.abort(),this.l=!1),Qn(this,!0)),Fn.Z.M.call(this)},D.Fa=function(){this.s||(this.F||this.v||this.l?Gn(this):this.cb())},D.cb=function(){Gn(this)},D.ba=function(){try{return 2<zn(this)?this.g.status:-1}catch(t){return-1}},D.ga=function(){try{return this.g?this.g.responseText:""}catch(t){return""}},D.Qa=function(t){if(this.g){var e=this.g.responseText;return t&&0==e.indexOf(t)&&(e=e.substring(t.length)),Mn(e)}},D.Da=function(){return this.m},D.La=function(){return"string"==typeof this.j?this.j:String(this.j)},(D=$n.prototype).ma=8,D.G=1,D.hb=function(t){try{this.h.info("Origin Trials invoked: "+t)}catch(t){}},D.Ha=function(t){if(this.m)if(this.m=null,1==this.G){if(!t){this.V=Math.floor(1e5*Math.random()),t=this.V++;var e=new De(this,this.h,t,void 0),n=this.s;if(this.P&&(n?nt(n=tt(n),this.P):n=this.P),null===this.o&&(e.H=n),this.ja)t:{for(var r=0,i=0;i<this.l.length;i++){var o=this.l[i];if("__data__"in o.g&&"string"==typeof(o=o.g.__data__)?o=o.length:o=void 0,void 0===o)break;if(4096<(r+=o)){r=i;break t}if(4096===r||i===this.l.length-1){r=i+1;break t}}r=1e3}else r=1e3;r=or(this,e,r),tn(i=Ye(this.F),"RID",t),tn(i,"CVER",22),this.D&&tn(i,"X-HTTP-Session-Id",this.D),ir(this,i),this.o&&n&&Yn(i,this.o,n),_n(this.i,e),this.Ra&&tn(i,"TYPE","init"),this.ja?(tn(i,"$req",r),tn(i,"SID","null"),e.$=!0,Oe(e,i,null)):Oe(e,i,r),this.G=2}}else 3==this.G&&(t?rr(this,t):0==this.l.length||En(this.i)||rr(this))},D.Ga=function(){var t;this.u=null,cr(this),this.$&&!(this.L||null==this.g||this.O<=0)&&(t=2*this.O,this.h.info("BP detection timer enabled: "+t),this.B=we(V(this.bb,this),t))},D.bb=function(){this.B&&(this.B=null,this.h.info("BP detection timeout reached."),this.h.info("Buffering proxy detected and switch to long-polling!"),this.N=!1,this.L=!0,me(10),Zn(this),cr(this))},D.ab=function(){null!=this.v&&(this.v=null,Zn(this),ar(this),me(19))},D.jb=function(t){t?(this.h.info("Successfully pinged google.com"),me(2)):(this.h.info("Failed to ping google.com"),me(1))},(D=mr.prototype).xa=function(){},D.wa=function(){},D.va=function(){},D.ua=function(){},D.Oa=function(){},vr.prototype.g=function(t,e){return new wr(t,e)},q(wr,Vt),wr.prototype.m=function(){this.g.j=this.j,this.A&&(this.g.H=!0);var t=this.g,e=this.l,n=this.h||void 0;t.Wa&&(t.h.info("Origin Trials enabled."),Yt(V(t.hb,t,e))),me(0),t.W=e,t.aa=n||{},t.N=t.X,t.F=yr(t,null,t.W),nr(t)},wr.prototype.close=function(){Jn(this.g)},wr.prototype.u=function(t){var e;"string"==typeof t?((e={}).__data__=t,er(this.g,e)):this.v?((e={}).__data__=Bt(t),er(this.g,e)):er(this.g,t)},wr.prototype.M=function(){this.g.j=null,delete this.j,Jn(this.g),delete this.g,wr.Z.M.call(this)},q(br,_e),q(Er,Se),q(Tr,mr),Tr.prototype.xa=function(){Ut(this.g,"a")},Tr.prototype.wa=function(t){Ut(this.g,new br(t))},Tr.prototype.va=function(t){Ut(this.g,new Er)},Tr.prototype.ua=function(){Ut(this.g,"b")},vr.prototype.createWebChannel=vr.prototype.g,wr.prototype.send=wr.prototype.u,wr.prototype.open=wr.prototype.m,be.NO_ERROR=0,be.TIMEOUT=8,be.HTTP_ERROR=6,N.COMPLETE="complete",(Ie.EventType=jt).OPEN="a",jt.CLOSE="b",jt.ERROR="c",jt.MESSAGE="d",Vt.prototype.listen=Vt.prototype.N,Fn.prototype.listenOnce=Fn.prototype.O,Fn.prototype.getLastError=Fn.prototype.La,Fn.prototype.getLastErrorCode=Fn.prototype.Da,Fn.prototype.getStatus=Fn.prototype.ba,Fn.prototype.getResponseJson=Fn.prototype.Qa,Fn.prototype.getResponseText=Fn.prototype.ga,Fn.prototype.send=Fn.prototype.ea;var Ir=de,_r=be,Sr=N,Ar=le,Dr=10,Nr=11,Cr=kn,kr=Ie,Rr=Fn,xr=(Or.prototype.t=function(t){return this.previousValue=Math.max(t,this.previousValue),this.previousValue},Or.prototype.next=function(){var t=++this.previousValue;return this.i&&this.i(t),t},Or);function Or(t,e){var n=this;this.previousValue=t,e&&(e.sequenceNumberHandler=function(t){return n.t(t)},this.i=function(t){return e.writeSequenceNumber(t)})}xr.o=-1;var Lr,Pr={OK:"ok",CANCELLED:"cancelled",UNKNOWN:"unknown",INVALID_ARGUMENT:"invalid-argument",DEADLINE_EXCEEDED:"deadline-exceeded",NOT_FOUND:"not-found",ALREADY_EXISTS:"already-exists",PERMISSION_DENIED:"permission-denied",UNAUTHENTICATED:"unauthenticated",RESOURCE_EXHAUSTED:"resource-exhausted",FAILED_PRECONDITION:"failed-precondition",ABORTED:"aborted",OUT_OF_RANGE:"out-of-range",UNIMPLEMENTED:"unimplemented",INTERNAL:"internal",UNAVAILABLE:"unavailable",DATA_LOSS:"data-loss"},Mr=(n(Vr,Lr=Error),Vr),Fr=new I("@firebase/firestore");function Vr(t,e){var n=this;return(n=Lr.call(this,e)||this).code=t,n.message=e,n.name="FirebaseError",n.toString=function(){return n.name+": [code="+n.code+"]: "+n.message},n}function Ur(){return Fr.logLevel}function qr(t){for(var e,n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];Fr.logLevel<=p.DEBUG&&(e=n.map(Kr),Fr.debug.apply(Fr,s(["Firestore (8.6.8): "+t],e)))}function Br(t){for(var e,n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];Fr.logLevel<=p.ERROR&&(e=n.map(Kr),Fr.error.apply(Fr,s(["Firestore (8.6.8): "+t],e)))}function jr(t){for(var e,n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];Fr.logLevel<=p.WARN&&(e=n.map(Kr),Fr.warn.apply(Fr,s(["Firestore (8.6.8): "+t],e)))}function Kr(e){if("string"==typeof e)return e;try{return JSON.stringify(e)}catch(t){return e}}function Gr(t){t="FIRESTORE (8.6.8) INTERNAL ASSERTION FAILED: "+(t=void 0===t?"Unexpected state":t);throw Br(t),new Error(t)}function Qr(t){t||Gr()}var Hr=(zr.u=function(){for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",e=Math.floor(256/t.length)*t.length,n="";n.length<20;)for(var r=function(t){var e="undefined"!=typeof self&&(self.crypto||self.msCrypto),n=new Uint8Array(t);if(e&&"function"==typeof e.getRandomValues)e.getRandomValues(n);else for(var r=0;r<t;r++)n[r]=Math.floor(256*Math.random());return n}(40),i=0;i<r.length;++i)n.length<20&&r[i]<e&&(n+=t.charAt(r[i]%t.length));return n},zr);function zr(){}function Wr(t,e){return t<e?-1:e<t?1:0}function Yr(t,n,r){return t.length===n.length&&t.every(function(t,e){return r(t,n[e])})}function Xr(t){return t+"\0"}var $r=(ti.now=function(){return ti.fromMillis(Date.now())},ti.fromDate=function(t){return ti.fromMillis(t.getTime())},ti.fromMillis=function(t){var e=Math.floor(t/1e3);return new ti(e,Math.floor(1e6*(t-1e3*e)))},ti.prototype.toDate=function(){return new Date(this.toMillis())},ti.prototype.toMillis=function(){return 1e3*this.seconds+this.nanoseconds/1e6},ti.prototype._compareTo=function(t){return this.seconds===t.seconds?Wr(this.nanoseconds,t.nanoseconds):Wr(this.seconds,t.seconds)},ti.prototype.isEqual=function(t){return t.seconds===this.seconds&&t.nanoseconds===this.nanoseconds},ti.prototype.toString=function(){return"Timestamp(seconds="+this.seconds+", nanoseconds="+this.nanoseconds+")"},ti.prototype.toJSON=function(){return{seconds:this.seconds,nanoseconds:this.nanoseconds}},ti.prototype.valueOf=function(){var t=this.seconds- -62135596800;return String(t).padStart(12,"0")+"."+String(this.nanoseconds).padStart(9,"0")},ti),Jr=(Zr.fromTimestamp=function(t){return new Zr(t)},Zr.min=function(){return new Zr(new $r(0,0))},Zr.prototype.compareTo=function(t){return this.timestamp._compareTo(t.timestamp)},Zr.prototype.isEqual=function(t){return this.timestamp.isEqual(t.timestamp)},Zr.prototype.toMicroseconds=function(){return 1e6*this.timestamp.seconds+this.timestamp.nanoseconds/1e3},Zr.prototype.toString=function(){return"SnapshotVersion("+this.timestamp.toString()+")"},Zr.prototype.toTimestamp=function(){return this.timestamp},Zr);function Zr(t){this.timestamp=t}function ti(t,e){if(this.seconds=t,(this.nanoseconds=e)<0)throw new Mr(Pr.INVALID_ARGUMENT,"Timestamp nanoseconds out of range: "+e);if(1e9<=e)throw new Mr(Pr.INVALID_ARGUMENT,"Timestamp nanoseconds out of range: "+e);if(t<-62135596800)throw new Mr(Pr.INVALID_ARGUMENT,"Timestamp seconds out of range: "+t);if(253402300800<=t)throw new Mr(Pr.INVALID_ARGUMENT,"Timestamp seconds out of range: "+t)}function ei(t){var e,n=0;for(e in t)Object.prototype.hasOwnProperty.call(t,e)&&n++;return n}function ni(t,e){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e(n,t[n])}function ri(t){for(var e in t)if(Object.prototype.hasOwnProperty.call(t,e))return!1;return!0}var ii,oi,N=(Object.defineProperty(yi.prototype,"length",{get:function(){return this.len},enumerable:!1,configurable:!0}),yi.prototype.isEqual=function(t){return 0===yi.comparator(this,t)},yi.prototype.child=function(t){var e=this.segments.slice(this.offset,this.limit());return t instanceof yi?t.forEach(function(t){e.push(t)}):e.push(t),this.construct(e)},yi.prototype.limit=function(){return this.offset+this.length},yi.prototype.popFirst=function(t){return this.construct(this.segments,this.offset+(t=void 0===t?1:t),this.length-t)},yi.prototype.popLast=function(){return this.construct(this.segments,this.offset,this.length-1)},yi.prototype.firstSegment=function(){return this.segments[this.offset]},yi.prototype.lastSegment=function(){return this.get(this.length-1)},yi.prototype.get=function(t){return this.segments[this.offset+t]},yi.prototype.isEmpty=function(){return 0===this.length},yi.prototype.isPrefixOf=function(t){if(t.length<this.length)return!1;for(var e=0;e<this.length;e++)if(this.get(e)!==t.get(e))return!1;return!0},yi.prototype.isImmediateParentOf=function(t){if(this.length+1!==t.length)return!1;for(var e=0;e<this.length;e++)if(this.get(e)!==t.get(e))return!1;return!0},yi.prototype.forEach=function(t){for(var e=this.offset,n=this.limit();e<n;e++)t(this.segments[e])},yi.prototype.toArray=function(){return this.segments.slice(this.offset,this.limit())},yi.comparator=function(t,e){for(var n=Math.min(t.length,e.length),r=0;r<n;r++){var i=t.get(r),o=e.get(r);if(i<o)return-1;if(o<i)return 1}return t.length<e.length?-1:t.length>e.length?1:0},yi),si=(n(pi,oi=N),pi.prototype.construct=function(t,e,n){return new pi(t,e,n)},pi.prototype.canonicalString=function(){return this.toArray().join("/")},pi.prototype.toString=function(){return this.canonicalString()},pi.fromString=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=[],r=0,i=t;r<i.length;r++){var o=i[r];if(0<=o.indexOf("//"))throw new Mr(Pr.INVALID_ARGUMENT,"Invalid segment ("+o+"). Paths must not contain // in them.");n.push.apply(n,o.split("/").filter(function(t){return 0<t.length}))}return new pi(n)},pi.emptyPath=function(){return new pi([])},pi),ai=/^[_a-zA-Z][_a-zA-Z0-9]*$/,ui=(n(di,ii=N),di.prototype.construct=function(t,e,n){return new di(t,e,n)},di.isValidIdentifier=function(t){return ai.test(t)},di.prototype.canonicalString=function(){return this.toArray().map(function(t){return t=t.replace(/\\/g,"\\\\").replace(/`/g,"\\`"),t=!di.isValidIdentifier(t)?"`"+t+"`":t}).join(".")},di.prototype.toString=function(){return this.canonicalString()},di.prototype.isKeyField=function(){return 1===this.length&&"__name__"===this.get(0)},di.keyField=function(){return new di(["__name__"])},di.fromServerFormat=function(t){for(var e=[],n="",r=0,i=function(){if(0===n.length)throw new Mr(Pr.INVALID_ARGUMENT,"Invalid field path ("+t+"). Paths must not be empty, begin with '.', end with '.', or contain '..'");e.push(n),n=""},o=!1;r<t.length;){var s=t[r];if("\\"===s){if(r+1===t.length)throw new Mr(Pr.INVALID_ARGUMENT,"Path has trailing escape character: "+t);var a=t[r+1];if("\\"!==a&&"."!==a&&"`"!==a)throw new Mr(Pr.INVALID_ARGUMENT,"Path has invalid escape sequence: "+t);n+=a,r+=2}else"`"===s?o=!o:"."!==s||o?n+=s:i(),r++}if(i(),o)throw new Mr(Pr.INVALID_ARGUMENT,"Unterminated ` in path: "+t);return new di(e)},di.emptyPath=function(){return new di([])},di),ci=(fi.prototype.covers=function(t){for(var e=0,n=this.fields;e<n.length;e++)if(n[e].isPrefixOf(t))return!0;return!1},fi.prototype.isEqual=function(t){return Yr(this.fields,t.fields,function(t,e){return t.isEqual(e)})},fi),hi=(li.fromBase64String=function(t){return new li(atob(t))},li.fromUint8Array=function(t){return new li(function(t){for(var e="",n=0;n<t.length;++n)e+=String.fromCharCode(t[n]);return e}(t))},li.prototype.toBase64=function(){return t=this.binaryString,btoa(t);var t},li.prototype.toUint8Array=function(){return function(t){for(var e=new Uint8Array(t.length),n=0;n<t.length;n++)e[n]=t.charCodeAt(n);return e}(this.binaryString)},li.prototype.approximateByteSize=function(){return 2*this.binaryString.length},li.prototype.compareTo=function(t){return Wr(this.binaryString,t.binaryString)},li.prototype.isEqual=function(t){return this.binaryString===t.binaryString},li);function li(t){this.binaryString=t}function fi(t){(this.fields=t).sort(ui.comparator)}function di(){return null!==ii&&ii.apply(this,arguments)||this}function pi(){return null!==oi&&oi.apply(this,arguments)||this}function yi(t,e,n){void 0===e?e=0:e>t.length&&Gr(),void 0===n?n=t.length-e:n>t.length-e&&Gr(),this.segments=t,this.offset=e,this.len=n}hi.EMPTY_BYTE_STRING=new hi("");var gi=new RegExp(/^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(?:\.(\d+))?Z$/);function mi(t){if(Qr(!!t),"string"!=typeof t)return{seconds:vi(t.seconds),nanos:vi(t.nanos)};var e=0,n=gi.exec(t);Qr(!!n),n[1]&&(n=((n=n[1])+"000000000").substr(0,9),e=Number(n));t=new Date(t);return{seconds:Math.floor(t.getTime()/1e3),nanos:e}}function vi(t){return"number"==typeof t?t:"string"==typeof t?Number(t):0}function wi(t){return"string"==typeof t?hi.fromBase64String(t):hi.fromUint8Array(t)}function bi(t){return"server_timestamp"===(null===(t=((null===(t=null==t?void 0:t.mapValue)||void 0===t?void 0:t.fields)||{}).__type__)||void 0===t?void 0:t.stringValue)}function Ei(t){t=mi(t.mapValue.fields.__local_write_time__.timestampValue);return new $r(t.seconds,t.nanos)}function Ti(t){return null==t}function Ii(t){return 0===t&&1/t==-1/0}function _i(t){return"number"==typeof t&&Number.isInteger(t)&&!Ii(t)&&t<=Number.MAX_SAFE_INTEGER&&t>=Number.MIN_SAFE_INTEGER}var Si=(Ai.fromPath=function(t){return new Ai(si.fromString(t))},Ai.fromName=function(t){return new Ai(si.fromString(t).popFirst(5))},Ai.prototype.hasCollectionId=function(t){return 2<=this.path.length&&this.path.get(this.path.length-2)===t},Ai.prototype.isEqual=function(t){return null!==t&&0===si.comparator(this.path,t.path)},Ai.prototype.toString=function(){return this.path.toString()},Ai.comparator=function(t,e){return si.comparator(t.path,e.path)},Ai.isDocumentKey=function(t){return t.length%2==0},Ai.fromSegments=function(t){return new Ai(new si(t.slice()))},Ai);function Ai(t){this.path=t}function Di(t){return"nullValue"in t?0:"booleanValue"in t?1:"integerValue"in t||"doubleValue"in t?2:"timestampValue"in t?3:"stringValue"in t?5:"bytesValue"in t?6:"referenceValue"in t?7:"geoPointValue"in t?8:"arrayValue"in t?9:"mapValue"in t?bi(t)?4:10:Gr()}function Ni(r,i){var t,e,n=Di(r);if(n!==Di(i))return!1;switch(n){case 0:return!0;case 1:return r.booleanValue===i.booleanValue;case 4:return Ei(r).isEqual(Ei(i));case 3:return function(t){if("string"==typeof r.timestampValue&&"string"==typeof t.timestampValue&&r.timestampValue.length===t.timestampValue.length)return r.timestampValue===t.timestampValue;var e=mi(r.timestampValue),t=mi(t.timestampValue);return e.seconds===t.seconds&&e.nanos===t.nanos}(i);case 5:return r.stringValue===i.stringValue;case 6:return e=i,wi(r.bytesValue).isEqual(wi(e.bytesValue));case 7:return r.referenceValue===i.referenceValue;case 8:return t=i,vi((e=r).geoPointValue.latitude)===vi(t.geoPointValue.latitude)&&vi(e.geoPointValue.longitude)===vi(t.geoPointValue.longitude);case 2:return function(t,e){if("integerValue"in t&&"integerValue"in e)return vi(t.integerValue)===vi(e.integerValue);if("doubleValue"in t&&"doubleValue"in e){t=vi(t.doubleValue),e=vi(e.doubleValue);return t===e?Ii(t)===Ii(e):isNaN(t)&&isNaN(e)}return!1}(r,i);case 9:return Yr(r.arrayValue.values||[],i.arrayValue.values||[],Ni);case 10:return function(){var t,e=r.mapValue.fields||{},n=i.mapValue.fields||{};if(ei(e)!==ei(n))return!1;for(t in e)if(e.hasOwnProperty(t)&&(void 0===n[t]||!Ni(e[t],n[t])))return!1;return!0}();default:return Gr()}}function Ci(t,e){return void 0!==(t.values||[]).find(function(t){return Ni(t,e)})}function ki(t,e){var n,r,i,o=Di(t),s=Di(e);if(o!==s)return Wr(o,s);switch(o){case 0:return 0;case 1:return Wr(t.booleanValue,e.booleanValue);case 2:return r=e,i=vi(t.integerValue||t.doubleValue),r=vi(r.integerValue||r.doubleValue),i<r?-1:r<i?1:i===r?0:isNaN(i)?isNaN(r)?0:-1:1;case 3:return Ri(t.timestampValue,e.timestampValue);case 4:return Ri(Ei(t),Ei(e));case 5:return Wr(t.stringValue,e.stringValue);case 6:return function(t,e){t=wi(t),e=wi(e);return t.compareTo(e)}(t.bytesValue,e.bytesValue);case 7:return function(t,e){for(var n=t.split("/"),r=e.split("/"),i=0;i<n.length&&i<r.length;i++){var o=Wr(n[i],r[i]);if(0!==o)return o}return Wr(n.length,r.length)}(t.referenceValue,e.referenceValue);case 8:return n=t.geoPointValue,i=e.geoPointValue,0!==(r=Wr(vi(n.latitude),vi(i.latitude)))?r:Wr(vi(n.longitude),vi(i.longitude));case 9:return function(t,e){for(var n=t.values||[],r=e.values||[],i=0;i<n.length&&i<r.length;++i){var o=ki(n[i],r[i]);if(o)return o}return Wr(n.length,r.length)}(t.arrayValue,e.arrayValue);case 10:return function(t,e){var n=t.fields||{},r=Object.keys(n),i=e.fields||{},o=Object.keys(i);r.sort(),o.sort();for(var s=0;s<r.length&&s<o.length;++s){var a=Wr(r[s],o[s]);if(0!==a)return a;a=ki(n[r[s]],i[o[s]]);if(0!==a)return a}return Wr(r.length,o.length)}(t.mapValue,e.mapValue);default:throw Gr()}}function Ri(t,e){if("string"==typeof t&&"string"==typeof e&&t.length===e.length)return Wr(t,e);var n=mi(t),t=mi(e),e=Wr(n.seconds,t.seconds);return 0!==e?e:Wr(n.nanos,t.nanos)}function xi(t){return function s(t){return"nullValue"in t?"null":"booleanValue"in t?""+t.booleanValue:"integerValue"in t?""+t.integerValue:"doubleValue"in t?""+t.doubleValue:"timestampValue"in t?function(t){t=mi(t);return"time("+t.seconds+","+t.nanos+")"}(t.timestampValue):"stringValue"in t?t.stringValue:"bytesValue"in t?wi(t.bytesValue).toBase64():"referenceValue"in t?(e=t.referenceValue,Si.fromName(e).toString()):"geoPointValue"in t?"geo("+(e=t.geoPointValue).latitude+","+e.longitude+")":"arrayValue"in t?function(t){for(var e="[",n=!0,r=0,i=t.values||[];r<i.length;r++)n?n=!1:e+=",",e+=s(i[r]);return e+"]"}(t.arrayValue):"mapValue"in t?function(t){for(var e="{",n=!0,r=0,i=Object.keys(t.fields||{}).sort();r<i.length;r++){var o=i[r];n?n=!1:e+=",",e+=o+":"+s(t.fields[o])}return e+"}"}(t.mapValue):Gr();var e}(t)}function Oi(t,e){return{referenceValue:"projects/"+t.projectId+"/databases/"+t.database+"/documents/"+e.path.canonicalString()}}function Li(t){return t&&"integerValue"in t}function Pi(t){return!!t&&"arrayValue"in t}function Mi(t){return t&&"nullValue"in t}function Fi(t){return t&&"doubleValue"in t&&isNaN(Number(t.doubleValue))}function Vi(t){return t&&"mapValue"in t}function Ui(t){if(t.geoPointValue)return{geoPointValue:Object.assign({},t.geoPointValue)};if(t.timestampValue)return{timestampValue:Object.assign({},mi(t.timestampValue))};if(t.mapValue){var n={mapValue:{fields:{}}};return ni(t.mapValue.fields,function(t,e){return n.mapValue.fields[t]=Ui(e)}),n}if(t.arrayValue){for(var e={arrayValue:{values:[]}},r=0;r<(t.arrayValue.values||[]).length;++r)e.arrayValue.values[r]=Ui(t.arrayValue.values[r]);return e}return Object.assign({},t)}var qi=(Bi.empty=function(){return new Bi({mapValue:{}})},Bi.prototype.field=function(t){if(t.isEmpty())return this.value;for(var e=this.value,n=0;n<t.length-1;++n)if(!Vi(e=(e.mapValue.fields||{})[t.get(n)]))return null;return(e=(e.mapValue.fields||{})[t.lastSegment()])||null},Bi.prototype.set=function(t,e){this.getFieldsMap(t.popLast())[t.lastSegment()]=Ui(e)},Bi.prototype.setAll=function(t){var r=this,i=ui.emptyPath(),o={},s=[];t.forEach(function(t,e){var n;i.isImmediateParentOf(e)||(n=r.getFieldsMap(i),r.applyChanges(n,o,s),o={},s=[],i=e.popLast()),t?o[e.lastSegment()]=Ui(t):s.push(e.lastSegment())});t=this.getFieldsMap(i);this.applyChanges(t,o,s)},Bi.prototype.delete=function(t){var e=this.field(t.popLast());Vi(e)&&e.mapValue.fields&&delete e.mapValue.fields[t.lastSegment()]},Bi.prototype.isEqual=function(t){return Ni(this.value,t.value)},Bi.prototype.getFieldsMap=function(t){var e=this.value;e.mapValue.fields||(e.mapValue={fields:{}});for(var n=0;n<t.length;++n){var r=e.mapValue.fields[t.get(n)];Vi(r)&&r.mapValue.fields||(r={mapValue:{fields:{}}},e.mapValue.fields[t.get(n)]=r),e=r}return e.mapValue.fields},Bi.prototype.applyChanges=function(n,t,e){ni(t,function(t,e){return n[t]=e});for(var r=0,i=e;r<i.length;r++){var o=i[r];delete n[o]}},Bi.prototype.clone=function(){return new Bi(Ui(this.value))},Bi);function Bi(t){this.value=t}var ji=(Gi.newInvalidDocument=function(t){return new Gi(t,0,Jr.min(),qi.empty(),0)},Gi.newFoundDocument=function(t,e,n){return new Gi(t,1,e,n,0)},Gi.newNoDocument=function(t,e){return new Gi(t,2,e,qi.empty(),0)},Gi.newUnknownDocument=function(t,e){return new Gi(t,3,e,qi.empty(),2)},Gi.prototype.convertToFoundDocument=function(t,e){return this.version=t,this.documentType=1,this.data=e,this.documentState=0,this},Gi.prototype.convertToNoDocument=function(t){return this.version=t,this.documentType=2,this.data=qi.empty(),this.documentState=0,this},Gi.prototype.convertToUnknownDocument=function(t){return this.version=t,this.documentType=3,this.data=qi.empty(),this.documentState=2,this},Gi.prototype.setHasCommittedMutations=function(){return this.documentState=2,this},Gi.prototype.setHasLocalMutations=function(){return this.documentState=1,this},Object.defineProperty(Gi.prototype,"hasLocalMutations",{get:function(){return 1===this.documentState},enumerable:!1,configurable:!0}),Object.defineProperty(Gi.prototype,"hasCommittedMutations",{get:function(){return 2===this.documentState},enumerable:!1,configurable:!0}),Object.defineProperty(Gi.prototype,"hasPendingWrites",{get:function(){return this.hasLocalMutations||this.hasCommittedMutations},enumerable:!1,configurable:!0}),Gi.prototype.isValidDocument=function(){return 0!==this.documentType},Gi.prototype.isFoundDocument=function(){return 1===this.documentType},Gi.prototype.isNoDocument=function(){return 2===this.documentType},Gi.prototype.isUnknownDocument=function(){return 3===this.documentType},Gi.prototype.isEqual=function(t){return t instanceof Gi&&this.key.isEqual(t.key)&&this.version.isEqual(t.version)&&this.documentType===t.documentType&&this.documentState===t.documentState&&this.data.isEqual(t.data)},Gi.prototype.clone=function(){return new Gi(this.key,this.documentType,this.version,this.data.clone(),this.documentState)},Gi.prototype.toString=function(){return"Document("+this.key+", "+this.version+", "+JSON.stringify(this.data.value)+", {documentType: "+this.documentType+"}), {documentState: "+this.documentState+"})"},Gi),Ki=function(t,e,n,r,i,o,s){void 0===e&&(e=null),void 0===n&&(n=[]),void 0===r&&(r=[]),void 0===i&&(i=null),void 0===o&&(o=null),void 0===s&&(s=null),this.path=t,this.collectionGroup=e,this.orderBy=n,this.filters=r,this.limit=i,this.startAt=o,this.endAt=s,this.h=null};function Gi(t,e,n,r,i){this.key=t,this.documentType=e,this.version=n,this.data=r,this.documentState=i}function Qi(t,e,n,r,i,o,s){return new Ki(t,e=void 0===e?null:e,n=void 0===n?[]:n,r=void 0===r?[]:r,i=void 0===i?null:i,o=void 0===o?null:o,s=void 0===s?null:s)}function Hi(t){var e=t;return null===e.h&&(t=e.path.canonicalString(),null!==e.collectionGroup&&(t+="|cg:"+e.collectionGroup),t+="|f:",t+=e.filters.map(function(t){return(t=t).field.canonicalString()+t.op.toString()+xi(t.value)}).join(","),t+="|ob:",t+=e.orderBy.map(function(t){return(t=t).field.canonicalString()+t.dir}).join(","),Ti(e.limit)||(t+="|l:",t+=e.limit),e.startAt&&(t+="|lb:",t+=To(e.startAt)),e.endAt&&(t+="|ub:",t+=To(e.endAt)),e.h=t),e.h}function zi(t,e){if(t.limit!==e.limit)return!1;if(t.orderBy.length!==e.orderBy.length)return!1;for(var n,r,i=0;i<t.orderBy.length;i++)if(n=t.orderBy[i],r=e.orderBy[i],n.dir!==r.dir||!n.field.isEqual(r.field))return!1;if(t.filters.length!==e.filters.length)return!1;for(var o,s,a=0;a<t.filters.length;a++)if(o=t.filters[a],s=e.filters[a],o.op!==s.op||!o.field.isEqual(s.field)||!Ni(o.value,s.value))return!1;return t.collectionGroup===e.collectionGroup&&!!t.path.isEqual(e.path)&&!!So(t.startAt,e.startAt)&&So(t.endAt,e.endAt)}function Wi(t){return Si.isDocumentKey(t.path)&&null===t.collectionGroup&&0===t.filters.length}var Yi,Xi=(n($i,Yi=function(){}),$i.create=function(t,e,n){return t.isKeyField()?"in"===e||"not-in"===e?this.l(t,e,n):new eo(t,e,n):"array-contains"===e?new fo(t,n):"in"===e?new po(t,n):"not-in"===e?new yo(t,n):"array-contains-any"===e?new go(t,n):new $i(t,e,n)},$i.l=function(t,e,n){return new("in"===e?no:ro)(t,n)},$i.prototype.matches=function(t){t=t.data.field(this.field);return"!="===this.op?null!==t&&this.m(ki(t,this.value)):null!==t&&Di(this.value)===Di(t)&&this.m(ki(t,this.value))},$i.prototype.m=function(t){switch(this.op){case"<":return t<0;case"<=":return t<=0;case"==":return 0===t;case"!=":return 0!==t;case">":return 0<t;case">=":return 0<=t;default:return Gr()}},$i.prototype.g=function(){return 0<=["<","<=",">",">=","!=","not-in"].indexOf(this.op)},$i);function $i(t,e,n){var r=this;return(r=Yi.call(this)||this).field=t,r.op=e,r.value=n,r}var Ji,Zi,to,eo=(n(so,to=Xi),so.prototype.matches=function(t){t=Si.comparator(t.key,this.key);return this.m(t)},so),no=(n(oo,Zi=Xi),oo.prototype.matches=function(e){return this.keys.some(function(t){return t.isEqual(e.key)})},oo),ro=(n(io,Ji=Xi),io.prototype.matches=function(e){return!this.keys.some(function(t){return t.isEqual(e.key)})},io);function io(t,e){var n=this;return(n=Ji.call(this,t,"not-in",e)||this).keys=ao(0,e),n}function oo(t,e){var n=this;return(n=Zi.call(this,t,"in",e)||this).keys=ao(0,e),n}function so(t,e,n){var r=this;return(r=to.call(this,t,e,n)||this).key=Si.fromName(n.referenceValue),r}function ao(t,e){return((null===(e=e.arrayValue)||void 0===e?void 0:e.values)||[]).map(function(t){return Si.fromName(t.referenceValue)})}var uo,co,ho,lo,fo=(n(Eo,lo=Xi),Eo.prototype.matches=function(t){t=t.data.field(this.field);return Pi(t)&&Ci(t.arrayValue,this.value)},Eo),po=(n(bo,ho=Xi),bo.prototype.matches=function(t){t=t.data.field(this.field);return null!==t&&Ci(this.value.arrayValue,t)},bo),yo=(n(wo,co=Xi),wo.prototype.matches=function(t){if(Ci(this.value.arrayValue,{nullValue:"NULL_VALUE"}))return!1;t=t.data.field(this.field);return null!==t&&!Ci(this.value.arrayValue,t)},wo),go=(n(vo,uo=Xi),vo.prototype.matches=function(t){var e=this,t=t.data.field(this.field);return!(!Pi(t)||!t.arrayValue.values)&&t.arrayValue.values.some(function(t){return Ci(e.value.arrayValue,t)})},vo),mo=function(t,e){this.position=t,this.before=e};function vo(t,e){return uo.call(this,t,"array-contains-any",e)||this}function wo(t,e){return co.call(this,t,"not-in",e)||this}function bo(t,e){return ho.call(this,t,"in",e)||this}function Eo(t,e){return lo.call(this,t,"array-contains",e)||this}function To(t){return(t.before?"b":"a")+":"+t.position.map(xi).join(",")}var Io=function(t,e){void 0===e&&(e="asc"),this.field=t,this.dir=e};function _o(t,e,n){for(var r=0,i=0;i<t.position.length;i++){var o=e[i],s=t.position[i],r=o.field.isKeyField()?Si.comparator(Si.fromName(s.referenceValue),n.key):ki(s,n.data.field(o.field));if("desc"===o.dir&&(r*=-1),0!==r)break}return t.before?r<=0:r<0}function So(t,e){if(null===t)return null===e;if(null===e)return!1;if(t.before!==e.before||t.position.length!==e.position.length)return!1;for(var n=0;n<t.position.length;n++)if(!Ni(t.position[n],e.position[n]))return!1;return!0}var Ao=function(t,e,n,r,i,o,s,a){void 0===e&&(e=null),void 0===n&&(n=[]),void 0===r&&(r=[]),void 0===i&&(i=null),void 0===o&&(o="F"),void 0===s&&(s=null),void 0===a&&(a=null),this.path=t,this.collectionGroup=e,this.explicitOrderBy=n,this.filters=r,this.limit=i,this.limitType=o,this.startAt=s,this.endAt=a,this.p=null,this.T=null,this.startAt,this.endAt};function Do(t,e,n,r,i,o,s,a){return new Ao(t,e,n,r,i,o,s,a)}function No(t){return new Ao(t)}function Co(t){return!Ti(t.limit)&&"F"===t.limitType}function ko(t){return!Ti(t.limit)&&"L"===t.limitType}function Ro(t){return 0<t.explicitOrderBy.length?t.explicitOrderBy[0].field:null}function xo(t){for(var e=0,n=t.filters;e<n.length;e++){var r=n[e];if(r.g())return r.field}return null}function Oo(t){return null!==t.collectionGroup}function Lo(t){var e=t;if(null===e.p){e.p=[];var n=xo(e),t=Ro(e);if(null!==n&&null===t)n.isKeyField()||e.p.push(new Io(n)),e.p.push(new Io(ui.keyField(),"asc"));else{for(var r=!1,i=0,o=e.explicitOrderBy;i<o.length;i++){var s=o[i];e.p.push(s),s.field.isKeyField()&&(r=!0)}r||(n=0<e.explicitOrderBy.length?e.explicitOrderBy[e.explicitOrderBy.length-1].dir:"asc",e.p.push(new Io(ui.keyField(),n)))}}return e.p}function Po(t){var e=t;if(!e.T)if("F"===e.limitType)e.T=Qi(e.path,e.collectionGroup,Lo(e),e.filters,e.limit,e.startAt,e.endAt);else{for(var n=[],r=0,i=Lo(e);r<i.length;r++){var o=i[r],s="desc"===o.dir?"asc":"desc";n.push(new Io(o.field,s))}var a=e.endAt?new mo(e.endAt.position,!e.endAt.before):null,t=e.startAt?new mo(e.startAt.position,!e.startAt.before):null;e.T=Qi(e.path,e.collectionGroup,n,e.filters,e.limit,a,t)}return e.T}function Mo(t,e,n){return new Ao(t.path,t.collectionGroup,t.explicitOrderBy.slice(),t.filters.slice(),e,n,t.startAt,t.endAt)}function Fo(t,e){return zi(Po(t),Po(e))&&t.limitType===e.limitType}function Vo(t){return Hi(Po(t))+"|lt:"+t.limitType}function Uo(t){return"Query(target="+(e=Po(t),n=e.path.canonicalString(),null!==e.collectionGroup&&(n+=" collectionGroup="+e.collectionGroup),0<e.filters.length&&(n+=", filters: ["+e.filters.map(function(t){return(t=t).field.canonicalString()+" "+t.op+" "+xi(t.value)}).join(", ")+"]"),Ti(e.limit)||(n+=", limit: "+e.limit),0<e.orderBy.length&&(n+=", orderBy: ["+e.orderBy.map(function(t){return(t=t).field.canonicalString()+" ("+t.dir+")"}).join(", ")+"]"),e.startAt&&(n+=", startAt: "+To(e.startAt)),e.endAt&&(n+=", endAt: "+To(e.endAt)),"Target("+n+")")+"; limitType="+t.limitType+")";var e,n}function qo(i,t){return t.isFoundDocument()&&(e=i,n=t.key.path,null!==e.collectionGroup?t.key.hasCollectionId(e.collectionGroup)&&e.path.isPrefixOf(n):Si.isDocumentKey(e.path)?e.path.isEqual(n):e.path.isImmediateParentOf(n))&&function(t){for(var e=0,n=i.explicitOrderBy;e<n.length;e++){var r=n[e];if(!r.field.isKeyField()&&null===t.data.field(r.field))return}return 1}(t)&&function(t){for(var e=0,n=i.filters;e<n.length;e++)if(!n[e].matches(t))return;return 1}(t)&&(n=t,(!(t=i).startAt||_o(t.startAt,Lo(t),n))&&(!t.endAt||!_o(t.endAt,Lo(t),n)));var e,n}function Bo(a){return function(t,e){for(var n=!1,r=0,i=Lo(a);r<i.length;r++){var o=i[r],s=function(t,r,e){var n=t.field.isKeyField()?Si.comparator(r.key,e.key):function(t,e){var n=r.data.field(t),t=e.data.field(t);return null!==n&&null!==t?ki(n,t):Gr()}(t.field,e);switch(t.dir){case"asc":return n;case"desc":return-1*n;default:return Gr()}}(o,t,e);if(0!==s)return s;n=n||o.field.isKeyField()}return 0}}function jo(t,e){if(t.I){if(isNaN(e))return{doubleValue:"NaN"};if(e===1/0)return{doubleValue:"Infinity"};if(e===-1/0)return{doubleValue:"-Infinity"}}return{doubleValue:Ii(e)?"-0":e}}function Ko(t){return{integerValue:""+t}}function Go(t,e){return _i(e)?Ko(e):jo(t,e)}I=function(){this._=void 0};function Qo(t,e){return t instanceof is?Li(t=e)||t&&"doubleValue"in t?e:{integerValue:0}:null}var Ho,zo,Wo=(n($o,zo=I),$o),Yo=(n(Xo,Ho=I),Xo);function Xo(t){var e=this;return(e=Ho.call(this)||this).elements=t,e}function $o(){return null!==zo&&zo.apply(this,arguments)||this}function Jo(t,e){for(var n=as(e),r=0,i=t.elements;r<i.length;r++)!function(e){n.some(function(t){return Ni(t,e)})||n.push(e)}(i[r]);return{arrayValue:{values:n}}}var Zo,ts=(n(es,Zo=I),es);function es(t){var e=this;return(e=Zo.call(this)||this).elements=t,e}function ns(t,e){for(var n=as(e),r=0,i=t.elements;r<i.length;r++)!function(e){n=n.filter(function(t){return!Ni(t,e)})}(i[r]);return{arrayValue:{values:n}}}var rs,is=(n(os,rs=I),os);function os(t,e){var n=this;return(n=rs.call(this)||this).R=t,n.A=e,n}function ss(t){return vi(t.integerValue||t.doubleValue)}function as(t){return Pi(t)&&t.arrayValue.values?t.arrayValue.values.slice():[]}function us(t,e){this.version=t,this.transformResults=e}var cs=function(t,e){this.field=t,this.transform=e},hs=(ls.none=function(){return new ls},ls.exists=function(t){return new ls(void 0,t)},ls.updateTime=function(t){return new ls(t)},Object.defineProperty(ls.prototype,"isNone",{get:function(){return void 0===this.updateTime&&void 0===this.exists},enumerable:!1,configurable:!0}),ls.prototype.isEqual=function(t){return this.exists===t.exists&&(this.updateTime?!!t.updateTime&&this.updateTime.isEqual(t.updateTime):!t.updateTime)},ls);function ls(t,e){this.updateTime=t,this.exists=e}function fs(t,e){return void 0!==t.updateTime?e.isFoundDocument()&&e.version.isEqual(t.updateTime):void 0===t.exists||t.exists===e.isFoundDocument()}N=function(){};function ds(t,e,n){var r,i,o,s;t instanceof vs?(i=e,o=n,fs((r=t).precondition,i)&&(s=r.value.clone(),o=_s(r.fieldTransforms,o,i),s.setAll(o),i.convertToFoundDocument(ys(i),s).setHasLocalMutations())):t instanceof ws?(o=e,i=n,fs((s=t).precondition,o)&&(n=_s(s.fieldTransforms,i,o),(i=o.data).setAll(Ts(s)),i.setAll(n),o.convertToFoundDocument(ys(o),i).setHasLocalMutations())):(e=e,fs(t.precondition,e)&&e.convertToNoDocument(Jr.min()))}function ps(t,e){return t.type===e.type&&!!t.key.isEqual(e.key)&&!!t.precondition.isEqual(e.precondition)&&(n=t.fieldTransforms,r=e.fieldTransforms,!!(void 0===n&&void 0===r||n&&r&&Yr(n,r,function(t,e){return e=e,(t=t).field.isEqual(e.field)&&(t=t.transform,e=e.transform,t instanceof Yo&&e instanceof Yo||t instanceof ts&&e instanceof ts?Yr(t.elements,e.elements,Ni):t instanceof is&&e instanceof is?Ni(t.A,e.A):t instanceof Wo&&e instanceof Wo)})))&&(0===t.type?t.value.isEqual(e.value):1!==t.type||t.data.isEqual(e.data)&&t.fieldMask.isEqual(e.fieldMask));var n,r}function ys(t){return t.isFoundDocument()?t.version:Jr.min()}var gs,ms,vs=(n(Es,ms=N),Es),ws=(n(bs,gs=N),bs);function bs(t,e,n,r,i){void 0===i&&(i=[]);var o=this;return(o=gs.call(this)||this).key=t,o.data=e,o.fieldMask=n,o.precondition=r,o.fieldTransforms=i,o.type=1,o}function Es(t,e,n,r){void 0===r&&(r=[]);var i=this;return(i=ms.call(this)||this).key=t,i.value=e,i.precondition=n,i.fieldTransforms=r,i.type=0,i}function Ts(n){var r=new Map;return n.fieldMask.fields.forEach(function(t){var e;t.isEmpty()||(e=n.data.field(t),r.set(t,e))}),r}function Is(t,e,n){var r=new Map;Qr(t.length===n.length);for(var i=0;i<n.length;i++){var o=t[i],s=o.transform,a=e.data.field(o.field);r.set(o.field,(o=s,s=a,a=n[i],o instanceof Yo?Jo(o,s):o instanceof ts?ns(o,s):a))}return r}function _s(t,e,n){for(var r,i=new Map,o=0,s=t;o<s.length;o++){var a=s[o],u=a.transform,c=n.data.field(a.field);i.set(a.field,(r=c,a=e,c=void 0,(u=u)instanceof Wo?(c={fields:{__type__:{stringValue:"server_timestamp"},__local_write_time__:{timestampValue:{seconds:a.seconds,nanos:a.nanoseconds}}}},r&&(c.fields.__previous_value__=r),{mapValue:c}):u instanceof Yo?Jo(u,r):u instanceof ts?ns(u,r):(u=Qo(c=u,r),r=ss(u)+ss(c.A),Li(u)&&Li(c.A)?Ko(r):jo(c.R,r))))}return i}function Ss(t){this.count=t}var As,Ds,Ns,Cs=(n(xs,Ns=N),xs),ks=(n(Rs,Ds=N),Rs);function Rs(t,e){var n=this;return(n=Ds.call(this)||this).key=t,n.precondition=e,n.type=3,n.fieldTransforms=[],n}function xs(t,e){var n=this;return(n=Ns.call(this)||this).key=t,n.precondition=e,n.type=2,n.fieldTransforms=[],n}function Os(t){switch(t){case Pr.OK:return Gr(),0;case Pr.CANCELLED:case Pr.UNKNOWN:case Pr.DEADLINE_EXCEEDED:case Pr.RESOURCE_EXHAUSTED:case Pr.INTERNAL:case Pr.UNAVAILABLE:case Pr.UNAUTHENTICATED:return;case Pr.INVALID_ARGUMENT:case Pr.NOT_FOUND:case Pr.ALREADY_EXISTS:case Pr.PERMISSION_DENIED:case Pr.FAILED_PRECONDITION:case Pr.ABORTED:case Pr.OUT_OF_RANGE:case Pr.UNIMPLEMENTED:case Pr.DATA_LOSS:return 1;default:return Gr(),0}}function Ls(t){if(void 0===t)return Br("GRPC error has no .code"),Pr.UNKNOWN;switch(t){case As.OK:return Pr.OK;case As.CANCELLED:return Pr.CANCELLED;case As.UNKNOWN:return Pr.UNKNOWN;case As.DEADLINE_EXCEEDED:return Pr.DEADLINE_EXCEEDED;case As.RESOURCE_EXHAUSTED:return Pr.RESOURCE_EXHAUSTED;case As.INTERNAL:return Pr.INTERNAL;case As.UNAVAILABLE:return Pr.UNAVAILABLE;case As.UNAUTHENTICATED:return Pr.UNAUTHENTICATED;case As.INVALID_ARGUMENT:return Pr.INVALID_ARGUMENT;case As.NOT_FOUND:return Pr.NOT_FOUND;case As.ALREADY_EXISTS:return Pr.ALREADY_EXISTS;case As.PERMISSION_DENIED:return Pr.PERMISSION_DENIED;case As.FAILED_PRECONDITION:return Pr.FAILED_PRECONDITION;case As.ABORTED:return Pr.ABORTED;case As.OUT_OF_RANGE:return Pr.OUT_OF_RANGE;case As.UNIMPLEMENTED:return Pr.UNIMPLEMENTED;case As.DATA_LOSS:return Pr.DATA_LOSS;default:return Gr()}}(I=As=As||{})[I.OK=0]="OK",I[I.CANCELLED=1]="CANCELLED",I[I.UNKNOWN=2]="UNKNOWN",I[I.INVALID_ARGUMENT=3]="INVALID_ARGUMENT",I[I.DEADLINE_EXCEEDED=4]="DEADLINE_EXCEEDED",I[I.NOT_FOUND=5]="NOT_FOUND",I[I.ALREADY_EXISTS=6]="ALREADY_EXISTS",I[I.PERMISSION_DENIED=7]="PERMISSION_DENIED",I[I.UNAUTHENTICATED=16]="UNAUTHENTICATED",I[I.RESOURCE_EXHAUSTED=8]="RESOURCE_EXHAUSTED",I[I.FAILED_PRECONDITION=9]="FAILED_PRECONDITION",I[I.ABORTED=10]="ABORTED",I[I.OUT_OF_RANGE=11]="OUT_OF_RANGE",I[I.UNIMPLEMENTED=12]="UNIMPLEMENTED",I[I.INTERNAL=13]="INTERNAL",I[I.UNAVAILABLE=14]="UNAVAILABLE",I[I.DATA_LOSS=15]="DATA_LOSS";var Ps=(qs.prototype.insert=function(t,e){return new qs(this.comparator,this.root.insert(t,e,this.comparator).copy(null,null,Fs.BLACK,null,null))},qs.prototype.remove=function(t){return new qs(this.comparator,this.root.remove(t,this.comparator).copy(null,null,Fs.BLACK,null,null))},qs.prototype.get=function(t){for(var e=this.root;!e.isEmpty();){var n=this.comparator(t,e.key);if(0===n)return e.value;n<0?e=e.left:0<n&&(e=e.right)}return null},qs.prototype.indexOf=function(t){for(var e=0,n=this.root;!n.isEmpty();){var r=this.comparator(t,n.key);if(0===r)return e+n.left.size;n=r<0?n.left:(e+=n.left.size+1,n.right)}return-1},qs.prototype.isEmpty=function(){return this.root.isEmpty()},Object.defineProperty(qs.prototype,"size",{get:function(){return this.root.size},enumerable:!1,configurable:!0}),qs.prototype.minKey=function(){return this.root.minKey()},qs.prototype.maxKey=function(){return this.root.maxKey()},qs.prototype.inorderTraversal=function(t){return this.root.inorderTraversal(t)},qs.prototype.forEach=function(n){this.inorderTraversal(function(t,e){return n(t,e),!1})},qs.prototype.toString=function(){var n=[];return this.inorderTraversal(function(t,e){return n.push(t+":"+e),!1}),"{"+n.join(", ")+"}"},qs.prototype.reverseTraversal=function(t){return this.root.reverseTraversal(t)},qs.prototype.getIterator=function(){return new Ms(this.root,null,this.comparator,!1)},qs.prototype.getIteratorFrom=function(t){return new Ms(this.root,t,this.comparator,!1)},qs.prototype.getReverseIterator=function(){return new Ms(this.root,null,this.comparator,!0)},qs.prototype.getReverseIteratorFrom=function(t){return new Ms(this.root,t,this.comparator,!0)},qs),Ms=(Us.prototype.getNext=function(){var t=this.nodeStack.pop(),e={key:t.key,value:t.value};if(this.isReverse)for(t=t.left;!t.isEmpty();)this.nodeStack.push(t),t=t.right;else for(t=t.right;!t.isEmpty();)this.nodeStack.push(t),t=t.left;return e},Us.prototype.hasNext=function(){return 0<this.nodeStack.length},Us.prototype.peek=function(){if(0===this.nodeStack.length)return null;var t=this.nodeStack[this.nodeStack.length-1];return{key:t.key,value:t.value}},Us),Fs=(Vs.prototype.copy=function(t,e,n,r,i){return new Vs(null!=t?t:this.key,null!=e?e:this.value,null!=n?n:this.color,null!=r?r:this.left,null!=i?i:this.right)},Vs.prototype.isEmpty=function(){return!1},Vs.prototype.inorderTraversal=function(t){return this.left.inorderTraversal(t)||t(this.key,this.value)||this.right.inorderTraversal(t)},Vs.prototype.reverseTraversal=function(t){return this.right.reverseTraversal(t)||t(this.key,this.value)||this.left.reverseTraversal(t)},Vs.prototype.min=function(){return this.left.isEmpty()?this:this.left.min()},Vs.prototype.minKey=function(){return this.min().key},Vs.prototype.maxKey=function(){return this.right.isEmpty()?this.key:this.right.maxKey()},Vs.prototype.insert=function(t,e,n){var r=this,i=n(t,r.key);return(r=i<0?r.copy(null,null,null,r.left.insert(t,e,n),null):0===i?r.copy(null,e,null,null,null):r.copy(null,null,null,null,r.right.insert(t,e,n))).fixUp()},Vs.prototype.removeMin=function(){if(this.left.isEmpty())return Vs.EMPTY;var t=this;return(t=(t=!t.left.isRed()&&!t.left.left.isRed()?t.moveRedLeft():t).copy(null,null,null,t.left.removeMin(),null)).fixUp()},Vs.prototype.remove=function(t,e){var n,r=this;if(e(t,r.key)<0)r=(r=!(r.left.isEmpty()||r.left.isRed()||r.left.left.isRed())?r.moveRedLeft():r).copy(null,null,null,r.left.remove(t,e),null);else{if(0===e(t,(r=!((r=r.left.isRed()?r.rotateRight():r).right.isEmpty()||r.right.isRed()||r.right.left.isRed())?r.moveRedRight():r).key)){if(r.right.isEmpty())return Vs.EMPTY;n=r.right.min(),r=r.copy(n.key,n.value,null,null,r.right.removeMin())}r=r.copy(null,null,null,null,r.right.remove(t,e))}return r.fixUp()},Vs.prototype.isRed=function(){return this.color},Vs.prototype.fixUp=function(){var t=this;return t=(t=(t=t.right.isRed()&&!t.left.isRed()?t.rotateLeft():t).left.isRed()&&t.left.left.isRed()?t.rotateRight():t).left.isRed()&&t.right.isRed()?t.colorFlip():t},Vs.prototype.moveRedLeft=function(){var t=this.colorFlip();return t=t.right.left.isRed()?(t=(t=t.copy(null,null,null,null,t.right.rotateRight())).rotateLeft()).colorFlip():t},Vs.prototype.moveRedRight=function(){var t=this.colorFlip();return t=t.left.left.isRed()?(t=t.rotateRight()).colorFlip():t},Vs.prototype.rotateLeft=function(){var t=this.copy(null,null,Vs.RED,null,this.right.left);return this.right.copy(null,null,this.color,t,null)},Vs.prototype.rotateRight=function(){var t=this.copy(null,null,Vs.RED,this.left.right,null);return this.left.copy(null,null,this.color,null,t)},Vs.prototype.colorFlip=function(){var t=this.left.copy(null,null,!this.left.color,null,null),e=this.right.copy(null,null,!this.right.color,null,null);return this.copy(null,null,!this.color,t,e)},Vs.prototype.checkMaxDepth=function(){var t=this.check();return Math.pow(2,t)<=this.size+1},Vs.prototype.check=function(){if(this.isRed()&&this.left.isRed())throw Gr();if(this.right.isRed())throw Gr();var t=this.left.check();if(t!==this.right.check())throw Gr();return t+(this.isRed()?0:1)},Vs);function Vs(t,e,n,r,i){this.key=t,this.value=e,this.color=null!=n?n:Vs.RED,this.left=null!=r?r:Vs.EMPTY,this.right=null!=i?i:Vs.EMPTY,this.size=this.left.size+1+this.right.size}function Us(t,e,n,r){this.isReverse=r,this.nodeStack=[];for(var i=1;!t.isEmpty();)if(i=e?n(t.key,e):1,r&&(i*=-1),i<0)t=this.isReverse?t.left:t.right;else{if(0===i){this.nodeStack.push(t);break}this.nodeStack.push(t),t=this.isReverse?t.right:t.left}}function qs(t,e){this.comparator=t,this.root=e||Fs.EMPTY}function Bs(){this.size=0}Fs.EMPTY=null,Fs.RED=!0,Fs.BLACK=!1,Fs.EMPTY=(Object.defineProperty(Bs.prototype,"key",{get:function(){throw Gr()},enumerable:!1,configurable:!0}),Object.defineProperty(Bs.prototype,"value",{get:function(){throw Gr()},enumerable:!1,configurable:!0}),Object.defineProperty(Bs.prototype,"color",{get:function(){throw Gr()},enumerable:!1,configurable:!0}),Object.defineProperty(Bs.prototype,"left",{get:function(){throw Gr()},enumerable:!1,configurable:!0}),Object.defineProperty(Bs.prototype,"right",{get:function(){throw Gr()},enumerable:!1,configurable:!0}),Bs.prototype.copy=function(t,e,n,r,i){return this},Bs.prototype.insert=function(t,e,n){return new Fs(t,e)},Bs.prototype.remove=function(t,e){return this},Bs.prototype.isEmpty=function(){return!0},Bs.prototype.inorderTraversal=function(t){return!1},Bs.prototype.reverseTraversal=function(t){return!1},Bs.prototype.minKey=function(){return null},Bs.prototype.maxKey=function(){return null},Bs.prototype.isRed=function(){return!1},Bs.prototype.checkMaxDepth=function(){return!0},Bs.prototype.check=function(){return 0},new Bs);var js=(Hs.prototype.has=function(t){return null!==this.data.get(t)},Hs.prototype.first=function(){return this.data.minKey()},Hs.prototype.last=function(){return this.data.maxKey()},Object.defineProperty(Hs.prototype,"size",{get:function(){return this.data.size},enumerable:!1,configurable:!0}),Hs.prototype.indexOf=function(t){return this.data.indexOf(t)},Hs.prototype.forEach=function(n){this.data.inorderTraversal(function(t,e){return n(t),!1})},Hs.prototype.forEachInRange=function(t,e){for(var n=this.data.getIteratorFrom(t[0]);n.hasNext();){var r=n.getNext();if(0<=this.comparator(r.key,t[1]))return;e(r.key)}},Hs.prototype.forEachWhile=function(t,e){for(var n=void 0!==e?this.data.getIteratorFrom(e):this.data.getIterator();n.hasNext();)if(!t(n.getNext().key))return},Hs.prototype.firstAfterOrEqual=function(t){t=this.data.getIteratorFrom(t);return t.hasNext()?t.getNext().key:null},Hs.prototype.getIterator=function(){return new Ks(this.data.getIterator())},Hs.prototype.getIteratorFrom=function(t){return new Ks(this.data.getIteratorFrom(t))},Hs.prototype.add=function(t){return this.copy(this.data.remove(t).insert(t,!0))},Hs.prototype.delete=function(t){return this.has(t)?this.copy(this.data.remove(t)):this},Hs.prototype.isEmpty=function(){return this.data.isEmpty()},Hs.prototype.unionWith=function(t){var e=this;return e.size<t.size&&(e=t,t=this),t.forEach(function(t){e=e.add(t)}),e},Hs.prototype.isEqual=function(t){if(!(t instanceof Hs))return!1;if(this.size!==t.size)return!1;for(var e=this.data.getIterator(),n=t.data.getIterator();e.hasNext();){var r=e.getNext().key,i=n.getNext().key;if(0!==this.comparator(r,i))return!1}return!0},Hs.prototype.toArray=function(){var e=[];return this.forEach(function(t){e.push(t)}),e},Hs.prototype.toString=function(){var e=[];return this.forEach(function(t){return e.push(t)}),"SortedSet("+e.toString()+")"},Hs.prototype.copy=function(t){var e=new Hs(this.comparator);return e.data=t,e},Hs),Ks=(Qs.prototype.getNext=function(){return this.iter.getNext().key},Qs.prototype.hasNext=function(){return this.iter.hasNext()},Qs),Gs=new Ps(Si.comparator);function Qs(t){this.iter=t}function Hs(t){this.comparator=t,this.data=new Ps(this.comparator)}var zs=new Ps(Si.comparator);var Ws=new Ps(Si.comparator);var Ys=new js(Si.comparator);function Xs(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=Ys,r=0,i=t;r<i.length;r++)var o=i[r],n=n.add(o);return n}var $s=new js(Wr);var Js=(ua.createSynthesizedRemoteEventForCurrentChange=function(t,e){var n=new Map;return n.set(t,Zs.createSynthesizedTargetChangeForCurrentChange(t,e)),new ua(Jr.min(),n,$s,Gs,Xs())},ua),Zs=(aa.createSynthesizedTargetChangeForCurrentChange=function(t,e){return new aa(hi.EMPTY_BYTE_STRING,e,Xs(),Xs(),Xs())},aa),ta=function(t,e,n,r){this.v=t,this.removedTargetIds=e,this.key=n,this.P=r},ea=function(t,e){this.targetId=t,this.V=e},na=function(t,e,n,r){void 0===n&&(n=hi.EMPTY_BYTE_STRING),void 0===r&&(r=null),this.state=t,this.targetIds=e,this.resumeToken=n,this.cause=r},ra=(Object.defineProperty(sa.prototype,"current",{get:function(){return this.N},enumerable:!1,configurable:!0}),Object.defineProperty(sa.prototype,"resumeToken",{get:function(){return this.C},enumerable:!1,configurable:!0}),Object.defineProperty(sa.prototype,"k",{get:function(){return 0!==this.S},enumerable:!1,configurable:!0}),Object.defineProperty(sa.prototype,"$",{get:function(){return this.F},enumerable:!1,configurable:!0}),sa.prototype.O=function(t){0<t.approximateByteSize()&&(this.F=!0,this.C=t)},sa.prototype.M=function(){var n=Xs(),r=Xs(),i=Xs();return this.D.forEach(function(t,e){switch(e){case 0:n=n.add(t);break;case 2:r=r.add(t);break;case 1:i=i.add(t);break;default:Gr()}}),new Zs(this.C,this.N,n,r,i)},sa.prototype.L=function(){this.F=!1,this.D=ha()},sa.prototype.B=function(t,e){this.F=!0,this.D=this.D.insert(t,e)},sa.prototype.q=function(t){this.F=!0,this.D=this.D.remove(t)},sa.prototype.U=function(){this.S+=1},sa.prototype.K=function(){--this.S},sa.prototype.j=function(){this.F=!0,this.N=!0},sa),ia=(oa.prototype.X=function(t){for(var e=0,n=t.v;e<n.length;e++){var r=n[e];t.P&&t.P.isFoundDocument()?this.Z(r,t.P):this.tt(r,t.key,t.P)}for(var i=0,o=t.removedTargetIds;i<o.length;i++)r=o[i],this.tt(r,t.key,t.P)},oa.prototype.et=function(n){var r=this;this.forEachTarget(n,function(t){var e=r.nt(t);switch(n.state){case 0:r.st(t)&&e.O(n.resumeToken);break;case 1:e.K(),e.k||e.L(),e.O(n.resumeToken);break;case 2:e.K(),e.k||r.removeTarget(t);break;case 3:r.st(t)&&(e.j(),e.O(n.resumeToken));break;case 4:r.st(t)&&(r.it(t),e.O(n.resumeToken));break;default:Gr()}})},oa.prototype.forEachTarget=function(t,n){var r=this;0<t.targetIds.length?t.targetIds.forEach(n):this.G.forEach(function(t,e){r.st(e)&&n(e)})},oa.prototype.rt=function(t){var e=t.targetId,n=t.V.count,t=this.ot(e);t&&(Wi(t=t.target)?0===n?(t=new Si(t.path),this.tt(e,t,ji.newNoDocument(t,Jr.min()))):Qr(1===n):this.ct(e)!==n&&(this.it(e),this.Y=this.Y.add(e)))},oa.prototype.ut=function(r){var i=this,o=new Map;this.G.forEach(function(t,e){var n=i.ot(e);n&&(t.current&&Wi(n.target)&&(n=new Si(n.target.path),null!==i.H.get(n)||i.at(e,n)||i.tt(e,n,ji.newNoDocument(n,r))),t.$&&(o.set(e,t.M()),t.L()))});var s=Xs();this.J.forEach(function(t,e){var n=!0;e.forEachWhile(function(t){t=i.ot(t);return!t||2===t.purpose||(n=!1)}),n&&(s=s.add(t))});var t=new Js(r,o,this.Y,this.H,s);return this.H=Gs,this.J=ca(),this.Y=new js(Wr),t},oa.prototype.Z=function(t,e){var n;this.st(t)&&(n=this.at(t,e.key)?2:0,this.nt(t).B(e.key,n),this.H=this.H.insert(e.key,e),this.J=this.J.insert(e.key,this.ht(e.key).add(t)))},oa.prototype.tt=function(t,e,n){var r;this.st(t)&&(r=this.nt(t),this.at(t,e)?r.B(e,1):r.q(e),this.J=this.J.insert(e,this.ht(e).delete(t)),n&&(this.H=this.H.insert(e,n)))},oa.prototype.removeTarget=function(t){this.G.delete(t)},oa.prototype.ct=function(t){var e=this.nt(t).M();return this.W.getRemoteKeysForTarget(t).size+e.addedDocuments.size-e.removedDocuments.size},oa.prototype.U=function(t){this.nt(t).U()},oa.prototype.nt=function(t){var e=this.G.get(t);return e||(e=new ra,this.G.set(t,e)),e},oa.prototype.ht=function(t){var e=this.J.get(t);return e||(e=new js(Wr),this.J=this.J.insert(t,e)),e},oa.prototype.st=function(t){var e=null!==this.ot(t);return e||qr("WatchChangeAggregator","Detected inactive target",t),e},oa.prototype.ot=function(t){var e=this.G.get(t);return e&&e.k?null:this.W.lt(t)},oa.prototype.it=function(e){var n=this;this.G.set(e,new ra),this.W.getRemoteKeysForTarget(e).forEach(function(t){n.tt(e,t,null)})},oa.prototype.at=function(t,e){return this.W.getRemoteKeysForTarget(t).has(e)},oa);function oa(t){this.W=t,this.G=new Map,this.H=Gs,this.J=ca(),this.Y=new js(Wr)}function sa(){this.S=0,this.D=ha(),this.C=hi.EMPTY_BYTE_STRING,this.N=!1,this.F=!0}function aa(t,e,n,r,i){this.resumeToken=t,this.current=e,this.addedDocuments=n,this.modifiedDocuments=r,this.removedDocuments=i}function ua(t,e,n,r,i){this.snapshotVersion=t,this.targetChanges=e,this.targetMismatches=n,this.documentUpdates=r,this.resolvedLimboDocuments=i}function ca(){return new Ps(Si.comparator)}function ha(){return new Ps(Si.comparator)}var la={asc:"ASCENDING",desc:"DESCENDING"},fa={"<":"LESS_THAN","<=":"LESS_THAN_OR_EQUAL",">":"GREATER_THAN",">=":"GREATER_THAN_OR_EQUAL","==":"EQUAL","!=":"NOT_EQUAL","array-contains":"ARRAY_CONTAINS",in:"IN","not-in":"NOT_IN","array-contains-any":"ARRAY_CONTAINS_ANY"},da=function(t,e){this.databaseId=t,this.I=e};function pa(t,e){return t.I?new Date(1e3*e.seconds).toISOString().replace(/\.\d*/,"").replace("Z","")+"."+("000000000"+e.nanoseconds).slice(-9)+"Z":{seconds:""+e.seconds,nanos:e.nanoseconds}}function ya(t,e){return t.I?e.toBase64():e.toUint8Array()}function ga(t){return Qr(!!t),Jr.fromTimestamp((t=mi(t),new $r(t.seconds,t.nanos)))}function ma(t,e){return new si(["projects",t.projectId,"databases",t.database]).child("documents").child(e).canonicalString()}function va(t){t=si.fromString(t);return Qr(Va(t)),t}function wa(t,e){return ma(t.databaseId,e.path)}function ba(t,e){e=va(e);if(e.get(1)!==t.databaseId.projectId)throw new Mr(Pr.INVALID_ARGUMENT,"Tried to deserialize key from different project: "+e.get(1)+" vs "+t.databaseId.projectId);if(e.get(3)!==t.databaseId.database)throw new Mr(Pr.INVALID_ARGUMENT,"Tried to deserialize key from different database: "+e.get(3)+" vs "+t.databaseId.database);return new Si(_a(e))}function Ea(t,e){return ma(t.databaseId,e)}function Ta(t){t=va(t);return 4===t.length?si.emptyPath():_a(t)}function Ia(t){return new si(["projects",t.databaseId.projectId,"databases",t.databaseId.database]).canonicalString()}function _a(t){return Qr(4<t.length&&"documents"===t.get(4)),t.popFirst(5)}function Sa(t,e,n){return{name:wa(t,e),fields:n.value.mapValue.fields}}function Aa(t,e,n){var r=ba(t,e.name),t=ga(e.updateTime),e=new qi({mapValue:{fields:e.fields}}),e=ji.newFoundDocument(r,t,e);return n&&e.setHasCommittedMutations(),n?e.setHasCommittedMutations():e}function Da(t,e){var n,r,i;if(e instanceof vs)n={update:Sa(t,e.key,e.value)};else if(e instanceof Cs)n={delete:wa(t,e.key)};else if(e instanceof ws)n={update:Sa(t,e.key,e.data),updateMask:(r=e.fieldMask,i=[],r.fields.forEach(function(t){return i.push(t.canonicalString())}),{fieldPaths:i})};else{if(!(e instanceof ks))return Gr();n={verify:wa(t,e.key)}}return 0<e.fieldTransforms.length&&(n.updateTransforms=e.fieldTransforms.map(function(t){var e=t.transform;if(e instanceof Wo)return{fieldPath:t.field.canonicalString(),setToServerValue:"REQUEST_TIME"};if(e instanceof Yo)return{fieldPath:t.field.canonicalString(),appendMissingElements:{values:e.elements}};if(e instanceof ts)return{fieldPath:t.field.canonicalString(),removeAllFromArray:{values:e.elements}};if(e instanceof is)return{fieldPath:t.field.canonicalString(),increment:e.A};throw Gr()})),e.precondition.isNone||(n.currentDocument=void 0!==(e=e.precondition).updateTime?{updateTime:pa(t,e.updateTime.toTimestamp())}:void 0!==e.exists?{exists:e.exists}:Gr()),n}function Na(e,t){var n=t.currentDocument?void 0!==(s=t.currentDocument).updateTime?hs.updateTime(ga(s.updateTime)):void 0!==s.exists?hs.exists(s.exists):hs.none():hs.none(),r=t.updateTransforms?t.updateTransforms.map(function(t){return function(t,e){var n,r=null;"setToServerValue"in e?(Qr("REQUEST_TIME"===e.setToServerValue),r=new Wo):"appendMissingElements"in e?(n=e.appendMissingElements.values||[],r=new Yo(n)):"removeAllFromArray"in e?(n=e.removeAllFromArray.values||[],r=new ts(n)):"increment"in e?r=new is(t,e.increment):Gr();e=ui.fromServerFormat(e.fieldPath);return new cs(e,r)}(e,t)}):[];if(t.update){t.update.name;var i=ba(e,t.update.name),o=new qi({mapValue:{fields:t.update.fields}});if(t.updateMask){var s=(s=t.updateMask.fieldPaths||[],new ci(s.map(function(t){return ui.fromServerFormat(t)})));return new ws(i,o,s,n,r)}return new vs(i,o,n,r)}if(t.delete){r=ba(e,t.delete);return new Cs(r,n)}if(t.verify){t=ba(e,t.verify);return new ks(t,n)}return Gr()}function Ca(t,e){return{documents:[Ea(t,e.path)]}}function ka(t,e){var n={structuredQuery:{}},r=e.path;null!==e.collectionGroup?(n.parent=Ea(t,r),n.structuredQuery.from=[{collectionId:e.collectionGroup,allDescendants:!0}]):(n.parent=Ea(t,r.popLast()),n.structuredQuery.from=[{collectionId:r.lastSegment()}]);r=function(t){if(0!==t.length){t=t.map(function(t){if("=="===t.op){if(Fi(t.value))return{unaryFilter:{field:La(t.field),op:"IS_NAN"}};if(Mi(t.value))return{unaryFilter:{field:La(t.field),op:"IS_NULL"}}}else if("!="===t.op){if(Fi(t.value))return{unaryFilter:{field:La(t.field),op:"IS_NOT_NAN"}};if(Mi(t.value))return{unaryFilter:{field:La(t.field),op:"IS_NOT_NULL"}}}return{fieldFilter:{field:La(t.field),op:(e=t.op,fa[e]),value:t.value}};var e});return 1===t.length?t[0]:{compositeFilter:{op:"AND",filters:t}}}}(e.filters);r&&(n.structuredQuery.where=r);r=function(t){if(0!==t.length)return t.map(function(t){return{field:La((t=t).field),direction:(t=t.dir,la[t])}})}(e.orderBy);r&&(n.structuredQuery.orderBy=r);r=e.limit,r=t.I||Ti(r)?r:{value:r};return null!==r&&(n.structuredQuery.limit=r),e.startAt&&(n.structuredQuery.startAt=xa(e.startAt)),e.endAt&&(n.structuredQuery.endAt=xa(e.endAt)),n}function Ra(t){var e=Ta(t.parent),n=t.structuredQuery,r=n.from?n.from.length:0,i=null;0<r&&(Qr(1===r),(a=n.from[0]).allDescendants?i=a.collectionId:e=e.child(a.collectionId));var o=[];n.where&&(o=function e(t){return t?void 0!==t.unaryFilter?[Fa(t)]:void 0!==t.fieldFilter?[Ma(t)]:void 0!==t.compositeFilter?t.compositeFilter.filters.map(function(t){return e(t)}).reduce(function(t,e){return t.concat(e)}):Gr():[]}(n.where));var s=[],t=null,r=null,a=null;return Do(e,i,s=n.orderBy?n.orderBy.map(function(t){return new Io(Pa((e=t).field),function(){switch(e.direction){case"ASCENDING":return"asc";case"DESCENDING":return"desc";default:return}}());var e}):s,o,t=n.limit?Ti(o="object"==typeof(o=n.limit)?o.value:o)?null:o:t,"F",r=n.startAt?Oa(n.startAt):r,a=n.endAt?Oa(n.endAt):a)}function xa(t){return{before:t.before,values:t.position}}function Oa(t){var e=!!t.before,t=t.values||[];return new mo(t,e)}function La(t){return{fieldPath:t.canonicalString()}}function Pa(t){return ui.fromServerFormat(t.fieldPath)}function Ma(t){return Xi.create(Pa(t.fieldFilter.field),function(){switch(t.fieldFilter.op){case"EQUAL":return"==";case"NOT_EQUAL":return"!=";case"GREATER_THAN":return">";case"GREATER_THAN_OR_EQUAL":return">=";case"LESS_THAN":return"<";case"LESS_THAN_OR_EQUAL":return"<=";case"ARRAY_CONTAINS":return"array-contains";case"IN":return"in";case"NOT_IN":return"not-in";case"ARRAY_CONTAINS_ANY":return"array-contains-any";case"OPERATOR_UNSPECIFIED":default:return Gr()}}(),t.fieldFilter.value)}function Fa(t){switch(t.unaryFilter.op){case"IS_NAN":var e=Pa(t.unaryFilter.field);return Xi.create(e,"==",{doubleValue:NaN});case"IS_NULL":e=Pa(t.unaryFilter.field);return Xi.create(e,"==",{nullValue:"NULL_VALUE"});case"IS_NOT_NAN":var n=Pa(t.unaryFilter.field);return Xi.create(n,"!=",{doubleValue:NaN});case"IS_NOT_NULL":n=Pa(t.unaryFilter.field);return Xi.create(n,"!=",{nullValue:"NULL_VALUE"});case"OPERATOR_UNSPECIFIED":default:return Gr()}}function Va(t){return 4<=t.length&&"projects"===t.get(0)&&"databases"===t.get(2)}function Ua(t){for(var e="",n=0;n<t.length;n++)0<e.length&&(e=qa(e)),e=function(t,e){for(var n=e,r=t.length,i=0;i<r;i++){var o=t.charAt(i);switch(o){case"\0":n+="\x01\x10";break;case"\x01":n+="\x01\x11";break;default:n+=o}}return n}(t.get(n),e);return qa(e)}function qa(t){return t+"\x01\x01"}function Ba(t){var e=t.length;if(Qr(2<=e),2===e)return Qr("\x01"===t.charAt(0)&&"\x01"===t.charAt(1)),si.emptyPath();for(var n=e-2,r=[],i="",o=0;o<e;){var s=t.indexOf("\x01",o);switch((s<0||n<s)&&Gr(),t.charAt(s+1)){case"\x01":var a=t.substring(o,s),u=void 0;0===i.length?u=a:(u=i+=a,i=""),r.push(u);break;case"\x10":i+=t.substring(o,s),i+="\0";break;case"\x11":i+=t.substring(o,s+1);break;default:Gr()}o=s+2}return new si(r)}function ja(t,e,n){this.ownerId=t,this.allowTabSynchronization=e,this.leaseTimestampMs=n}var Ka=function(t,e){this.seconds=t,this.nanoseconds=e};ja.store="owner",ja.key="owner";function Ga(t,e,n){this.userId=t,this.lastAcknowledgedBatchId=e,this.lastStreamToken=n}Ga.store="mutationQueues",Ga.keyPath="userId";function Qa(t,e,n,r,i){this.userId=t,this.batchId=e,this.localWriteTimeMs=n,this.baseMutations=r,this.mutations=i}Qa.store="mutations",Qa.keyPath="batchId",Qa.userMutationsIndex="userMutationsIndex",Qa.userMutationsKeyPath=["userId","batchId"];var Ha=(za.prefixForUser=function(t){return[t]},za.prefixForPath=function(t,e){return[t,Ua(e)]},za.key=function(t,e,n){return[t,Ua(e),n]},za);function za(){}Ha.store="documentMutations",Ha.PLACEHOLDER=new Ha;function Wa(t,e){this.path=t,this.readTime=e}function Ya(t,e){this.path=t,this.version=e}var Xa=function(t,e,n,r,i,o){this.unknownDocument=t,this.noDocument=e,this.document=n,this.hasCommittedMutations=r,this.readTime=i,this.parentPath=o};Xa.store="remoteDocuments",Xa.readTimeIndex="readTimeIndex",Xa.readTimeIndexPath="readTime",Xa.collectionReadTimeIndex="collectionReadTimeIndex",Xa.collectionReadTimeIndexPath=["parentPath","readTime"];function $a(t){this.byteSize=t}$a.store="remoteDocumentGlobal",$a.key="remoteDocumentGlobalKey";function Ja(t,e,n,r,i,o,s){this.targetId=t,this.canonicalId=e,this.readTime=n,this.resumeToken=r,this.lastListenSequenceNumber=i,this.lastLimboFreeSnapshotVersion=o,this.query=s}Ja.store="targets",Ja.keyPath="targetId",Ja.queryTargetsIndexName="queryTargetsIndex",Ja.queryTargetsKeyPath=["canonicalId","targetId"];var Za=function(t,e,n){this.targetId=t,this.path=e,this.sequenceNumber=n};Za.store="targetDocuments",Za.keyPath=["targetId","path"],Za.documentTargetsIndex="documentTargetsIndex",Za.documentTargetsKeyPath=["path","targetId"];function tu(t,e,n,r){this.highestTargetId=t,this.highestListenSequenceNumber=e,this.lastRemoteSnapshotVersion=n,this.targetCount=r}tu.key="targetGlobalKey",tu.store="targetGlobal";function eu(t,e){this.collectionId=t,this.parent=e}eu.store="collectionParents",eu.keyPath=["collectionId","parent"];function nu(t,e,n,r){this.clientId=t,this.updateTimeMs=e,this.networkEnabled=n,this.inForeground=r}nu.store="clientMetadata",nu.keyPath="clientId";function ru(t,e,n){this.bundleId=t,this.createTime=e,this.version=n}ru.store="bundles",ru.keyPath="bundleId";function iu(t,e,n){this.name=t,this.readTime=e,this.bundledQuery=n}iu.store="namedQueries",iu.keyPath="name";var ou,su=s(s([],s(s([],s(s([],s(s([],[Ga.store,Qa.store,Ha.store,Xa.store,Ja.store,ja.store,tu.store,Za.store]),[nu.store])),[$a.store])),[eu.store])),[ru.store,iu.store]),au="The current tab is not in the required state to perform this operation. It might be necessary to refresh the browser tab.",N=(wu.prototype.addOnCommittedListener=function(t){this.onCommittedListeners.push(t)},wu.prototype.raiseOnCommittedEvent=function(){this.onCommittedListeners.forEach(function(t){return t()})},wu),uu=function(){var n=this;this.promise=new Promise(function(t,e){n.resolve=t,n.reject=e})},cu=(vu.prototype.catch=function(t){return this.next(void 0,t)},vu.prototype.next=function(r,i){var o=this;return this.callbackAttached&&Gr(),this.callbackAttached=!0,this.isDone?this.error?this.wrapFailure(i,this.error):this.wrapSuccess(r,this.result):new vu(function(e,n){o.nextCallback=function(t){o.wrapSuccess(r,t).next(e,n)},o.catchCallback=function(t){o.wrapFailure(i,t).next(e,n)}})},vu.prototype.toPromise=function(){var n=this;return new Promise(function(t,e){n.next(t,e)})},vu.prototype.wrapUserFunction=function(t){try{var e=t();return e instanceof vu?e:vu.resolve(e)}catch(t){return vu.reject(t)}},vu.prototype.wrapSuccess=function(t,e){return t?this.wrapUserFunction(function(){return t(e)}):vu.resolve(e)},vu.prototype.wrapFailure=function(t,e){return t?this.wrapUserFunction(function(){return t(e)}):vu.reject(e)},vu.resolve=function(n){return new vu(function(t,e){t(n)})},vu.reject=function(n){return new vu(function(t,e){e(n)})},vu.waitFor=function(t){return new vu(function(e,n){var r=0,i=0,o=!1;t.forEach(function(t){++r,t.next(function(){++i,o&&i===r&&e()},function(t){return n(t)})}),o=!0,i===r&&e()})},vu.or=function(t){for(var n=vu.resolve(!1),e=0,r=t;e<r.length;e++)!function(e){n=n.next(function(t){return t?vu.resolve(t):e()})}(r[e]);return n},vu.forEach=function(t,n){var r=this,i=[];return t.forEach(function(t,e){i.push(n.call(r,t,e))}),this.waitFor(i)},vu),hu=(mu.open=function(t,e,n,r){try{return new mu(e,t.transaction(r,n))}catch(t){throw new du(e,t)}},Object.defineProperty(mu.prototype,"dt",{get:function(){return this.ft.promise},enumerable:!1,configurable:!0}),mu.prototype.abort=function(t){t&&this.ft.reject(t),this.aborted||(qr("SimpleDb","Aborting transaction:",t?t.message:"Client-initiated abort"),this.aborted=!0,this.transaction.abort())},mu.prototype.store=function(t){t=this.transaction.objectStore(t);return new Eu(t)},mu),lu=(gu.delete=function(t){return qr("SimpleDb","Removing database:",t),Iu(window.indexedDB.deleteDatabase(t)).toPromise()},gu.yt=function(){if("undefined"==typeof indexedDB)return!1;if(gu.gt())return!0;var t=h(),e=gu._t(t),n=0<e&&e<10,e=gu.Et(t),e=0<e&&e<4.5;return!(0<t.indexOf("MSIE ")||0<t.indexOf("Trident/")||0<t.indexOf("Edge/")||n||e)},gu.gt=function(){var t;return"undefined"!=typeof process&&"YES"===(null===(t=process.env)||void 0===t?void 0:t.Tt)},gu.It=function(t,e){return t.store(e)},gu._t=function(t){t=t.match(/i(?:phone|pad|pod) os ([\d_]+)/i),t=t?t[1].split("_").slice(0,2).join("."):"-1";return Number(t)},gu.Et=function(t){t=t.match(/Android ([\d.]+)/i),t=t?t[1].split(".").slice(0,2).join("."):"-1";return Number(t)},gu.prototype.At=function(o){return y(this,void 0,void 0,function(){var e,i=this;return g(this,function(t){switch(t.label){case 0:return this.db?[3,2]:(qr("SimpleDb","Opening database:",this.name),e=this,[4,new Promise(function(e,n){var r=indexedDB.open(i.name,i.version);r.onsuccess=function(t){t=t.target.result;e(t)},r.onblocked=function(){n(new du(o,"Cannot upgrade IndexedDB schema while another tab is open. Close all tabs that access Firestore and reload this page to proceed."))},r.onerror=function(t){t=t.target.error;"VersionError"===t.name?n(new Mr(Pr.FAILED_PRECONDITION,"A newer version of the Firestore SDK was previously used and so the persisted data is not compatible with the version of the SDK you are now using. The SDK will operate with persistence disabled. If you need persistence, please re-upgrade to a newer version of the SDK or else clear the persisted IndexedDB data for your app to start fresh.")):n(new du(o,t))},r.onupgradeneeded=function(t){qr("SimpleDb",'Database "'+i.name+'" requires upgrade from version:',t.oldVersion);var e=t.target.result;i.wt.Rt(e,r.transaction,t.oldVersion,i.version).next(function(){qr("SimpleDb","Database upgrade to version "+i.version+" complete")})}})]);case 1:e.db=t.sent(),t.label=2;case 2:return[2,(this.bt&&(this.db.onversionchange=function(t){return i.bt(t)}),this.db)]}})})},gu.prototype.vt=function(e){this.bt=e,this.db&&(this.db.onversionchange=function(t){return e(t)})},gu.prototype.runTransaction=function(a,n,u,c){return y(this,void 0,void 0,function(){var i,o,s,e;return g(this,function(t){switch(t.label){case 0:i="readonly"===n,o=0,e=function(){var e,n,r;return g(this,function(t){switch(t.label){case 0:++o,t.label=1;case 1:return t.trys.push([1,4,,5]),[4,s.At(a)];case 2:return s.db=t.sent(),e=hu.open(s.db,a,i?"readonly":"readwrite",u),n=c(e).catch(function(t){return e.abort(t),cu.reject(t)}).toPromise(),r={},n.catch(function(){}),[4,e.dt];case 3:return[2,(r.value=(t.sent(),n),r)];case 4:return n=t.sent(),r="FirebaseError"!==n.name&&o<3,qr("SimpleDb","Transaction failed with error:",n.message,"Retrying:",r),s.close(),r?[3,5]:[2,{value:Promise.reject(n)}];case 5:return[2]}})},s=this,t.label=1;case 1:return[5,e()];case 2:if("object"==typeof(e=t.sent()))return[2,e.value];t.label=3;case 3:return[3,1];case 4:return[2]}})})},gu.prototype.close=function(){this.db&&this.db.close(),this.db=void 0},gu),fu=(Object.defineProperty(yu.prototype,"isDone",{get:function(){return this.Vt},enumerable:!1,configurable:!0}),Object.defineProperty(yu.prototype,"Dt",{get:function(){return this.St},enumerable:!1,configurable:!0}),Object.defineProperty(yu.prototype,"cursor",{set:function(t){this.Pt=t},enumerable:!1,configurable:!0}),yu.prototype.done=function(){this.Vt=!0},yu.prototype.Ct=function(t){this.St=t},yu.prototype.delete=function(){return Iu(this.Pt.delete())},yu),du=(n(pu,ou=Mr),pu);function pu(t,e){var n=this;return(n=ou.call(this,Pr.UNAVAILABLE,"IndexedDB transaction '"+t+"' failed: "+e)||this).name="IndexedDbTransactionError",n}function yu(t){this.Pt=t,this.Vt=!1,this.St=null}function gu(t,e,n){this.name=t,this.version=e,this.wt=n,12.2===gu._t(h())&&Br("Firestore persistence suffers from a bug in iOS 12.2 Safari that may cause your app to stop working. See https://stackoverflow.com/q/56496296/110915 for details and a potential workaround.")}function mu(e,t){var n=this;this.action=e,this.transaction=t,this.aborted=!1,this.ft=new uu,this.transaction.oncomplete=function(){n.ft.resolve()},this.transaction.onabort=function(){t.error?n.ft.reject(new du(e,t.error)):n.ft.resolve()},this.transaction.onerror=function(t){t=Su(t.target.error);n.ft.reject(new du(e,t))}}function vu(t){var e=this;this.nextCallback=null,this.catchCallback=null,this.result=void 0,this.error=void 0,this.isDone=!1,this.callbackAttached=!1,t(function(t){e.isDone=!0,e.result=t,e.nextCallback&&e.nextCallback(t)},function(t){e.isDone=!0,e.error=t,e.catchCallback&&e.catchCallback(t)})}function wu(){this.onCommittedListeners=[]}function bu(t){return"IndexedDbTransactionError"===t.name}var Eu=(Tu.prototype.put=function(t,e){t=void 0!==e?(qr("SimpleDb","PUT",this.store.name,t,e),this.store.put(e,t)):(qr("SimpleDb","PUT",this.store.name,"<auto-key>",t),this.store.put(t));return Iu(t)},Tu.prototype.add=function(t){return qr("SimpleDb","ADD",this.store.name,t,t),Iu(this.store.add(t))},Tu.prototype.get=function(e){var n=this;return Iu(this.store.get(e)).next(function(t){return qr("SimpleDb","GET",n.store.name,e,t=void 0===t?null:t),t})},Tu.prototype.delete=function(t){return qr("SimpleDb","DELETE",this.store.name,t),Iu(this.store.delete(t))},Tu.prototype.count=function(){return qr("SimpleDb","COUNT",this.store.name),Iu(this.store.count())},Tu.prototype.Nt=function(t,e){var e=this.cursor(this.options(t,e)),n=[];return this.xt(e,function(t,e){n.push(e)}).next(function(){return n})},Tu.prototype.Ft=function(t,e){qr("SimpleDb","DELETE ALL",this.store.name);e=this.options(t,e);e.kt=!1;e=this.cursor(e);return this.xt(e,function(t,e,n){return n.delete()})},Tu.prototype.$t=function(t,e){e?n=t:(n={},e=t);var n=this.cursor(n);return this.xt(n,e)},Tu.prototype.Ot=function(r){var t=this.cursor({});return new cu(function(n,e){t.onerror=function(t){t=Su(t.target.error);e(t)},t.onsuccess=function(t){var e=t.target.result;e?r(e.primaryKey,e.value).next(function(t){t?e.continue():n()}):n()}})},Tu.prototype.xt=function(t,i){var o=[];return new cu(function(r,e){t.onerror=function(t){e(t.target.error)},t.onsuccess=function(t){var e,n=t.target.result;n?(e=new fu(n),(t=i(n.primaryKey,n.value,e))instanceof cu&&(t=t.catch(function(t){return e.done(),cu.reject(t)}),o.push(t)),e.isDone?r():null===e.Dt?n.continue():n.continue(e.Dt)):r()}}).next(function(){return cu.waitFor(o)})},Tu.prototype.options=function(t,e){var n;return void 0!==t&&("string"==typeof t?n=t:e=t),{index:n,range:e}},Tu.prototype.cursor=function(t){var e="next";if(t.reverse&&(e="prev"),t.index){var n=this.store.index(t.index);return t.kt?n.openKeyCursor(t.range,e):n.openCursor(t.range,e)}return this.store.openCursor(t.range,e)},Tu);function Tu(t){this.store=t}function Iu(t){return new cu(function(e,n){t.onsuccess=function(t){t=t.target.result;e(t)},t.onerror=function(t){t=Su(t.target.error);n(t)}})}var _u=!1;function Su(t){var e=lu._t(h());if(12.2<=e&&e<13){e="An internal error was encountered in the Indexed Database server";if(0<=t.message.indexOf(e)){var n=new Mr("internal","IOS_INDEXEDDB_BUG1: IndexedDb has thrown '"+e+"'. This is likely due to an unavoidable bug in iOS. See https://stackoverflow.com/q/56496296/110915 for details and a potential workaround.");return _u||(_u=!0,setTimeout(function(){throw n},0)),n}}return t}var Au,Du=(n(Nu,Au=N),Nu);function Nu(t,e){var n=this;return(n=Au.call(this)||this).Mt=t,n.currentSequenceNumber=e,n}function Cu(t,e){return lu.It(t.Mt,e)}var ku=(Mu.prototype.applyToRemoteDocument=function(t,e){for(var n,r,i,o,s,a,u=e.mutationResults,c=0;c<this.mutations.length;c++){var h=this.mutations[c];h.key.isEqual(t.key)&&(n=h,r=t,i=u[c],h=a=s=o=void 0,n instanceof vs?(s=r,a=i,h=(o=n).value.clone(),o=Is(o.fieldTransforms,s,a.transformResults),h.setAll(o),s.convertToFoundDocument(a.version,h).setHasCommittedMutations()):n instanceof ws?(o=r,s=i,fs((a=n).precondition,o)?(h=Is(a.fieldTransforms,o,s.transformResults),(n=o.data).setAll(Ts(a)),n.setAll(h),o.convertToFoundDocument(s.version,n).setHasCommittedMutations()):o.convertToUnknownDocument(s.version)):r.convertToNoDocument(i.version).setHasCommittedMutations())}},Mu.prototype.applyToLocalView=function(t){for(var e=0,n=this.baseMutations;e<n.length;e++)(r=n[e]).key.isEqual(t.key)&&ds(r,t,this.localWriteTime);for(var r,i=0,o=this.mutations;i<o.length;i++)(r=o[i]).key.isEqual(t.key)&&ds(r,t,this.localWriteTime)},Mu.prototype.applyToLocalDocumentSet=function(n){var r=this;this.mutations.forEach(function(t){var e=n.get(t.key),t=e;r.applyToLocalView(t),e.isValidDocument()||t.convertToNoDocument(Jr.min())})},Mu.prototype.keys=function(){return this.mutations.reduce(function(t,e){return t.add(e.key)},Xs())},Mu.prototype.isEqual=function(t){return this.batchId===t.batchId&&Yr(this.mutations,t.mutations,ps)&&Yr(this.baseMutations,t.baseMutations,ps)},Mu),Ru=(Pu.from=function(t,e,n){Qr(t.mutations.length===n.length);for(var r=Ws,i=t.mutations,o=0;o<i.length;o++)r=r.insert(i[o].key,n[o].version);return new Pu(t,e,n,r)},Pu),xu=(Lu.prototype.withSequenceNumber=function(t){return new Lu(this.target,this.targetId,this.purpose,t,this.snapshotVersion,this.lastLimboFreeSnapshotVersion,this.resumeToken)},Lu.prototype.withResumeToken=function(t,e){return new Lu(this.target,this.targetId,this.purpose,this.sequenceNumber,e,this.lastLimboFreeSnapshotVersion,t)},Lu.prototype.withLastLimboFreeSnapshotVersion=function(t){return new Lu(this.target,this.targetId,this.purpose,this.sequenceNumber,this.snapshotVersion,t,this.resumeToken)},Lu),Ou=function(t){this.Lt=t};function Lu(t,e,n,r,i,o,s){void 0===i&&(i=Jr.min()),void 0===o&&(o=Jr.min()),void 0===s&&(s=hi.EMPTY_BYTE_STRING),this.target=t,this.targetId=e,this.purpose=n,this.sequenceNumber=r,this.snapshotVersion=i,this.lastLimboFreeSnapshotVersion=o,this.resumeToken=s}function Pu(t,e,n,r){this.batch=t,this.commitVersion=e,this.mutationResults=n,this.docVersions=r}function Mu(t,e,n,r){this.batchId=t,this.localWriteTime=e,this.baseMutations=n,this.mutations=r}function Fu(t,e){if(e.document)return Aa(t.Lt,e.document,!!e.hasCommittedMutations);if(e.noDocument){var n=Si.fromSegments(e.noDocument.path),r=ju(e.noDocument.readTime),n=ji.newNoDocument(n,r);return e.hasCommittedMutations?n.setHasCommittedMutations():n}if(e.unknownDocument){n=Si.fromSegments(e.unknownDocument.path),r=ju(e.unknownDocument.version);return ji.newUnknownDocument(n,r)}return Gr()}function Vu(t,e,n){var r=Uu(n),n=e.key.path.popLast().toArray();if(e.isFoundDocument()){var i={name:wa(o=t.Lt,(s=e).key),fields:s.data.value.mapValue.fields,updateTime:pa(o,s.version.toTimestamp())},o=e.hasCommittedMutations;return new Xa(null,null,i,o,r,n)}if(e.isNoDocument()){var s=e.key.path.toArray(),i=Bu(e.version),o=e.hasCommittedMutations;return new Xa(null,new Wa(s,i),null,o,r,n)}if(e.isUnknownDocument()){o=e.key.path.toArray(),e=Bu(e.version);return new Xa(new Ya(o,e),null,null,!0,r,n)}return Gr()}function Uu(t){t=t.toTimestamp();return[t.seconds,t.nanoseconds]}function qu(t){t=new $r(t[0],t[1]);return Jr.fromTimestamp(t)}function Bu(t){t=t.toTimestamp();return new Ka(t.seconds,t.nanoseconds)}function ju(t){t=new $r(t.seconds,t.nanoseconds);return Jr.fromTimestamp(t)}function Ku(e,t){for(var n=(t.baseMutations||[]).map(function(t){return Na(e.Lt,t)}),r=0;r<t.mutations.length-1;++r){var i,o=t.mutations[r];r+1<t.mutations.length&&void 0!==t.mutations[r+1].transform&&(i=t.mutations[r+1],o.updateTransforms=i.transform.fieldTransforms,t.mutations.splice(r+1,1),++r)}var s=t.mutations.map(function(t){return Na(e.Lt,t)}),a=$r.fromMillis(t.localWriteTimeMs);return new ku(t.batchId,a,n,s)}function Gu(t){var e=ju(t.readTime),n=void 0!==t.lastLimboFreeSnapshotVersion?ju(t.lastLimboFreeSnapshotVersion):Jr.min(),r=void 0!==t.query.documents?(Qr(1===(r=t.query).documents.length),Po(No(Ta(r.documents[0])))):Po(Ra(t.query));return new xu(r,t.targetId,0,t.lastListenSequenceNumber,e,n,hi.fromBase64String(t.resumeToken))}function Qu(t,e){var n=Bu(e.snapshotVersion),r=Bu(e.lastLimboFreeSnapshotVersion),i=(Wi(e.target)?Ca:ka)(t.Lt,e.target),t=e.resumeToken.toBase64();return new Ja(e.targetId,Hi(e.target),n,t,e.sequenceNumber,r,i)}function Hu(t){var e=Ra({parent:t.parent,structuredQuery:t.structuredQuery});return"LAST"===t.limitType?Mo(e,e.limit,"L"):e}var zu=(Wu.prototype.getBundleMetadata=function(t,e){return Yu(t).get(e).next(function(t){if(t)return{id:(t=t).bundleId,createTime:ju(t.createTime),version:t.version}})},Wu.prototype.saveBundleMetadata=function(t,e){return Yu(t).put({bundleId:(e=e).id,createTime:Bu(ga(e.createTime)),version:e.version})},Wu.prototype.getNamedQuery=function(t,e){return Xu(t).get(e).next(function(t){if(t)return{name:(t=t).name,query:Hu(t.bundledQuery),readTime:ju(t.readTime)}})},Wu.prototype.saveNamedQuery=function(t,e){return Xu(t).put({name:(e=e).name,readTime:Bu(ga(e.readTime)),bundledQuery:e.bundledQuery})},Wu);function Wu(){}function Yu(t){return Cu(t,ru.store)}function Xu(t){return Cu(t,iu.store)}var $u=(nc.prototype.addToCollectionParentIndex=function(t,e){return this.Bt.add(e),cu.resolve()},nc.prototype.getCollectionParents=function(t,e){return cu.resolve(this.Bt.getEntries(e))},nc),Ju=(ec.prototype.add=function(t){var e=t.lastSegment(),n=t.popLast(),r=this.index[e]||new js(si.comparator),t=!r.has(n);return this.index[e]=r.add(n),t},ec.prototype.has=function(t){var e=t.lastSegment(),t=t.popLast(),e=this.index[e];return e&&e.has(t)},ec.prototype.getEntries=function(t){return(this.index[t]||new js(si.comparator)).toArray()},ec),Zu=(tc.prototype.addToCollectionParentIndex=function(t,e){var n=this;if(this.qt.has(e))return cu.resolve();var r=e.lastSegment(),i=e.popLast();t.addOnCommittedListener(function(){n.qt.add(e)});i={collectionId:r,parent:Ua(i)};return rc(t).put(i)},tc.prototype.getCollectionParents=function(t,i){var o=[],e=IDBKeyRange.bound([i,""],[Xr(i),""],!1,!0);return rc(t).Nt(e).next(function(t){for(var e=0,n=t;e<n.length;e++){var r=n[e];if(r.collectionId!==i)break;o.push(Ba(r.parent))}return o})},tc);function tc(){this.qt=new Ju}function ec(){this.index={}}function nc(){this.Bt=new Ju}function rc(t){return Cu(t,eu.store)}var ic={didRun:!1,sequenceNumbersCollected:0,targetsRemoved:0,documentsRemoved:0},oc=(sc.withCacheSize=function(t){return new sc(t,sc.DEFAULT_COLLECTION_PERCENTILE,sc.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT)},sc);function sc(t,e,n){this.cacheSizeCollectionThreshold=t,this.percentileToCollect=e,this.maximumSequenceNumbersToCollect=n}function ac(t,e,n){var r=t.store(Qa.store),i=t.store(Ha.store),o=[],t=IDBKeyRange.only(n.batchId),s=0,t=r.$t({range:t},function(t,e,n){return s++,n.delete()});o.push(t.next(function(){Qr(1===s)}));for(var a=[],u=0,c=n.mutations;u<c.length;u++){var h=c[u],l=Ha.key(e,h.key.path,n.batchId);o.push(i.delete(l)),a.push(h.key)}return cu.waitFor(o).next(function(){return a})}function uc(t){if(!t)return 0;var e;if(t.document)e=t.document;else if(t.unknownDocument)e=t.unknownDocument;else{if(!t.noDocument)throw Gr();e=t.noDocument}return JSON.stringify(e).length}oc.DEFAULT_COLLECTION_PERCENTILE=10,oc.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT=1e3,oc.DEFAULT=new oc(41943040,oc.DEFAULT_COLLECTION_PERCENTILE,oc.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT),oc.DISABLED=new oc(-1,0,0);var cc=(hc.Qt=function(t,e,n,r){return Qr(""!==t.uid),new hc(t.isAuthenticated()?t.uid:"",e,n,r)},hc.prototype.checkEmpty=function(t){var r=!0,e=IDBKeyRange.bound([this.userId,Number.NEGATIVE_INFINITY],[this.userId,Number.POSITIVE_INFINITY]);return fc(t).$t({index:Qa.userMutationsIndex,range:e},function(t,e,n){r=!1,n.done()}).next(function(){return r})},hc.prototype.addMutationBatch=function(p,y,g,m){var v=this,w=dc(p),b=fc(p);return b.add({}).next(function(t){Qr("number"==typeof t);for(var e,n,r,i,o,s=new ku(t,y,g,m),a=(e=v.R,n=v.userId,i=(r=s).baseMutations.map(function(t){return Da(e.Lt,t)}),o=r.mutations.map(function(t){return Da(e.Lt,t)}),new Qa(n,r.batchId,r.localWriteTime.toMillis(),i,o)),u=[],c=new js(function(t,e){return Wr(t.canonicalString(),e.canonicalString())}),h=0,l=m;h<l.length;h++){var f=l[h],d=Ha.key(v.userId,f.key.path,t),c=c.add(f.key.path.popLast());u.push(b.put(a)),u.push(w.put(d,Ha.PLACEHOLDER))}return c.forEach(function(t){u.push(v.Ut.addToCollectionParentIndex(p,t))}),p.addOnCommittedListener(function(){v.Kt[t]=s.keys()}),cu.waitFor(u).next(function(){return s})})},hc.prototype.lookupMutationBatch=function(t,e){var n=this;return fc(t).get(e).next(function(t){return t?(Qr(t.userId===n.userId),Ku(n.R,t)):null})},hc.prototype.jt=function(t,e){var n=this;return this.Kt[e]?cu.resolve(this.Kt[e]):this.lookupMutationBatch(t,e).next(function(t){if(t){t=t.keys();return n.Kt[e]=t}return null})},hc.prototype.getNextMutationBatchAfterBatchId=function(t,e){var r=this,i=e+1,e=IDBKeyRange.lowerBound([this.userId,i]),o=null;return fc(t).$t({index:Qa.userMutationsIndex,range:e},function(t,e,n){e.userId===r.userId&&(Qr(e.batchId>=i),o=Ku(r.R,e)),n.done()}).next(function(){return o})},hc.prototype.getHighestUnacknowledgedBatchId=function(t){var e=IDBKeyRange.upperBound([this.userId,Number.POSITIVE_INFINITY]),r=-1;return fc(t).$t({index:Qa.userMutationsIndex,range:e,reverse:!0},function(t,e,n){r=e.batchId,n.done()}).next(function(){return r})},hc.prototype.getAllMutationBatches=function(t){var e=this,n=IDBKeyRange.bound([this.userId,-1],[this.userId,Number.POSITIVE_INFINITY]);return fc(t).Nt(Qa.userMutationsIndex,n).next(function(t){return t.map(function(t){return Ku(e.R,t)})})},hc.prototype.getAllMutationBatchesAffectingDocumentKey=function(o,s){var a=this,t=Ha.prefixForPath(this.userId,s.path),t=IDBKeyRange.lowerBound(t),u=[];return dc(o).$t({range:t},function(t,e,n){var r=t[0],i=t[1],t=t[2],i=Ba(i);if(r===a.userId&&s.path.isEqual(i))return fc(o).get(t).next(function(t){if(!t)throw Gr();Qr(t.userId===a.userId),u.push(Ku(a.R,t))});n.done()}).next(function(){return u})},hc.prototype.getAllMutationBatchesAffectingDocumentKeys=function(e,t){var s=this,a=new js(Wr),n=[];return t.forEach(function(o){var t=Ha.prefixForPath(s.userId,o.path),t=IDBKeyRange.lowerBound(t),t=dc(e).$t({range:t},function(t,e,n){var r=t[0],i=t[1],t=t[2],i=Ba(i);r===s.userId&&o.path.isEqual(i)?a=a.add(t):n.done()});n.push(t)}),cu.waitFor(n).next(function(){return s.Wt(e,a)})},hc.prototype.getAllMutationBatchesAffectingQuery=function(t,e){var o=this,s=e.path,a=s.length+1,e=Ha.prefixForPath(this.userId,s),e=IDBKeyRange.lowerBound(e),u=new js(Wr);return dc(t).$t({range:e},function(t,e,n){var r=t[0],i=t[1],t=t[2],i=Ba(i);r===o.userId&&s.isPrefixOf(i)?i.length===a&&(u=u.add(t)):n.done()}).next(function(){return o.Wt(t,u)})},hc.prototype.Wt=function(e,t){var n=this,r=[],i=[];return t.forEach(function(t){i.push(fc(e).get(t).next(function(t){if(null===t)throw Gr();Qr(t.userId===n.userId),r.push(Ku(n.R,t))}))}),cu.waitFor(i).next(function(){return r})},hc.prototype.removeMutationBatch=function(e,n){var r=this;return ac(e.Mt,this.userId,n).next(function(t){return e.addOnCommittedListener(function(){r.Gt(n.batchId)}),cu.forEach(t,function(t){return r.referenceDelegate.markPotentiallyOrphaned(e,t)})})},hc.prototype.Gt=function(t){delete this.Kt[t]},hc.prototype.performConsistencyCheck=function(e){var i=this;return this.checkEmpty(e).next(function(t){if(!t)return cu.resolve();var t=IDBKeyRange.lowerBound(Ha.prefixForUser(i.userId)),r=[];return dc(e).$t({range:t},function(t,e,n){t[0]===i.userId?(t=Ba(t[1]),r.push(t)):n.done()}).next(function(){Qr(0===r.length)})})},hc.prototype.containsKey=function(t,e){return lc(t,this.userId,e)},hc.prototype.zt=function(t){var e=this;return pc(t).get(this.userId).next(function(t){return t||new Ga(e.userId,-1,"")})},hc);function hc(t,e,n,r){this.userId=t,this.R=e,this.Ut=n,this.referenceDelegate=r,this.Kt={}}function lc(t,o,e){var e=Ha.prefixForPath(o,e.path),s=e[1],e=IDBKeyRange.lowerBound(e),a=!1;return dc(t).$t({range:e,kt:!0},function(t,e,n){var r=t[0],i=t[1];t[2],r===o&&i===s&&(a=!0),n.done()}).next(function(){return a})}function fc(t){return Cu(t,Qa.store)}function dc(t){return Cu(t,Ha.store)}function pc(t){return Cu(t,Ga.store)}var yc=(vc.prototype.next=function(){return this.Ht+=2,this.Ht},vc.Jt=function(){return new vc(0)},vc.Yt=function(){return new vc(-1)},vc),gc=(mc.prototype.allocateTargetId=function(n){var r=this;return this.Xt(n).next(function(t){var e=new yc(t.highestTargetId);return t.highestTargetId=e.next(),r.Zt(n,t).next(function(){return t.highestTargetId})})},mc.prototype.getLastRemoteSnapshotVersion=function(t){return this.Xt(t).next(function(t){return Jr.fromTimestamp(new $r(t.lastRemoteSnapshotVersion.seconds,t.lastRemoteSnapshotVersion.nanoseconds))})},mc.prototype.getHighestSequenceNumber=function(t){return this.Xt(t).next(function(t){return t.highestListenSequenceNumber})},mc.prototype.setTargetsMetadata=function(e,n,r){var i=this;return this.Xt(e).next(function(t){return t.highestListenSequenceNumber=n,r&&(t.lastRemoteSnapshotVersion=r.toTimestamp()),n>t.highestListenSequenceNumber&&(t.highestListenSequenceNumber=n),i.Zt(e,t)})},mc.prototype.addTargetData=function(e,n){var r=this;return this.te(e,n).next(function(){return r.Xt(e).next(function(t){return t.targetCount+=1,r.ee(n,t),r.Zt(e,t)})})},mc.prototype.updateTargetData=function(t,e){return this.te(t,e)},mc.prototype.removeTargetData=function(e,t){var n=this;return this.removeMatchingKeysForTargetId(e,t.targetId).next(function(){return wc(e).delete(t.targetId)}).next(function(){return n.Xt(e)}).next(function(t){return Qr(0<t.targetCount),--t.targetCount,n.Zt(e,t)})},mc.prototype.removeTargets=function(n,r,i){var o=this,s=0,a=[];return wc(n).$t(function(t,e){e=Gu(e);e.sequenceNumber<=r&&null===i.get(e.targetId)&&(s++,a.push(o.removeTargetData(n,e)))}).next(function(){return cu.waitFor(a)}).next(function(){return s})},mc.prototype.forEachTarget=function(t,n){return wc(t).$t(function(t,e){e=Gu(e);n(e)})},mc.prototype.Xt=function(t){return bc(t).get(tu.key).next(function(t){return Qr(null!==t),t})},mc.prototype.Zt=function(t,e){return bc(t).put(tu.key,e)},mc.prototype.te=function(t,e){return wc(t).put(Qu(this.R,e))},mc.prototype.ee=function(t,e){var n=!1;return t.targetId>e.highestTargetId&&(e.highestTargetId=t.targetId,n=!0),t.sequenceNumber>e.highestListenSequenceNumber&&(e.highestListenSequenceNumber=t.sequenceNumber,n=!0),n},mc.prototype.getTargetCount=function(t){return this.Xt(t).next(function(t){return t.targetCount})},mc.prototype.getTargetData=function(t,r){var e=Hi(r),e=IDBKeyRange.bound([e,Number.NEGATIVE_INFINITY],[e,Number.POSITIVE_INFINITY]),i=null;return wc(t).$t({range:e,index:Ja.queryTargetsIndexName},function(t,e,n){e=Gu(e);zi(r,e.target)&&(i=e,n.done())}).next(function(){return i})},mc.prototype.addMatchingKeys=function(n,t,r){var i=this,o=[],s=Ec(n);return t.forEach(function(t){var e=Ua(t.path);o.push(s.put(new Za(r,e))),o.push(i.referenceDelegate.addReference(n,r,t))}),cu.waitFor(o)},mc.prototype.removeMatchingKeys=function(n,t,r){var i=this,o=Ec(n);return cu.forEach(t,function(t){var e=Ua(t.path);return cu.waitFor([o.delete([r,e]),i.referenceDelegate.removeReference(n,r,t)])})},mc.prototype.removeMatchingKeysForTargetId=function(t,e){t=Ec(t),e=IDBKeyRange.bound([e],[e+1],!1,!0);return t.delete(e)},mc.prototype.getMatchingKeysForTargetId=function(t,e){var e=IDBKeyRange.bound([e],[e+1],!1,!0),t=Ec(t),r=Xs();return t.$t({range:e,kt:!0},function(t,e,n){t=Ba(t[1]),t=new Si(t);r=r.add(t)}).next(function(){return r})},mc.prototype.containsKey=function(t,e){var e=Ua(e.path),e=IDBKeyRange.bound([e],[Xr(e)],!1,!0),i=0;return Ec(t).$t({index:Za.documentTargetsIndex,kt:!0,range:e},function(t,e,n){var r=t[0];t[1],0!==r&&(i++,n.done())}).next(function(){return 0<i})},mc.prototype.lt=function(t,e){return wc(t).get(e).next(function(t){return t?Gu(t):null})},mc);function mc(t,e){this.referenceDelegate=t,this.R=e}function vc(t){this.Ht=t}function wc(t){return Cu(t,Ja.store)}function bc(t){return Cu(t,tu.store)}function Ec(t){return Cu(t,Za.store)}function Tc(e){return y(this,void 0,void 0,function(){return g(this,function(t){if(e.code!==Pr.FAILED_PRECONDITION||e.message!==au)throw e;return qr("LocalStore","Unexpectedly lost primary lease"),[2]})})}function Ic(t,e){var n=t[0],r=t[1],t=e[0],e=e[1],t=Wr(n,t);return 0===t?Wr(r,e):t}var _c=(Rc.prototype.ie=function(){return++this.se},Rc.prototype.re=function(t){var e=[t,this.ie()];this.buffer.size<this.ne?this.buffer=this.buffer.add(e):Ic(e,t=this.buffer.last())<0&&(this.buffer=this.buffer.delete(t).add(e))},Object.defineProperty(Rc.prototype,"maxValue",{get:function(){return this.buffer.last()[0]},enumerable:!1,configurable:!0}),Rc),Sc=(kc.prototype.start=function(t){-1!==this.garbageCollector.params.cacheSizeCollectionThreshold&&this.ue(t)},kc.prototype.stop=function(){this.ce&&(this.ce.cancel(),this.ce=null)},Object.defineProperty(kc.prototype,"started",{get:function(){return null!==this.ce},enumerable:!1,configurable:!0}),kc.prototype.ue=function(n){var t=this,e=this.oe?3e5:6e4;qr("LruGarbageCollector","Garbage collection scheduled in "+e+"ms"),this.ce=this.asyncQueue.enqueueAfterDelay("lru_garbage_collection",e,function(){return y(t,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:this.ce=null,this.oe=!0,t.label=1;case 1:return t.trys.push([1,3,,7]),[4,n.collectGarbage(this.garbageCollector)];case 2:return t.sent(),[3,7];case 3:return bu(e=t.sent())?(qr("LruGarbageCollector","Ignoring IndexedDB error during garbage collection: ",e),[3,6]):[3,4];case 4:return[4,Tc(e)];case 5:t.sent(),t.label=6;case 6:return[3,7];case 7:return[4,this.ue(n)];case 8:return t.sent(),[2]}})})})},kc),Ac=(Cc.prototype.calculateTargetCount=function(t,e){return this.ae.he(t).next(function(t){return Math.floor(e/100*t)})},Cc.prototype.nthSequenceNumber=function(t,e){var n=this;if(0===e)return cu.resolve(xr.o);var r=new _c(e);return this.ae.forEachTarget(t,function(t){return r.re(t.sequenceNumber)}).next(function(){return n.ae.le(t,function(t){return r.re(t)})}).next(function(){return r.maxValue})},Cc.prototype.removeTargets=function(t,e,n){return this.ae.removeTargets(t,e,n)},Cc.prototype.removeOrphanedDocuments=function(t,e){return this.ae.removeOrphanedDocuments(t,e)},Cc.prototype.collect=function(e,n){var r=this;return-1===this.params.cacheSizeCollectionThreshold?(qr("LruGarbageCollector","Garbage collection skipped; disabled"),cu.resolve(ic)):this.getCacheSize(e).next(function(t){return t<r.params.cacheSizeCollectionThreshold?(qr("LruGarbageCollector","Garbage collection skipped; Cache size "+t+" is lower than threshold "+r.params.cacheSizeCollectionThreshold),ic):r.fe(e,n)})},Cc.prototype.getCacheSize=function(t){return this.ae.getCacheSize(t)},Cc.prototype.fe=function(e,n){var r,i,o,s,a,u,c,h=this,l=Date.now();return this.calculateTargetCount(e,this.params.percentileToCollect).next(function(t){return i=t>h.params.maximumSequenceNumbersToCollect?(qr("LruGarbageCollector","Capping sequence numbers to collect down to the maximum of "+h.params.maximumSequenceNumbersToCollect+" from "+t),h.params.maximumSequenceNumbersToCollect):t,s=Date.now(),h.nthSequenceNumber(e,i)}).next(function(t){return r=t,a=Date.now(),h.removeTargets(e,r,n)}).next(function(t){return o=t,u=Date.now(),h.removeOrphanedDocuments(e,r)}).next(function(t){return c=Date.now(),Ur()<=p.DEBUG&&qr("LruGarbageCollector","LRU Garbage Collection\n\tCounted targets in "+(s-l)+"ms\n\tDetermined least recently used "+i+" in "+(a-s)+"ms\n\tRemoved "+o+" targets in "+(u-a)+"ms\n\tRemoved "+t+" documents in "+(c-u)+"ms\nTotal Duration: "+(c-l)+"ms"),cu.resolve({didRun:!0,sequenceNumbersCollected:i,targetsRemoved:o,documentsRemoved:t})})},Cc),Dc=(Nc.prototype.he=function(t){var n=this.de(t);return this.db.getTargetCache().getTargetCount(t).next(function(e){return n.next(function(t){return e+t})})},Nc.prototype.de=function(t){var e=0;return this.le(t,function(t){e++}).next(function(){return e})},Nc.prototype.forEachTarget=function(t,e){return this.db.getTargetCache().forEachTarget(t,e)},Nc.prototype.le=function(t,n){return this.we(t,function(t,e){return n(e)})},Nc.prototype.addReference=function(t,e,n){return xc(t,n)},Nc.prototype.removeReference=function(t,e,n){return xc(t,n)},Nc.prototype.removeTargets=function(t,e,n){return this.db.getTargetCache().removeTargets(t,e,n)},Nc.prototype.markPotentiallyOrphaned=xc,Nc.prototype._e=function(t,e){return r=e,i=!1,pc(n=t).Ot(function(t){return lc(n,t,r).next(function(t){return t&&(i=!0),cu.resolve(!t)})}).next(function(){return i});var n,r,i},Nc.prototype.removeOrphanedDocuments=function(n,r){var i=this,o=this.db.getRemoteDocumentCache().newChangeBuffer(),s=[],a=0;return this.we(n,function(e,t){t<=r&&(t=i._e(n,e).next(function(t){if(!t)return a++,o.getEntry(n,e).next(function(){return o.removeEntry(e),Ec(n).delete([0,Ua(e.path)])})}),s.push(t))}).next(function(){return cu.waitFor(s)}).next(function(){return o.apply(n)}).next(function(){return a})},Nc.prototype.removeTarget=function(t,e){e=e.withSequenceNumber(t.currentSequenceNumber);return this.db.getTargetCache().updateTargetData(t,e)},Nc.prototype.updateLimboDocument=xc,Nc.prototype.we=function(t,r){var i,t=Ec(t),o=xr.o;return t.$t({index:Za.documentTargetsIndex},function(t,e){var n=t[0];t[1];t=e.path,e=e.sequenceNumber;0===n?(o!==xr.o&&r(new Si(Ba(i)),o),o=e,i=t):o=xr.o}).next(function(){o!==xr.o&&r(new Si(Ba(i)),o)})},Nc.prototype.getCacheSize=function(t){return this.db.getRemoteDocumentCache().getSize(t)},Nc);function Nc(t,e){this.db=t,this.garbageCollector=new Ac(this,e)}function Cc(t,e){this.ae=t,this.params=e}function kc(t,e){this.garbageCollector=t,this.asyncQueue=e,this.oe=!1,this.ce=null}function Rc(t){this.ne=t,this.buffer=new js(Ic),this.se=0}function xc(t,e){return Ec(t).put((t=t.currentSequenceNumber,new Za(0,Ua(e.path),t)))}var Oc,Lc=(qc.prototype.get=function(t){var e=this.mapKeyFn(t),e=this.inner[e];if(void 0!==e)for(var n=0,r=e;n<r.length;n++){var i=r[n],o=i[0],i=i[1];if(this.equalsFn(o,t))return i}},qc.prototype.has=function(t){return void 0!==this.get(t)},qc.prototype.set=function(t,e){var n=this.mapKeyFn(t),r=this.inner[n];if(void 0!==r){for(var i=0;i<r.length;i++)if(this.equalsFn(r[i][0],t))return void(r[i]=[t,e]);r.push([t,e])}else this.inner[n]=[[t,e]]},qc.prototype.delete=function(t){var e=this.mapKeyFn(t),n=this.inner[e];if(void 0===n)return!1;for(var r=0;r<n.length;r++)if(this.equalsFn(n[r][0],t))return 1===n.length?delete this.inner[e]:n.splice(r,1),!0;return!1},qc.prototype.forEach=function(s){ni(this.inner,function(t,e){for(var n=0,r=e;n<r.length;n++){var i=r[n],o=i[0],i=i[1];s(o,i)}})},qc.prototype.isEmpty=function(){return ri(this.inner)},qc),I=(Uc.prototype.getReadTime=function(t){t=this.changes.get(t);return t?t.readTime:Jr.min()},Uc.prototype.addEntry=function(t,e){this.assertNotApplied(),this.changes.set(t.key,{document:t,readTime:e})},Uc.prototype.removeEntry=function(t,e){void 0===e&&(e=null),this.assertNotApplied(),this.changes.set(t,{document:ji.newInvalidDocument(t),readTime:e})},Uc.prototype.getEntry=function(t,e){this.assertNotApplied();var n=this.changes.get(e);return void 0!==n?cu.resolve(n.document):this.getFromCache(t,e)},Uc.prototype.getEntries=function(t,e){return this.getAllFromCache(t,e)},Uc.prototype.apply=function(t){return this.assertNotApplied(),this.changesApplied=!0,this.applyChanges(t)},Uc.prototype.assertNotApplied=function(){},Uc),Pc=(Vc.prototype.addEntry=function(t,e,n){return jc(t).put(Kc(e),n)},Vc.prototype.removeEntry=function(t,e){t=jc(t),e=Kc(e);return t.delete(e)},Vc.prototype.updateMetadata=function(e,n){var r=this;return this.getMetadata(e).next(function(t){return t.byteSize+=n,r.me(e,t)})},Vc.prototype.getEntry=function(t,e){var n=this;return jc(t).get(Kc(e)).next(function(t){return n.ye(e,t)})},Vc.prototype.ge=function(t,e){var n=this;return jc(t).get(Kc(e)).next(function(t){return{document:n.ye(e,t),size:uc(t)}})},Vc.prototype.getEntries=function(t,e){var n=this,r=Gs;return this.pe(t,e,function(t,e){e=n.ye(t,e);r=r.insert(t,e)}).next(function(){return r})},Vc.prototype.Ee=function(t,e){var r=this,i=Gs,o=new Ps(Si.comparator);return this.pe(t,e,function(t,e){var n=r.ye(t,e);i=i.insert(t,n),o=o.insert(t,uc(e))}).next(function(){return{documents:i,Te:o}})},Vc.prototype.pe=function(t,e,i){if(e.isEmpty())return cu.resolve();var n=IDBKeyRange.bound(e.first().path.toArray(),e.last().path.toArray()),o=e.getIterator(),s=o.getNext();return jc(t).$t({range:n},function(t,e,n){for(var r=Si.fromSegments(t);s&&Si.comparator(s,r)<0;)i(s,null),s=o.getNext();s&&s.isEqual(r)&&(i(s,e),s=o.hasNext()?o.getNext():null),s?n.Ct(s.path.toArray()):n.done()}).next(function(){for(;s;)i(s,null),s=o.hasNext()?o.getNext():null})},Vc.prototype.getDocumentsMatchingQuery=function(t,r,e){var n,i=this,o=Gs,s=r.path.length+1,a={};return e.isEqual(Jr.min())?(n=r.path.toArray(),a.range=IDBKeyRange.lowerBound(n)):(n=r.path.toArray(),e=Uu(e),a.range=IDBKeyRange.lowerBound([n,e],!0),a.index=Xa.collectionReadTimeIndex),jc(t).$t(a,function(t,e,n){t.length===s&&(e=Fu(i.R,e),r.path.isPrefixOf(e.key.path)?qo(r,e)&&(o=o.insert(e.key,e)):n.done())}).next(function(){return o})},Vc.prototype.newChangeBuffer=function(t){return new Mc(this,!!t&&t.trackRemovals)},Vc.prototype.getSize=function(t){return this.getMetadata(t).next(function(t){return t.byteSize})},Vc.prototype.getMetadata=function(t){return Bc(t).get($a.key).next(function(t){return Qr(!!t),t})},Vc.prototype.me=function(t,e){return Bc(t).put($a.key,e)},Vc.prototype.ye=function(t,e){if(e){e=Fu(this.R,e);if(!e.isNoDocument()||!e.version.isEqual(Jr.min()))return e}return ji.newInvalidDocument(t)},Vc),Mc=(n(Fc,Oc=I),Fc.prototype.applyChanges=function(i){var o=this,s=[],a=0,u=new js(function(t,e){return Wr(t.canonicalString(),e.canonicalString())});return this.changes.forEach(function(t,e){var n,r=o.Ae.get(t);e.document.isValidDocument()?(n=Vu(o.Ie.R,e.document,o.getReadTime(t)),u=u.add(t.path.popLast()),e=uc(n),a+=e-r,s.push(o.Ie.addEntry(i,t,n))):(a-=r,o.trackRemovals?(r=Vu(o.Ie.R,ji.newNoDocument(t,Jr.min()),o.getReadTime(t)),s.push(o.Ie.addEntry(i,t,r))):s.push(o.Ie.removeEntry(i,t)))}),u.forEach(function(t){s.push(o.Ie.Ut.addToCollectionParentIndex(i,t))}),s.push(this.Ie.updateMetadata(i,a)),cu.waitFor(s)},Fc.prototype.getFromCache=function(t,e){var n=this;return this.Ie.ge(t,e).next(function(t){return n.Ae.set(e,t.size),t.document})},Fc.prototype.getAllFromCache=function(t,e){var n=this;return this.Ie.Ee(t,e).next(function(t){var e=t.documents;return t.Te.forEach(function(t,e){n.Ae.set(t,e)}),e})},Fc);function Fc(t,e){var n=this;return(n=Oc.call(this)||this).Ie=t,n.trackRemovals=e,n.Ae=new Lc(function(t){return t.toString()},function(t,e){return t.isEqual(e)}),n}function Vc(t,e){this.R=t,this.Ut=e}function Uc(){this.changes=new Lc(function(t){return t.toString()},function(t,e){return t.isEqual(e)}),this.changesApplied=!1}function qc(t,e){this.mapKeyFn=t,this.equalsFn=e,this.inner={}}function Bc(t){return Cu(t,$a.store)}function jc(t){return Cu(t,Xa.store)}function Kc(t){return t.path.toArray()}var Gc=(Qc.prototype.Rt=function(e,n,t,r){var i=this;Qr(t<r&&0<=t&&r<=11);var o=new hu("createOrUpgrade",n);t<1&&1<=r&&(e.createObjectStore(ja.store),(s=e).createObjectStore(Ga.store,{keyPath:Ga.keyPath}),s.createObjectStore(Qa.store,{keyPath:Qa.keyPath,autoIncrement:!0}).createIndex(Qa.userMutationsIndex,Qa.userMutationsKeyPath,{unique:!0}),s.createObjectStore(Ha.store),Hc(e),e.createObjectStore(Xa.store));var s,a=cu.resolve();return t<3&&3<=r&&(0!==t&&((s=e).deleteObjectStore(Za.store),s.deleteObjectStore(Ja.store),s.deleteObjectStore(tu.store),Hc(e)),a=a.next(function(){return t=o.store(tu.store),e=new tu(0,0,Jr.min().toTimestamp(),0),t.put(tu.key,e);var t,e})),t<4&&4<=r&&(a=(a=0!==t?a.next(function(){return n=e,(r=o).store(Qa.store).Nt().next(function(t){n.deleteObjectStore(Qa.store),n.createObjectStore(Qa.store,{keyPath:Qa.keyPath,autoIncrement:!0}).createIndex(Qa.userMutationsIndex,Qa.userMutationsKeyPath,{unique:!0});var e=r.store(Qa.store),t=t.map(function(t){return e.put(t)});return cu.waitFor(t)});var n,r}):a).next(function(){e.createObjectStore(nu.store,{keyPath:nu.keyPath})})),t<5&&5<=r&&(a=a.next(function(){return i.Re(o)})),t<6&&6<=r&&(a=a.next(function(){return e.createObjectStore($a.store),i.be(o)})),t<7&&7<=r&&(a=a.next(function(){return i.ve(o)})),t<8&&8<=r&&(a=a.next(function(){return i.Pe(e,o)})),t<9&&9<=r&&(a=a.next(function(){var t;(t=e).objectStoreNames.contains("remoteDocumentChanges")&&t.deleteObjectStore("remoteDocumentChanges"),(t=n.objectStore(Xa.store)).createIndex(Xa.readTimeIndex,Xa.readTimeIndexPath,{unique:!1}),t.createIndex(Xa.collectionReadTimeIndex,Xa.collectionReadTimeIndexPath,{unique:!1})})),t<10&&10<=r&&(a=a.next(function(){return i.Ve(o)})),a=t<11&&11<=r?a.next(function(){e.createObjectStore(ru.store,{keyPath:ru.keyPath}),e.createObjectStore(iu.store,{keyPath:iu.keyPath})}):a},Qc.prototype.be=function(e){var n=0;return e.store(Xa.store).$t(function(t,e){n+=uc(e)}).next(function(){var t=new $a(n);return e.store($a.store).put($a.key,t)})},Qc.prototype.Re=function(n){var r=this,t=n.store(Ga.store),i=n.store(Qa.store);return t.Nt().next(function(t){return cu.forEach(t,function(e){var t=IDBKeyRange.bound([e.userId,-1],[e.userId,e.lastAcknowledgedBatchId]);return i.Nt(Qa.userMutationsIndex,t).next(function(t){return cu.forEach(t,function(t){Qr(t.userId===e.userId);t=Ku(r.R,t);return ac(n,e.userId,t).next(function(){})})})})})},Qc.prototype.ve=function(t){var o=t.store(Za.store),e=t.store(Xa.store);return t.store(tu.store).get(tu.key).next(function(r){var i=[];return e.$t(function(t,e){var n=new si(t),t=[0,Ua(n)];i.push(o.get(t).next(function(t){return t?cu.resolve():o.put(new Za(0,Ua(n),r.highestListenSequenceNumber))}))}).next(function(){return cu.waitFor(i)})})},Qc.prototype.Pe=function(t,e){t.createObjectStore(eu.store,{keyPath:eu.keyPath});function r(t){if(i.add(t)){var e=t.lastSegment(),t=t.popLast();return n.put({collectionId:e,parent:Ua(t)})}}var n=e.store(eu.store),i=new Ju;return e.store(Xa.store).$t({kt:!0},function(t,e){t=new si(t);return r(t.popLast())}).next(function(){return e.store(Ha.store).$t({kt:!0},function(t,e){t[0];var n=t[1];t[2];n=Ba(n);return r(n.popLast())})})},Qc.prototype.Ve=function(t){var n=this,r=t.store(Ja.store);return r.$t(function(t,e){e=Gu(e),e=Qu(n.R,e);return r.put(e)})},Qc);function Qc(t){this.R=t}function Hc(t){t.createObjectStore(Za.store,{keyPath:Za.keyPath}).createIndex(Za.documentTargetsIndex,Za.documentTargetsKeyPath,{unique:!0}),t.createObjectStore(Ja.store,{keyPath:Ja.keyPath}).createIndex(Ja.queryTargetsIndexName,Ja.queryTargetsKeyPath,{unique:!0}),t.createObjectStore(tu.store)}var zc="Failed to obtain exclusive access to the persistence layer. To allow shared access, multi-tab synchronization has to be enabled in all tabs. If you are using `experimentalForceOwningTab:true`, make sure that only one tab has persistence enabled at any given time.",Wc=(Yc.prototype.start=function(){var e=this;return this.je().then(function(){if(!e.isPrimary&&!e.allowTabSynchronization)throw new Mr(Pr.FAILED_PRECONDITION,zc);return e.We(),e.Ge(),e.ze(),e.runTransaction("getHighestListenSequenceNumber","readonly",function(t){return e.qe.getHighestSequenceNumber(t)})}).then(function(t){e.Ne=new xr(t,e.De)}).then(function(){e.xe=!0}).catch(function(t){return e.Be&&e.Be.close(),Promise.reject(t)})},Yc.prototype.He=function(n){var t=this;return this.Me=function(e){return y(t,void 0,void 0,function(){return g(this,function(t){return this.started?[2,n(e)]:[2]})})},n(this.isPrimary)},Yc.prototype.setDatabaseDeletedListener=function(n){var t=this;this.Be.vt(function(e){return y(t,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return null===e.newVersion?[4,n()]:[3,2];case 1:t.sent(),t.label=2;case 2:return[2]}})})})},Yc.prototype.setNetworkEnabled=function(t){var e=this;this.networkEnabled!==t&&(this.networkEnabled=t,this.Se.enqueueAndForget(function(){return y(e,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return this.started?[4,this.je()]:[3,2];case 1:t.sent(),t.label=2;case 2:return[2]}})})}))},Yc.prototype.je=function(){var n=this;return this.runTransaction("updateClientMetadataAndTryBecomePrimary","readwrite",function(e){return $c(e).put(new nu(n.clientId,Date.now(),n.networkEnabled,n.inForeground)).next(function(){if(n.isPrimary)return n.Je(e).next(function(t){t||(n.isPrimary=!1,n.Se.enqueueRetryable(function(){return n.Me(!1)}))})}).next(function(){return n.Ye(e)}).next(function(t){return n.isPrimary&&!t?n.Xe(e).next(function(){return!1}):!!t&&n.Ze(e).next(function(){return!0})})}).catch(function(t){if(bu(t))return qr("IndexedDbPersistence","Failed to extend owner lease: ",t),n.isPrimary;if(!n.allowTabSynchronization)throw t;return qr("IndexedDbPersistence","Releasing owner lease after error during lease refresh",t),!1}).then(function(t){n.isPrimary!==t&&n.Se.enqueueRetryable(function(){return n.Me(t)}),n.isPrimary=t})},Yc.prototype.Je=function(t){var e=this;return Xc(t).get(ja.key).next(function(t){return cu.resolve(e.tn(t))})},Yc.prototype.en=function(t){return $c(t).delete(this.clientId)},Yc.prototype.nn=function(){return y(this,void 0,void 0,function(){var e,n,r,i,o=this;return g(this,function(t){switch(t.label){case 0:return!this.isPrimary||this.sn(this.Oe,18e5)?[3,2]:(this.Oe=Date.now(),[4,this.runTransaction("maybeGarbageCollectMultiClientState","readwrite-primary",function(t){var r=Cu(t,nu.store);return r.Nt().next(function(t){var e=o.rn(t,18e5),n=t.filter(function(t){return-1===e.indexOf(t)});return cu.forEach(n,function(t){return r.delete(t.clientId)}).next(function(){return n})})}).catch(function(){return[]})]);case 1:if(e=t.sent(),this.Qe)for(n=0,r=e;n<r.length;n++)i=r[n],this.Qe.removeItem(this.on(i.clientId));t.label=2;case 2:return[2]}})})},Yc.prototype.ze=function(){var t=this;this.$e=this.Se.enqueueAfterDelay("client_metadata_refresh",4e3,function(){return t.je().then(function(){return t.nn()}).then(function(){return t.ze()})})},Yc.prototype.tn=function(t){return!!t&&t.ownerId===this.clientId},Yc.prototype.Ye=function(e){var r=this;return this.Ce?cu.resolve(!0):Xc(e).get(ja.key).next(function(t){if(null!==t&&r.sn(t.leaseTimestampMs,5e3)&&!r.cn(t.ownerId)){if(r.tn(t)&&r.networkEnabled)return!0;if(!r.tn(t)){if(!t.allowTabSynchronization)throw new Mr(Pr.FAILED_PRECONDITION,zc);return!1}}return!(!r.networkEnabled||!r.inForeground)||$c(e).Nt().next(function(t){return void 0===r.rn(t,5e3).find(function(t){if(r.clientId!==t.clientId){var e=!r.networkEnabled&&t.networkEnabled,n=!r.inForeground&&t.inForeground,t=r.networkEnabled===t.networkEnabled;if(e||n&&t)return!0}return!1})})}).next(function(t){return r.isPrimary!==t&&qr("IndexedDbPersistence","Client "+(t?"is":"is not")+" eligible for a primary lease."),t})},Yc.prototype.shutdown=function(){return y(this,void 0,void 0,function(){var n=this;return g(this,function(t){switch(t.label){case 0:return this.xe=!1,this.un(),this.$e&&(this.$e.cancel(),this.$e=null),this.an(),this.hn(),[4,this.Be.runTransaction("shutdown","readwrite",[ja.store,nu.store],function(t){var e=new Du(t,xr.o);return n.Xe(e).next(function(){return n.en(e)})})];case 1:return t.sent(),this.Be.close(),this.ln(),[2]}})})},Yc.prototype.rn=function(t,e){var n=this;return t.filter(function(t){return n.sn(t.updateTimeMs,e)&&!n.cn(t.clientId)})},Yc.prototype.fn=function(){var e=this;return this.runTransaction("getActiveClients","readonly",function(t){return $c(t).Nt().next(function(t){return e.rn(t,18e5).map(function(t){return t.clientId})})})},Object.defineProperty(Yc.prototype,"started",{get:function(){return this.xe},enumerable:!1,configurable:!0}),Yc.prototype.getMutationQueue=function(t){return cc.Qt(t,this.R,this.Ut,this.referenceDelegate)},Yc.prototype.getTargetCache=function(){return this.qe},Yc.prototype.getRemoteDocumentCache=function(){return this.Ue},Yc.prototype.getIndexManager=function(){return this.Ut},Yc.prototype.getBundleCache=function(){return this.Ke},Yc.prototype.runTransaction=function(e,n,r){var i,o=this;return qr("IndexedDbPersistence","Starting transaction:",e),this.Be.runTransaction(e,"readonly"===n?"readonly":"readwrite",su,function(t){return i=new Du(t,o.Ne?o.Ne.next():xr.o),"readwrite-primary"===n?o.Je(i).next(function(t){return!!t||o.Ye(i)}).next(function(t){if(!t)throw Br("Failed to obtain primary lease for action '"+e+"'."),o.isPrimary=!1,o.Se.enqueueRetryable(function(){return o.Me(!1)}),new Mr(Pr.FAILED_PRECONDITION,au);return r(i)}).next(function(t){return o.Ze(i).next(function(){return t})}):o.dn(i).next(function(){return r(i)})}).then(function(t){return i.raiseOnCommittedEvent(),t})},Yc.prototype.dn=function(t){var e=this;return Xc(t).get(ja.key).next(function(t){if(null!==t&&e.sn(t.leaseTimestampMs,5e3)&&!e.cn(t.ownerId)&&!e.tn(t)&&!(e.Ce||e.allowTabSynchronization&&t.allowTabSynchronization))throw new Mr(Pr.FAILED_PRECONDITION,zc)})},Yc.prototype.Ze=function(t){var e=new ja(this.clientId,this.allowTabSynchronization,Date.now());return Xc(t).put(ja.key,e)},Yc.yt=function(){return lu.yt()},Yc.prototype.Xe=function(t){var e=this,n=Xc(t);return n.get(ja.key).next(function(t){return e.tn(t)?(qr("IndexedDbPersistence","Releasing primary lease."),n.delete(ja.key)):cu.resolve()})},Yc.prototype.sn=function(t,e){var n=Date.now();return!(t<n-e||n<t&&(Br("Detected an update time that is in the future: "+t+" > "+n),1))},Yc.prototype.We=function(){var t=this;null!==this.document&&"function"==typeof this.document.addEventListener&&(this.ke=function(){t.Se.enqueueAndForget(function(){return t.inForeground="visible"===t.document.visibilityState,t.je()})},this.document.addEventListener("visibilitychange",this.ke),this.inForeground="visible"===this.document.visibilityState)},Yc.prototype.an=function(){this.ke&&(this.document.removeEventListener("visibilitychange",this.ke),this.ke=null)},Yc.prototype.Ge=function(){var t,e=this;"function"==typeof(null===(t=this.window)||void 0===t?void 0:t.addEventListener)&&(this.Fe=function(){e.un(),e.Se.enqueueAndForget(function(){return e.shutdown()})},this.window.addEventListener("pagehide",this.Fe))},Yc.prototype.hn=function(){this.Fe&&(this.window.removeEventListener("pagehide",this.Fe),this.Fe=null)},Yc.prototype.cn=function(t){var e;try{var n=null!==(null===(e=this.Qe)||void 0===e?void 0:e.getItem(this.on(t)));return qr("IndexedDbPersistence","Client '"+t+"' "+(n?"is":"is not")+" zombied in LocalStorage"),n}catch(t){return Br("IndexedDbPersistence","Failed to get zombied client id.",t),!1}},Yc.prototype.un=function(){if(this.Qe)try{this.Qe.setItem(this.on(this.clientId),String(Date.now()))}catch(t){Br("Failed to set zombie client id.",t)}},Yc.prototype.ln=function(){if(this.Qe)try{this.Qe.removeItem(this.on(this.clientId))}catch(t){}},Yc.prototype.on=function(t){return"firestore_zombie_"+this.persistenceKey+"_"+t},Yc);function Yc(t,e,n,r,i,o,s,a,u,c){if(this.allowTabSynchronization=t,this.persistenceKey=e,this.clientId=n,this.Se=i,this.window=o,this.document=s,this.De=u,this.Ce=c,this.Ne=null,this.xe=!1,this.isPrimary=!1,this.networkEnabled=!0,this.Fe=null,this.inForeground=!1,this.ke=null,this.$e=null,this.Oe=Number.NEGATIVE_INFINITY,this.Me=function(t){return Promise.resolve()},!Yc.yt())throw new Mr(Pr.UNIMPLEMENTED,"This platform is either missing IndexedDB or is known to have an incomplete implementation. Offline persistence has been disabled.");this.referenceDelegate=new Dc(this,r),this.Le=e+"main",this.R=new Ou(a),this.Be=new lu(this.Le,11,new Gc(this.R)),this.qe=new gc(this.referenceDelegate,this.R),this.Ut=new Zu,this.Ue=(e=this.R,a=this.Ut,new Pc(e,a)),this.Ke=new zu,this.window&&this.window.localStorage?this.Qe=this.window.localStorage:(this.Qe=null,!1===c&&Br("IndexedDbPersistence","LocalStorage is unavailable. As a result, persistence may not work reliably. In particular enablePersistence() could fail immediately after refreshing the page."))}function Xc(t){return Cu(t,ja.store)}function $c(t){return Cu(t,nu.store)}function Jc(t,e){var n=t.projectId;return t.isDefaultDatabase||(n+="."+t.database),"firestore/"+e+"/"+n+"/"}function Zc(t,e){this.progress=t,this.wn=e}var th=(ah.prototype.mn=function(e,n){var r=this;return this._n.getAllMutationBatchesAffectingDocumentKey(e,n).next(function(t){return r.yn(e,n,t)})},ah.prototype.yn=function(t,e,r){return this.Ue.getEntry(t,e).next(function(t){for(var e=0,n=r;e<n.length;e++)n[e].applyToLocalView(t);return t})},ah.prototype.gn=function(t,i){t.forEach(function(t,e){for(var n=0,r=i;n<r.length;n++)r[n].applyToLocalView(e)})},ah.prototype.pn=function(e,t){var n=this;return this.Ue.getEntries(e,t).next(function(t){return n.En(e,t).next(function(){return t})})},ah.prototype.En=function(t,e){var n=this;return this._n.getAllMutationBatchesAffectingDocumentKeys(t,e).next(function(t){return n.gn(e,t)})},ah.prototype.getDocumentsMatchingQuery=function(t,e,n){return r=e,Si.isDocumentKey(r.path)&&null===r.collectionGroup&&0===r.filters.length?this.Tn(t,e.path):Oo(e)?this.In(t,e,n):this.An(t,e,n);var r},ah.prototype.Tn=function(t,e){return this.mn(t,new Si(e)).next(function(t){var e=zs;return e=t.isFoundDocument()?e.insert(t.key,t):e})},ah.prototype.In=function(n,r,i){var o=this,s=r.collectionGroup,a=zs;return this.Ut.getCollectionParents(n,s).next(function(t){return cu.forEach(t,function(t){var e,e=(e=r,t=t.child(s),new Ao(t,null,e.explicitOrderBy.slice(),e.filters.slice(),e.limit,e.limitType,e.startAt,e.endAt));return o.An(n,e,i).next(function(t){t.forEach(function(t,e){a=a.insert(t,e)})})}).next(function(){return a})})},ah.prototype.An=function(e,n,t){var c,h,r=this;return this.Ue.getDocumentsMatchingQuery(e,n,t).next(function(t){return c=t,r._n.getAllMutationBatchesAffectingQuery(e,n)}).next(function(t){return h=t,r.Rn(e,h,c).next(function(t){c=t;for(var e=0,n=h;e<n.length;e++)for(var r=n[e],i=0,o=r.mutations;i<o.length;i++){var s=o[i],a=s.key,u=c.get(a);null==u&&(u=ji.newInvalidDocument(a),c=c.insert(a,u)),ds(s,u,r.localWriteTime),u.isFoundDocument()||(c=c.remove(a))}})}).next(function(){return c.forEach(function(t,e){qo(n,e)||(c=c.remove(t))}),c})},ah.prototype.Rn=function(t,e,n){for(var r=Xs(),i=0,o=e;i<o.length;i++)for(var s=0,a=o[i].mutations;s<a.length;s++){var u=a[s];u instanceof ws&&null===n.get(u.key)&&(r=r.add(u.key))}var c=n;return this.Ue.getEntries(t,r).next(function(t){return t.forEach(function(t,e){e.isFoundDocument()&&(c=c.insert(t,e))}),c})},ah),eh=(sh.Pn=function(t,e){for(var n=Xs(),r=Xs(),i=0,o=e.docChanges;i<o.length;i++){var s=o[i];switch(s.type){case 0:n=n.add(s.doc.key);break;case 1:r=r.add(s.doc.key)}}return new sh(t,e.fromCache,n,r)},sh),nh=(oh.prototype.Vn=function(t){this.Sn=t},oh.prototype.getDocumentsMatchingQuery=function(e,r,i,o){var s=this;return 0===r.filters.length&&null===r.limit&&null==r.startAt&&null==r.endAt&&(0===r.explicitOrderBy.length||1===r.explicitOrderBy.length&&r.explicitOrderBy[0].field.isKeyField())||i.isEqual(Jr.min())?this.Dn(e,r):this.Sn.pn(e,o).next(function(t){var n=s.Cn(r,t);return(Co(r)||ko(r))&&s.Nn(r.limitType,n,o,i)?s.Dn(e,r):(Ur()<=p.DEBUG&&qr("QueryEngine","Re-using previous result from %s to execute query: %s",i.toString(),Uo(r)),s.Sn.getDocumentsMatchingQuery(e,r,i).next(function(e){return n.forEach(function(t){e=e.insert(t.key,t)}),e}))})},oh.prototype.Cn=function(n,t){var r=new js(Bo(n));return t.forEach(function(t,e){qo(n,e)&&(r=r.add(e))}),r},oh.prototype.Nn=function(t,e,n,r){if(n.size!==e.size)return!0;e="F"===t?e.last():e.first();return!!e&&(e.hasPendingWrites||0<e.version.compareTo(r))},oh.prototype.Dn=function(t,e){return Ur()<=p.DEBUG&&qr("QueryEngine","Using full collection scan to execute query:",Uo(e)),this.Sn.getDocumentsMatchingQuery(t,e,Jr.min())},oh),rh=(ih.prototype.collectGarbage=function(e){var n=this;return this.persistence.runTransaction("Collect garbage","readwrite-primary",function(t){return e.collect(t,n.Fn)})},ih);function ih(t,e,n,r){this.persistence=t,this.xn=e,this.R=r,this.Fn=new Ps(Wr),this.kn=new Lc(Hi,zi),this.$n=Jr.min(),this._n=t.getMutationQueue(n),this.On=t.getRemoteDocumentCache(),this.qe=t.getTargetCache(),this.Mn=new th(this.On,this._n,this.persistence.getIndexManager()),this.Ke=t.getBundleCache(),this.xn.Vn(this.Mn)}function oh(){}function sh(t,e,n,r){this.targetId=t,this.fromCache=e,this.bn=n,this.vn=r}function ah(t,e,n){this.Ue=t,this._n=e,this.Ut=n}function uh(t,e,n,r){return new rh(t,e,n,r)}function ch(i,o){return y(this,void 0,void 0,function(){var e,n,v,r;return g(this,function(t){switch(t.label){case 0:return n=(e=i)._n,v=e.Mn,[4,e.persistence.runTransaction("Handle user change","readonly",function(g){var m;return e._n.getAllMutationBatches(g).next(function(t){return m=t,n=e.persistence.getMutationQueue(o),v=new th(e.On,n,e.persistence.getIndexManager()),n.getAllMutationBatches(g)}).next(function(t){for(var e=[],n=[],r=Xs(),i=0,o=m;i<o.length;i++){var s=o[i];e.push(s.batchId);for(var a=0,u=s.mutations;a<u.length;a++)var c=u[a],r=r.add(c.key)}for(var h=0,l=t;h<l.length;h++){var f=l[h];n.push(f.batchId);for(var d=0,p=f.mutations;d<p.length;d++){var y=p[d];r=r.add(y.key)}}return v.pn(g,r).next(function(t){return{Ln:t,removedBatchIds:e,addedBatchIds:n}})})})];case 1:return r=t.sent(),[2,(e._n=n,e.Mn=v,e.xn.Vn(e.Mn),r)]}})})}function hh(t,h){var l=t;return l.persistence.runTransaction("Acknowledge batch","readwrite-primary",function(t){var e,r,i,o,s,n,a,u=h.batch.keys(),c=l.On.newChangeBuffer({trackRemovals:!0});return e=l,r=t,o=c,s=(i=h).batch,n=s.keys(),a=cu.resolve(),n.forEach(function(n){a=a.next(function(){return o.getEntry(r,n)}).next(function(t){var e=i.docVersions.get(n);Qr(null!==e),t.version.compareTo(e)<0&&(s.applyToRemoteDocument(t,i),t.isValidDocument()&&o.addEntry(t,i.commitVersion))})}),a.next(function(){return e._n.removeMutationBatch(r,s)}).next(function(){return c.apply(t)}).next(function(){return l._n.performConsistencyCheck(t)}).next(function(){return l.Mn.pn(t,u)})})}function lh(t){var e=t;return e.persistence.runTransaction("Get last remote snapshot version","readonly",function(t){return e.qe.getLastRemoteSnapshotVersion(t)})}function fh(t,r){var u=t,c=r.snapshotVersion,h=u.Fn;return u.persistence.runTransaction("Apply remote event","readwrite-primary",function(s){var t=u.On.newChangeBuffer({trackRemovals:!0});h=u.Fn;var a=[];r.targetChanges.forEach(function(t,e){var n,r,i,o=h.get(e);o&&(a.push(u.qe.removeMatchingKeys(s,t.removedDocuments,e).next(function(){return u.qe.addMatchingKeys(s,t.addedDocuments,e)})),0<(i=t.resumeToken).approximateByteSize()&&(n=o.withResumeToken(i,c).withSequenceNumber(s.currentSequenceNumber),h=h.insert(e,n),r=o,i=t,Qr(0<(o=n).resumeToken.approximateByteSize()),(0===r.resumeToken.approximateByteSize()||3e8<=o.snapshotVersion.toMicroseconds()-r.snapshotVersion.toMicroseconds()||0<i.addedDocuments.size+i.modifiedDocuments.size+i.removedDocuments.size)&&a.push(u.qe.updateTargetData(s,n))))});var e,n=Gs;return r.documentUpdates.forEach(function(t,e){r.resolvedLimboDocuments.has(t)&&a.push(u.persistence.referenceDelegate.updateLimboDocument(s,t))}),a.push(dh(s,t,r.documentUpdates,c,void 0).next(function(t){n=t})),c.isEqual(Jr.min())||(e=u.qe.getLastRemoteSnapshotVersion(s).next(function(t){return u.qe.setTargetsMetadata(s,s.currentSequenceNumber,c)}),a.push(e)),cu.waitFor(a).next(function(){return t.apply(s)}).next(function(){return u.Mn.En(s,n)}).next(function(){return n})}).then(function(t){return u.Fn=h,t})}function dh(t,s,e,a,u){var n=Xs();return e.forEach(function(t){return n=n.add(t)}),s.getEntries(t,n).next(function(i){var o=Gs;return e.forEach(function(t,e){var n=i.get(t),r=(null==u?void 0:u.get(t))||a;e.isNoDocument()&&e.version.isEqual(Jr.min())?(s.removeEntry(t,r),o=o.insert(t,e)):!n.isValidDocument()||0<e.version.compareTo(n.version)||0===e.version.compareTo(n.version)&&n.hasPendingWrites?(s.addEntry(e,r),o=o.insert(t,e)):qr("LocalStore","Ignoring outdated watch update for ",t,". Current version:",n.version," Watch version:",e.version)}),o})}function ph(t,r){var i=t;return i.persistence.runTransaction("Allocate target","readwrite",function(e){var n;return i.qe.getTargetData(e,r).next(function(t){return t?(n=t,cu.resolve(n)):i.qe.allocateTargetId(e).next(function(t){return n=new xu(r,t,0,e.currentSequenceNumber),i.qe.addTargetData(e,n).next(function(){return n})})})}).then(function(t){var e=i.Fn.get(t.targetId);return(null===e||0<t.snapshotVersion.compareTo(e.snapshotVersion))&&(i.Fn=i.Fn.insert(t.targetId,t),i.kn.set(r,t.targetId)),t})}function yh(i,o,s){return y(this,void 0,void 0,function(){var e,n,r;return g(this,function(t){switch(t.label){case 0:n=(e=i).Fn.get(o),r=s?"readwrite":"readwrite-primary",t.label=1;case 1:return t.trys.push([1,4,,5]),s?[3,3]:[4,e.persistence.runTransaction("Release target",r,function(t){return e.persistence.referenceDelegate.removeTarget(t,n)})];case 2:t.sent(),t.label=3;case 3:return[3,5];case 4:if(!bu(r=t.sent()))throw r;return qr("LocalStore","Failed to update sequence numbers for target "+o+": "+r),[3,5];case 5:return e.Fn=e.Fn.remove(o),e.kn.delete(n.target),[2]}})})}function gh(t,o,s){var a=t,u=Jr.min(),c=Xs();return a.persistence.runTransaction("Execute query","readonly",function(e){return t=a,n=e,r=Po(o),(void 0!==(t=(i=t).kn.get(r))?cu.resolve(i.Fn.get(t)):i.qe.getTargetData(n,r)).next(function(t){if(t)return u=t.lastLimboFreeSnapshotVersion,a.qe.getMatchingKeysForTargetId(e,t.targetId).next(function(t){c=t})}).next(function(){return a.xn.getDocumentsMatchingQuery(e,o,s?u:Jr.min(),s?c:Xs())}).next(function(t){return{documents:t,Bn:c}});var t,n,r,i})}function mh(t,e){var n=t,r=n.qe,t=n.Fn.get(e);return t?Promise.resolve(t.target):n.persistence.runTransaction("Get target data","readonly",function(t){return r.lt(t,e).next(function(t){return t?t.target:null})})}function vh(t){var s=t;return s.persistence.runTransaction("Get new document changes","readonly",function(t){return e=s.On,n=t,t=s.$n,r=e,i=Gs,o=Uu(t),t=jc(n),n=IDBKeyRange.lowerBound(o,!0),t.$t({index:Xa.readTimeIndex,range:n},function(t,e){var n=Fu(r.R,e);i=i.insert(n.key,n),o=e.readTime}).next(function(){return{wn:i,readTime:qu(o)}});var e,n,r,i,o}).then(function(t){var e=t.wn,t=t.readTime;return s.$n=t,e})}function wh(n){return y(this,void 0,void 0,function(){var e;return g(this,function(t){return[2,(e=n).persistence.runTransaction("Synchronize last document change read time","readonly",function(t){return t=jc(t),r=Jr.min(),t.$t({index:Xa.readTimeIndex,reverse:!0},function(t,e,n){e.readTime&&(r=qu(e.readTime)),n.done()}).next(function(){return r});var r}).then(function(t){e.$n=t})]})})}var bh,Eh,Th=(Kh.prototype.getBundleMetadata=function(t,e){return cu.resolve(this.Qn.get(e))},Kh.prototype.saveBundleMetadata=function(t,e){return this.Qn.set(e.id,{id:e.id,version:e.version,createTime:ga(e.createTime)}),cu.resolve()},Kh.prototype.getNamedQuery=function(t,e){return cu.resolve(this.jn.get(e))},Kh.prototype.saveNamedQuery=function(t,e){return this.jn.set(e.name,{name:(e=e).name,query:Hu(e.bundledQuery),readTime:ga(e.readTime)}),cu.resolve()},Kh),Ih=(jh.prototype.isEmpty=function(){return this.Wn.isEmpty()},jh.prototype.addReference=function(t,e){e=new _h(t,e);this.Wn=this.Wn.add(e),this.zn=this.zn.add(e)},jh.prototype.Jn=function(t,e){var n=this;t.forEach(function(t){return n.addReference(t,e)})},jh.prototype.removeReference=function(t,e){this.Yn(new _h(t,e))},jh.prototype.Xn=function(t,e){var n=this;t.forEach(function(t){return n.removeReference(t,e)})},jh.prototype.Zn=function(t){var e=this,n=new Si(new si([])),r=new _h(n,t),t=new _h(n,t+1),i=[];return this.zn.forEachInRange([r,t],function(t){e.Yn(t),i.push(t.key)}),i},jh.prototype.ts=function(){var e=this;this.Wn.forEach(function(t){return e.Yn(t)})},jh.prototype.Yn=function(t){this.Wn=this.Wn.delete(t),this.zn=this.zn.delete(t)},jh.prototype.es=function(t){var e=new Si(new si([])),n=new _h(e,t),t=new _h(e,t+1),r=Xs();return this.zn.forEachInRange([n,t],function(t){r=r.add(t.key)}),r},jh.prototype.containsKey=function(t){var e=new _h(t,0),e=this.Wn.firstAfterOrEqual(e);return null!==e&&t.isEqual(e.key)},jh),_h=(Bh.Gn=function(t,e){return Si.comparator(t.key,e.key)||Wr(t.ns,e.ns)},Bh.Hn=function(t,e){return Wr(t.ns,e.ns)||Si.comparator(t.key,e.key)},Bh),Sh=(qh.prototype.checkEmpty=function(t){return cu.resolve(0===this._n.length)},qh.prototype.addMutationBatch=function(t,e,n,r){var i=this.ss;this.ss++,0<this._n.length&&this._n[this._n.length-1];n=new ku(i,e,n,r);this._n.push(n);for(var o=0,s=r;o<s.length;o++){var a=s[o];this.rs=this.rs.add(new _h(a.key,i)),this.Ut.addToCollectionParentIndex(t,a.key.path.popLast())}return cu.resolve(n)},qh.prototype.lookupMutationBatch=function(t,e){return cu.resolve(this.os(e))},qh.prototype.getNextMutationBatchAfterBatchId=function(t,e){e=this.cs(e+1),e=e<0?0:e;return cu.resolve(this._n.length>e?this._n[e]:null)},qh.prototype.getHighestUnacknowledgedBatchId=function(){return cu.resolve(0===this._n.length?-1:this.ss-1)},qh.prototype.getAllMutationBatches=function(t){return cu.resolve(this._n.slice())},qh.prototype.getAllMutationBatchesAffectingDocumentKey=function(t,e){var n=this,r=new _h(e,0),e=new _h(e,Number.POSITIVE_INFINITY),i=[];return this.rs.forEachInRange([r,e],function(t){t=n.os(t.ns);i.push(t)}),cu.resolve(i)},qh.prototype.getAllMutationBatchesAffectingDocumentKeys=function(t,e){var n=this,r=new js(Wr);return e.forEach(function(t){var e=new _h(t,0),t=new _h(t,Number.POSITIVE_INFINITY);n.rs.forEachInRange([e,t],function(t){r=r.add(t.ns)})}),cu.resolve(this.us(r))},qh.prototype.getAllMutationBatchesAffectingQuery=function(t,e){var n=e.path,r=n.length+1,e=n;Si.isDocumentKey(e)||(e=e.child(""));var e=new _h(new Si(e),0),i=new js(Wr);return this.rs.forEachWhile(function(t){var e=t.key.path;return!!n.isPrefixOf(e)&&(e.length===r&&(i=i.add(t.ns)),!0)},e),cu.resolve(this.us(i))},qh.prototype.us=function(t){var e=this,n=[];return t.forEach(function(t){t=e.os(t);null!==t&&n.push(t)}),n},qh.prototype.removeMutationBatch=function(n,r){var i=this;Qr(0===this.hs(r.batchId,"removed")),this._n.shift();var o=this.rs;return cu.forEach(r.mutations,function(t){var e=new _h(t.key,r.batchId);return o=o.delete(e),i.referenceDelegate.markPotentiallyOrphaned(n,t.key)}).next(function(){i.rs=o})},qh.prototype.Gt=function(t){},qh.prototype.containsKey=function(t,e){var n=new _h(e,0),n=this.rs.firstAfterOrEqual(n);return cu.resolve(e.isEqual(n&&n.key))},qh.prototype.performConsistencyCheck=function(t){return this._n.length,cu.resolve()},qh.prototype.hs=function(t,e){return this.cs(t)},qh.prototype.cs=function(t){return 0===this._n.length?0:t-this._n[0].batchId},qh.prototype.os=function(t){t=this.cs(t);return t<0||t>=this._n.length?null:this._n[t]},qh),Ah=(Uh.prototype.addEntry=function(t,e,n){var r=e.key,i=this.docs.get(r),o=i?i.size:0,i=this.ls(e);return this.docs=this.docs.insert(r,{document:e.clone(),size:i,readTime:n}),this.size+=i-o,this.Ut.addToCollectionParentIndex(t,r.path.popLast())},Uh.prototype.removeEntry=function(t){var e=this.docs.get(t);e&&(this.docs=this.docs.remove(t),this.size-=e.size)},Uh.prototype.getEntry=function(t,e){var n=this.docs.get(e);return cu.resolve(n?n.document.clone():ji.newInvalidDocument(e))},Uh.prototype.getEntries=function(t,e){var n=this,r=Gs;return e.forEach(function(t){var e=n.docs.get(t);r=r.insert(t,e?e.document.clone():ji.newInvalidDocument(t))}),cu.resolve(r)},Uh.prototype.getDocumentsMatchingQuery=function(t,e,n){for(var r=Gs,i=new Si(e.path.child("")),o=this.docs.getIteratorFrom(i);o.hasNext();){var s=o.getNext(),a=s.key,u=s.value,s=u.document,u=u.readTime;if(!e.path.isPrefixOf(a.path))break;u.compareTo(n)<=0||qo(e,s)&&(r=r.insert(s.key,s.clone()))}return cu.resolve(r)},Uh.prototype.fs=function(t,e){return cu.forEach(this.docs,function(t){return e(t)})},Uh.prototype.newChangeBuffer=function(t){return new Dh(this)},Uh.prototype.getSize=function(t){return cu.resolve(this.size)},Uh),Dh=(n(Vh,Eh=I),Vh.prototype.applyChanges=function(n){var r=this,i=[];return this.changes.forEach(function(t,e){e.document.isValidDocument()?i.push(r.Ie.addEntry(n,e.document,r.getReadTime(t))):r.Ie.removeEntry(t)}),cu.waitFor(i)},Vh.prototype.getFromCache=function(t,e){return this.Ie.getEntry(t,e)},Vh.prototype.getAllFromCache=function(t,e){return this.Ie.getEntries(t,e)},Vh),Nh=(Fh.prototype.forEachTarget=function(t,n){return this.ds.forEach(function(t,e){return n(e)}),cu.resolve()},Fh.prototype.getLastRemoteSnapshotVersion=function(t){return cu.resolve(this.lastRemoteSnapshotVersion)},Fh.prototype.getHighestSequenceNumber=function(t){return cu.resolve(this.ws)},Fh.prototype.allocateTargetId=function(t){return this.highestTargetId=this.ys.next(),cu.resolve(this.highestTargetId)},Fh.prototype.setTargetsMetadata=function(t,e,n){return n&&(this.lastRemoteSnapshotVersion=n),e>this.ws&&(this.ws=e),cu.resolve()},Fh.prototype.te=function(t){this.ds.set(t.target,t);var e=t.targetId;e>this.highestTargetId&&(this.ys=new yc(e),this.highestTargetId=e),t.sequenceNumber>this.ws&&(this.ws=t.sequenceNumber)},Fh.prototype.addTargetData=function(t,e){return this.te(e),this.targetCount+=1,cu.resolve()},Fh.prototype.updateTargetData=function(t,e){return this.te(e),cu.resolve()},Fh.prototype.removeTargetData=function(t,e){return this.ds.delete(e.target),this._s.Zn(e.targetId),--this.targetCount,cu.resolve()},Fh.prototype.removeTargets=function(n,r,i){var o=this,s=0,a=[];return this.ds.forEach(function(t,e){e.sequenceNumber<=r&&null===i.get(e.targetId)&&(o.ds.delete(t),a.push(o.removeMatchingKeysForTargetId(n,e.targetId)),s++)}),cu.waitFor(a).next(function(){return s})},Fh.prototype.getTargetCount=function(t){return cu.resolve(this.targetCount)},Fh.prototype.getTargetData=function(t,e){e=this.ds.get(e)||null;return cu.resolve(e)},Fh.prototype.addMatchingKeys=function(t,e,n){return this._s.Jn(e,n),cu.resolve()},Fh.prototype.removeMatchingKeys=function(e,t,n){this._s.Xn(t,n);var r=this.persistence.referenceDelegate,i=[];return r&&t.forEach(function(t){i.push(r.markPotentiallyOrphaned(e,t))}),cu.waitFor(i)},Fh.prototype.removeMatchingKeysForTargetId=function(t,e){return this._s.Zn(e),cu.resolve()},Fh.prototype.getMatchingKeysForTargetId=function(t,e){e=this._s.es(e);return cu.resolve(e)},Fh.prototype.containsKey=function(t,e){return cu.resolve(this._s.containsKey(e))},Fh),Ch=(Mh.prototype.start=function(){return Promise.resolve()},Mh.prototype.shutdown=function(){return this.xe=!1,Promise.resolve()},Object.defineProperty(Mh.prototype,"started",{get:function(){return this.xe},enumerable:!1,configurable:!0}),Mh.prototype.setDatabaseDeletedListener=function(){},Mh.prototype.setNetworkEnabled=function(){},Mh.prototype.getIndexManager=function(){return this.Ut},Mh.prototype.getMutationQueue=function(t){var e=this.gs[t.toKey()];return e||(e=new Sh(this.Ut,this.referenceDelegate),this.gs[t.toKey()]=e),e},Mh.prototype.getTargetCache=function(){return this.qe},Mh.prototype.getRemoteDocumentCache=function(){return this.Ue},Mh.prototype.getBundleCache=function(){return this.Ke},Mh.prototype.runTransaction=function(t,e,n){var r=this;qr("MemoryPersistence","Starting transaction:",t);var i=new kh(this.Ne.next());return this.referenceDelegate.Es(),n(i).next(function(t){return r.referenceDelegate.Ts(i).next(function(){return t})}).toPromise().then(function(t){return i.raiseOnCommittedEvent(),t})},Mh.prototype.Is=function(e,n){return cu.or(Object.values(this.gs).map(function(t){return function(){return t.containsKey(e,n)}}))},Mh),kh=(n(Ph,bh=N),Ph),Rh=(Lh.bs=function(t){return new Lh(t)},Object.defineProperty(Lh.prototype,"vs",{get:function(){if(this.Rs)return this.Rs;throw Gr()},enumerable:!1,configurable:!0}),Lh.prototype.addReference=function(t,e,n){return this.As.addReference(n,e),this.vs.delete(n.toString()),cu.resolve()},Lh.prototype.removeReference=function(t,e,n){return this.As.removeReference(n,e),this.vs.add(n.toString()),cu.resolve()},Lh.prototype.markPotentiallyOrphaned=function(t,e){return this.vs.add(e.toString()),cu.resolve()},Lh.prototype.removeTarget=function(t,e){var n=this;this.As.Zn(e.targetId).forEach(function(t){return n.vs.add(t.toString())});var r=this.persistence.getTargetCache();return r.getMatchingKeysForTargetId(t,e.targetId).next(function(t){t.forEach(function(t){return n.vs.add(t.toString())})}).next(function(){return r.removeTargetData(t,e)})},Lh.prototype.Es=function(){this.Rs=new Set},Lh.prototype.Ts=function(n){var r=this,i=this.persistence.getRemoteDocumentCache().newChangeBuffer();return cu.forEach(this.vs,function(t){var e=Si.fromPath(t);return r.Ps(n,e).next(function(t){t||i.removeEntry(e)})}).next(function(){return r.Rs=null,i.apply(n)})},Lh.prototype.updateLimboDocument=function(t,e){var n=this;return this.Ps(t,e).next(function(t){t?n.vs.delete(e.toString()):n.vs.add(e.toString())})},Lh.prototype.ps=function(t){return 0},Lh.prototype.Ps=function(t,e){var n=this;return cu.or([function(){return cu.resolve(n.As.containsKey(e))},function(){return n.persistence.getTargetCache().containsKey(t,e)},function(){return n.persistence.Is(t,e)}])},Lh),xh=(Oh.prototype.isAuthenticated=function(){return null!=this.uid},Oh.prototype.toKey=function(){return this.isAuthenticated()?"uid:"+this.uid:"anonymous-user"},Oh.prototype.isEqual=function(t){return t.uid===this.uid},Oh);function Oh(t){this.uid=t}function Lh(t){this.persistence=t,this.As=new Ih,this.Rs=null}function Ph(t){var e=this;return(e=bh.call(this)||this).currentSequenceNumber=t,e}function Mh(t,e){var n=this;this.gs={},this.Ne=new xr(0),this.xe=!1,this.xe=!0,this.referenceDelegate=t(this),this.qe=new Nh(this),this.Ut=new $u,this.Ue=(t=this.Ut,new Ah(t,function(t){return n.referenceDelegate.ps(t)})),this.R=new Ou(e),this.Ke=new Th(this.R)}function Fh(t){this.persistence=t,this.ds=new Lc(Hi,zi),this.lastRemoteSnapshotVersion=Jr.min(),this.highestTargetId=0,this.ws=0,this._s=new Ih,this.targetCount=0,this.ys=yc.Jt()}function Vh(t){var e=this;return(e=Eh.call(this)||this).Ie=t,e}function Uh(t,e){this.Ut=t,this.ls=e,this.docs=new Ps(Si.comparator),this.size=0}function qh(t,e){this.Ut=t,this.referenceDelegate=e,this._n=[],this.ss=1,this.rs=new js(_h.Gn)}function Bh(t,e){this.key=t,this.ns=e}function jh(){this.Wn=new js(_h.Gn),this.zn=new js(_h.Hn)}function Kh(t){this.R=t,this.Qn=new Map,this.jn=new Map}function Gh(t,e){return"firestore_clients_"+t+"_"+e}function Qh(t,e,n){n="firestore_mutations_"+t+"_"+n;return e.isAuthenticated()&&(n+="_"+e.uid),n}function Hh(t,e){return"firestore_targets_"+t+"_"+e}xh.UNAUTHENTICATED=new xh(null),xh.GOOGLE_CREDENTIALS=new xh("google-credentials-uid"),xh.FIRST_PARTY=new xh("first-party-uid");var zh,Wh=(ml.Vs=function(t,e,n){var r,i=JSON.parse(n),o="object"==typeof i&&-1!==["pending","acknowledged","rejected"].indexOf(i.state)&&(void 0===i.error||"object"==typeof i.error);return o&&i.error&&(o="string"==typeof i.error.message&&"string"==typeof i.error.code)&&(r=new Mr(i.error.code,i.error.message)),o?new ml(t,e,i.state,r):(Br("SharedClientState","Failed to parse mutation state for ID '"+e+"': "+n),null)},ml.prototype.Ss=function(){var t={state:this.state,updateTimeMs:Date.now()};return this.error&&(t.error={code:this.error.code,message:this.error.message}),JSON.stringify(t)},ml),Yh=(gl.Vs=function(t,e){var n,r=JSON.parse(e),i="object"==typeof r&&-1!==["not-current","current","rejected"].indexOf(r.state)&&(void 0===r.error||"object"==typeof r.error);return i&&r.error&&(i="string"==typeof r.error.message&&"string"==typeof r.error.code)&&(n=new Mr(r.error.code,r.error.message)),i?new gl(t,r.state,n):(Br("SharedClientState","Failed to parse target state for ID '"+t+"': "+e),null)},gl.prototype.Ss=function(){var t={state:this.state,updateTimeMs:Date.now()};return this.error&&(t.error={code:this.error.code,message:this.error.message}),JSON.stringify(t)},gl),Xh=(yl.Vs=function(t,e){for(var n=JSON.parse(e),r="object"==typeof n&&n.activeTargetIds instanceof Array,i=$s,o=0;r&&o<n.activeTargetIds.length;++o)r=_i(n.activeTargetIds[o]),i=i.add(n.activeTargetIds[o]);return r?new yl(t,i):(Br("SharedClientState","Failed to parse client data for instance '"+t+"': "+e),null)},yl),$h=(pl.Vs=function(t){var e=JSON.parse(t);return"object"==typeof e&&-1!==["Unknown","Online","Offline"].indexOf(e.onlineState)&&"string"==typeof e.clientId?new pl(e.clientId,e.onlineState):(Br("SharedClientState","Failed to parse online state: "+t),null)},pl),Jh=(dl.prototype.Ds=function(t){this.activeTargetIds=this.activeTargetIds.add(t)},dl.prototype.Cs=function(t){this.activeTargetIds=this.activeTargetIds.delete(t)},dl.prototype.Ss=function(){var t={activeTargetIds:this.activeTargetIds.toArray(),updateTimeMs:Date.now()};return JSON.stringify(t)},dl),Zh=(fl.yt=function(t){return!(!t||!t.localStorage)},fl.prototype.start=function(){return y(this,void 0,void 0,function(){var e,n,r,i,o,s,a,u,c,h,l=this;return g(this,function(t){switch(t.label){case 0:return[4,this.syncEngine.fn()];case 1:for(s=t.sent(),e=0,n=s;e<n.length;e++)(r=n[e])!==this.Ns&&(i=this.getItem(Gh(this.persistenceKey,r)))&&(o=Xh.Vs(r,i))&&(this.ks=this.ks.insert(o.clientId,o));for(this.Qs(),(s=this.storage.getItem(this.Us))&&(a=this.js(s))&&this.Ws(a),u=0,c=this.$s;u<c.length;u++)h=c[u],this.Fs(h);return this.$s=[],this.window.addEventListener("pagehide",function(){return l.shutdown()}),this.started=!0,[2]}})})},fl.prototype.writeSequenceNumber=function(t){this.setItem(this.Ms,JSON.stringify(t))},fl.prototype.getAllActiveQueryTargets=function(){return this.Gs(this.ks)},fl.prototype.isActiveQueryTarget=function(n){var r=!1;return this.ks.forEach(function(t,e){e.activeTargetIds.has(n)&&(r=!0)}),r},fl.prototype.addPendingMutation=function(t){this.zs(t,"pending")},fl.prototype.updateMutationState=function(t,e,n){this.zs(t,e,n),this.Hs(t)},fl.prototype.addLocalQueryTarget=function(t){var e,n="not-current";return this.isActiveQueryTarget(t)&&(!(e=this.storage.getItem(Hh(this.persistenceKey,t)))||(e=Yh.Vs(t,e))&&(n=e.state)),this.Js.Ds(t),this.Qs(),n},fl.prototype.removeLocalQueryTarget=function(t){this.Js.Cs(t),this.Qs()},fl.prototype.isLocalQueryTarget=function(t){return this.Js.activeTargetIds.has(t)},fl.prototype.clearQueryState=function(t){this.removeItem(Hh(this.persistenceKey,t))},fl.prototype.updateQueryState=function(t,e,n){this.Ys(t,e,n)},fl.prototype.handleUserChange=function(t,e,n){var r=this;e.forEach(function(t){r.Hs(t)}),this.currentUser=t,n.forEach(function(t){r.addPendingMutation(t)})},fl.prototype.setOnlineState=function(t){this.Xs(t)},fl.prototype.notifyBundleLoaded=function(){this.Zs()},fl.prototype.shutdown=function(){this.started&&(this.window.removeEventListener("storage",this.xs),this.removeItem(this.Os),this.started=!1)},fl.prototype.getItem=function(t){var e=this.storage.getItem(t);return qr("SharedClientState","READ",t,e),e},fl.prototype.setItem=function(t,e){qr("SharedClientState","SET",t,e),this.storage.setItem(t,e)},fl.prototype.removeItem=function(t){qr("SharedClientState","REMOVE",t),this.storage.removeItem(t)},fl.prototype.Fs=function(t){var e=this,o=t;o.storageArea===this.storage&&(qr("SharedClientState","EVENT",o.key,o.newValue),o.key!==this.Os?this.Se.enqueueRetryable(function(){return y(e,void 0,void 0,function(){var e,n,r,i;return g(this,function(t){if(this.started){if(null!==o.key)if(this.Ls.test(o.key)){if(null==o.newValue)return e=this.ti(o.key),[2,this.ei(e,null)];if(e=this.ni(o.key,o.newValue))return[2,this.ei(e.clientId,e)]}else if(this.Bs.test(o.key)){if(null!==o.newValue&&(n=this.si(o.key,o.newValue)))return[2,this.ii(n)]}else if(this.qs.test(o.key)){if(null!==o.newValue&&(r=this.ri(o.key,o.newValue)))return[2,this.oi(r)]}else if(o.key===this.Us){if(null!==o.newValue&&(i=this.js(o.newValue)))return[2,this.Ws(i)]}else if(o.key===this.Ms)(i=function(t){var e=xr.o;if(null!=t)try{var n=JSON.parse(t);Qr("number"==typeof n),e=n}catch(t){Br("SharedClientState","Failed to read sequence number from WebStorage",t)}return e}(o.newValue))!==xr.o&&this.sequenceNumberHandler(i);else if(o.key===this.Ks)return[2,this.syncEngine.ci()]}else this.$s.push(o);return[2]})})}):Br("Received WebStorage notification for local change. Another client might have garbage-collected our state"))},Object.defineProperty(fl.prototype,"Js",{get:function(){return this.ks.get(this.Ns)},enumerable:!1,configurable:!0}),fl.prototype.Qs=function(){this.setItem(this.Os,this.Js.Ss())},fl.prototype.zs=function(t,e,n){n=new Wh(this.currentUser,t,e,n),t=Qh(this.persistenceKey,this.currentUser,t);this.setItem(t,n.Ss())},fl.prototype.Hs=function(t){t=Qh(this.persistenceKey,this.currentUser,t);this.removeItem(t)},fl.prototype.Xs=function(t){t={clientId:this.Ns,onlineState:t};this.storage.setItem(this.Us,JSON.stringify(t))},fl.prototype.Ys=function(t,e,n){var r=Hh(this.persistenceKey,t),n=new Yh(t,e,n);this.setItem(r,n.Ss())},fl.prototype.Zs=function(){this.setItem(this.Ks,"value-not-used")},fl.prototype.ti=function(t){t=this.Ls.exec(t);return t?t[1]:null},fl.prototype.ni=function(t,e){t=this.ti(t);return Xh.Vs(t,e)},fl.prototype.si=function(t,e){var n=this.Bs.exec(t),t=Number(n[1]),n=void 0!==n[2]?n[2]:null;return Wh.Vs(new xh(n),t,e)},fl.prototype.ri=function(t,e){t=this.qs.exec(t),t=Number(t[1]);return Yh.Vs(t,e)},fl.prototype.js=function(t){return $h.Vs(t)},fl.prototype.ii=function(e){return y(this,void 0,void 0,function(){return g(this,function(t){return e.user.uid===this.currentUser.uid?[2,this.syncEngine.ui(e.batchId,e.state,e.error)]:(qr("SharedClientState","Ignoring mutation for non-active user "+e.user.uid),[2])})})},fl.prototype.oi=function(t){return this.syncEngine.ai(t.targetId,t.state,t.error)},fl.prototype.ei=function(t,e){var n=this,r=e?this.ks.insert(t,e):this.ks.remove(t),i=this.Gs(this.ks),o=this.Gs(r),s=[],a=[];return o.forEach(function(t){i.has(t)||s.push(t)}),i.forEach(function(t){o.has(t)||a.push(t)}),this.syncEngine.hi(s,a).then(function(){n.ks=r})},fl.prototype.Ws=function(t){this.ks.get(t.clientId)&&this.onlineStateHandler(t.onlineState)},fl.prototype.Gs=function(t){var n=$s;return t.forEach(function(t,e){n=n.unionWith(e.activeTargetIds)}),n},fl),tl=(ll.prototype.addPendingMutation=function(t){},ll.prototype.updateMutationState=function(t,e,n){},ll.prototype.addLocalQueryTarget=function(t){return this.li.Ds(t),this.fi[t]||"not-current"},ll.prototype.updateQueryState=function(t,e,n){this.fi[t]=e},ll.prototype.removeLocalQueryTarget=function(t){this.li.Cs(t)},ll.prototype.isLocalQueryTarget=function(t){return this.li.activeTargetIds.has(t)},ll.prototype.clearQueryState=function(t){delete this.fi[t]},ll.prototype.getAllActiveQueryTargets=function(){return this.li.activeTargetIds},ll.prototype.isActiveQueryTarget=function(t){return this.li.activeTargetIds.has(t)},ll.prototype.start=function(){return this.li=new Jh,Promise.resolve()},ll.prototype.handleUserChange=function(t,e,n){},ll.prototype.setOnlineState=function(t){},ll.prototype.shutdown=function(){},ll.prototype.writeSequenceNumber=function(t){},ll.prototype.notifyBundleLoaded=function(){},ll),el=(hl.prototype.di=function(t){},hl.prototype.shutdown=function(){},hl),nl=(cl.prototype.di=function(t){this.gi.push(t)},cl.prototype.shutdown=function(){window.removeEventListener("online",this.wi),window.removeEventListener("offline",this.mi)},cl.prototype.pi=function(){window.addEventListener("online",this.wi),window.addEventListener("offline",this.mi)},cl.prototype._i=function(){qr("ConnectivityMonitor","Network connectivity changed: AVAILABLE");for(var t=0,e=this.gi;t<e.length;t++)(0,e[t])(0)},cl.prototype.yi=function(){qr("ConnectivityMonitor","Network connectivity changed: UNAVAILABLE");for(var t=0,e=this.gi;t<e.length;t++)(0,e[t])(1)},cl.yt=function(){return"undefined"!=typeof window&&void 0!==window.addEventListener&&void 0!==window.removeEventListener},cl),rl={BatchGetDocuments:"batchGet",Commit:"commit",RunQuery:"runQuery"},il=(ul.prototype.Ii=function(t){this.Ai=t},ul.prototype.Ri=function(t){this.bi=t},ul.prototype.onMessage=function(t){this.vi=t},ul.prototype.close=function(){this.Ti()},ul.prototype.send=function(t){this.Ei(t)},ul.prototype.Pi=function(){this.Ai()},ul.prototype.Vi=function(t){this.bi(t)},ul.prototype.Si=function(t){this.vi(t)},ul),ol=(al.prototype.Ni=function(e,t,n,r){var i=this.xi(e,t);qr("RestConnection","Sending: ",i,n);t={};return this.Fi(t,r),this.ki(e,i,t,n).then(function(t){return qr("RestConnection","Received: ",t),t},function(t){throw jr("RestConnection",e+" failed with error: ",t,"url: ",i,"request:",n),t})},al.prototype.$i=function(t,e,n,r){return this.Ni(t,e,n,r)},al.prototype.Fi=function(t,e){if(t["X-Goog-Api-Client"]="gl-js/ fire/8.6.8",t["Content-Type"]="text/plain",this.databaseInfo.appId&&(t["X-Firebase-GMPID"]=this.databaseInfo.appId),e)for(var n in e.authHeaders)e.authHeaders.hasOwnProperty(n)&&(t[n]=e.authHeaders[n])},al.prototype.xi=function(t,e){return this.Di+"/v1/"+e+":"+rl[t]},n(sl,zh=al),sl.prototype.ki=function(u,e,n,r){return new Promise(function(o,s){var a=new Rr;a.listenOnce(Sr.COMPLETE,function(){try{switch(a.getLastErrorCode()){case _r.NO_ERROR:var t=a.getResponseJson();qr("Connection","XHR received:",JSON.stringify(t)),o(t);break;case _r.TIMEOUT:qr("Connection",'RPC "'+u+'" timed out'),s(new Mr(Pr.DEADLINE_EXCEEDED,"Request time out"));break;case _r.HTTP_ERROR:var e,n,r=a.getStatus();qr("Connection",'RPC "'+u+'" failed with status:',r,"response text:",a.getResponseText()),0<r?(e=a.getResponseJson().error)&&e.status&&e.message?(i=e.status.toLowerCase().replace(/_/g,"-"),n=0<=Object.values(Pr).indexOf(i)?i:Pr.UNKNOWN,s(new Mr(n,e.message))):s(new Mr(Pr.UNKNOWN,"Server responded with status "+a.getStatus())):s(new Mr(Pr.UNAVAILABLE,"Connection failed."));break;default:Gr()}}finally{qr("Connection",'RPC "'+u+'" completed.')}var i});var t=JSON.stringify(r);a.send(e,"POST",t,n,15)})},sl.prototype.Oi=function(t,e){var n,r=[this.Di,"/","google.firestore.v1.Firestore","/",t,"/channel"],i=new vr,o=Ir(),t={httpSessionIdParam:"gsessionid",initMessageHeaders:{},messageUrlParams:{database:"projects/"+this.databaseId.projectId+"/databases/"+this.databaseId.database},sendRawJson:!0,supportsCrossDomainXhr:!0,internalChannelParams:{forwardChannelRequestTimeoutMs:6e5},forceLongPolling:this.forceLongPolling,detectBufferingProxy:this.autoDetectLongPolling};this.useFetchStreams&&(t.xmlHttpFactory=new Cr({})),this.Fi(t.initMessageHeaders,e),"undefined"!=typeof window&&(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(h())||"object"==typeof navigator&&"ReactNative"===navigator.product||0<=h().indexOf("Electron/")||(0<=(n=h()).indexOf("MSIE ")||0<=n.indexOf("Trident/"))||0<=h().indexOf("MSAppHost/")||"object"==typeof(n="object"==typeof chrome?chrome.runtime:"object"==typeof browser?browser.runtime:void 0)&&void 0!==n.id||(t.httpHeadersOverwriteParam="$httpHeaders");r=r.join("");qr("Connection","Creating WebChannel: "+r,t);var s=i.createWebChannel(r,t),a=!1,u=!1,c=new il({Ei:function(t){u?qr("Connection","Not sending because WebChannel is closed:",t):(a||(qr("Connection","Opening WebChannel transport."),s.open(),a=!0),qr("Connection","WebChannel sending:",t),s.send(t))},Ti:function(){return s.close()}}),t=function(t,e,n){t.listen(e,function(t){try{n(t)}catch(t){setTimeout(function(){throw t},0)}})};return t(s,kr.EventType.OPEN,function(){u||qr("Connection","WebChannel transport opened.")}),t(s,kr.EventType.CLOSE,function(){u||(u=!0,qr("Connection","WebChannel transport closed"),c.Vi())}),t(s,kr.EventType.ERROR,function(t){u||(u=!0,jr("Connection","WebChannel transport errored:",t),c.Vi(new Mr(Pr.UNAVAILABLE,"The operation could not be completed")))}),t(s,kr.EventType.MESSAGE,function(t){var e,n,r,i;u||(Qr(!!(e=t.data[0])),(n=e.error||(null===(i=e[0])||void 0===i?void 0:i.error))?(qr("Connection","WebChannel received error:",n),r=n.status,t=function(){var t=As[r];if(void 0!==t)return Ls(t)}(),i=n.message,void 0===t&&(t=Pr.INTERNAL,i="Unknown error status: "+r+" with message "+n.message),u=!0,c.Vi(new Mr(t,i)),s.close()):(qr("Connection","WebChannel received:",e),c.Si(e)))}),t(o,Ar.STAT_EVENT,function(t){t.stat===Dr?qr("Connection","Detected buffering proxy"):t.stat===Nr&&qr("Connection","Detected no buffering proxy")}),setTimeout(function(){c.Pi()},0),c},sl);function sl(t){var e=this;return(e=zh.call(this,t)||this).forceLongPolling=t.forceLongPolling,e.autoDetectLongPolling=t.autoDetectLongPolling,e.useFetchStreams=t.useFetchStreams,e}function al(t){this.databaseInfo=t,this.databaseId=t.databaseId;var e=t.ssl?"https":"http";this.Di=e+"://"+t.host,this.Ci="projects/"+this.databaseId.projectId+"/databases/"+this.databaseId.database+"/documents"}function ul(t){this.Ei=t.Ei,this.Ti=t.Ti}function cl(){var t=this;this.wi=function(){return t._i()},this.mi=function(){return t.yi()},this.gi=[],this.pi()}function hl(){}function ll(){this.li=new Jh,this.fi={},this.onlineStateHandler=null,this.sequenceNumberHandler=null}function fl(t,e,n,r,i){this.window=t,this.Se=e,this.persistenceKey=n,this.Ns=r,this.syncEngine=null,this.onlineStateHandler=null,this.sequenceNumberHandler=null,this.xs=this.Fs.bind(this),this.ks=new Ps(Wr),this.started=!1,this.$s=[];n=n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");this.storage=this.window.localStorage,this.currentUser=i,this.Os=Gh(this.persistenceKey,this.Ns),this.Ms="firestore_sequence_number_"+this.persistenceKey,this.ks=this.ks.insert(this.Ns,new Jh),this.Ls=new RegExp("^firestore_clients_"+n+"_([^_]*)$"),this.Bs=new RegExp("^firestore_mutations_"+n+"_(\\d+)(?:_(.*))?$"),this.qs=new RegExp("^firestore_targets_"+n+"_(\\d+)$"),this.Us="firestore_online_state_"+this.persistenceKey,this.Ks="firestore_bundle_loaded_"+this.persistenceKey,this.window.addEventListener("storage",this.xs)}function dl(){this.activeTargetIds=$s}function pl(t,e){this.clientId=t,this.onlineState=e}function yl(t,e){this.clientId=t,this.activeTargetIds=e}function gl(t,e,n){this.targetId=t,this.state=e,this.error=n}function ml(t,e,n,r){this.user=t,this.batchId=e,this.state=n,this.error=r}function vl(){return"undefined"!=typeof window?window:null}function wl(){return"undefined"!=typeof document?document:null}function bl(t){return new da(t,!0)}function El(t,e,n,r,i){var o=this;this.localStore=t,this.datastore=e,this.asyncQueue=n,this.remoteSyncer={},this.kr=[],this.$r=new Map,this.Or=new Set,this.Mr=[],this.Lr=i,this.Lr.di(function(t){n.enqueueAndForget(function(){return y(o,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return Gl(this)?(qr("RemoteStore","Restarting streams for network reachability change."),[4,function(n){return y(this,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return(e=n).Or.add(4),[4,Fl(e)];case 1:return t.sent(),e.Br.set("Unknown"),e.Or.delete(4),[4,Ml(e)];case 2:return t.sent(),[2]}})})}(this)]):[3,2];case 1:t.sent(),t.label=2;case 2:return[2]}})})})}),this.Br=new Cl(n,r)}var Tl,Il,_l,Sl=(Pl.prototype.reset=function(){this.qi=0},Pl.prototype.Qi=function(){this.qi=this.Bi},Pl.prototype.ji=function(t){var e=this;this.cancel();var n=Math.floor(this.qi+this.Wi()),r=Math.max(0,Date.now()-this.Ki),i=Math.max(0,n-r);0<i&&qr("ExponentialBackoff","Backing off for "+i+" ms (base delay: "+this.qi+" ms, delay with jitter: "+n+" ms, last attempt: "+r+" ms ago)"),this.Ui=this.Se.enqueueAfterDelay(this.timerId,i,function(){return e.Ki=Date.now(),t()}),this.qi*=this.Li,this.qi<this.Mi&&(this.qi=this.Mi),this.qi>this.Bi&&(this.qi=this.Bi)},Pl.prototype.Gi=function(){null!==this.Ui&&(this.Ui.skipDelay(),this.Ui=null)},Pl.prototype.cancel=function(){null!==this.Ui&&(this.Ui.cancel(),this.Ui=null)},Pl.prototype.Wi=function(){return(Math.random()-.5)*this.qi},Pl),I=(Ll.prototype.tr=function(){return 1===this.state||2===this.state||4===this.state},Ll.prototype.er=function(){return 2===this.state},Ll.prototype.start=function(){3!==this.state?this.auth():this.nr()},Ll.prototype.stop=function(){return y(this,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return this.tr()?[4,this.close(0)]:[3,2];case 1:t.sent(),t.label=2;case 2:return[2]}})})},Ll.prototype.sr=function(){this.state=0,this.Zi.reset()},Ll.prototype.ir=function(){var t=this;this.er()&&null===this.Xi&&(this.Xi=this.Se.enqueueAfterDelay(this.zi,6e4,function(){return t.rr()}))},Ll.prototype.cr=function(t){this.ur(),this.stream.send(t)},Ll.prototype.rr=function(){return y(this,void 0,void 0,function(){return g(this,function(t){return this.er()?[2,this.close(0)]:[2]})})},Ll.prototype.ur=function(){this.Xi&&(this.Xi.cancel(),this.Xi=null)},Ll.prototype.close=function(e,n){return y(this,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return this.ur(),this.Zi.cancel(),this.Yi++,3!==e?this.Zi.reset():n&&n.code===Pr.RESOURCE_EXHAUSTED?(Br(n.toString()),Br("Using maximum backoff delay to prevent overloading the backend."),this.Zi.Qi()):n&&n.code===Pr.UNAUTHENTICATED&&this.Ji.invalidateToken(),null!==this.stream&&(this.ar(),this.stream.close(),this.stream=null),this.state=e,[4,this.listener.Ri(n)];case 1:return t.sent(),[2]}})})},Ll.prototype.ar=function(){},Ll.prototype.auth=function(){var n=this;this.state=1;var t=this.hr(this.Yi),e=this.Yi;this.Ji.getToken().then(function(t){n.Yi===e&&n.lr(t)},function(e){t(function(){var t=new Mr(Pr.UNKNOWN,"Fetching auth token failed: "+e.message);return n.dr(t)})})},Ll.prototype.lr=function(t){var e=this,n=this.hr(this.Yi);this.stream=this.wr(t),this.stream.Ii(function(){n(function(){return e.state=2,e.listener.Ii()})}),this.stream.Ri(function(t){n(function(){return e.dr(t)})}),this.stream.onMessage(function(t){n(function(){return e.onMessage(t)})})},Ll.prototype.nr=function(){var t=this;this.state=4,this.Zi.ji(function(){return y(t,void 0,void 0,function(){return g(this,function(t){return this.state=0,this.start(),[2]})})})},Ll.prototype.dr=function(t){return qr("PersistentStream","close with error: "+t),this.stream=null,this.close(3,t)},Ll.prototype.hr=function(e){var n=this;return function(t){n.Se.enqueueAndForget(function(){return n.Yi===e?t():(qr("PersistentStream","stream callback skipped by getCloseGuardedDispatcher."),Promise.resolve())})}},Ll),Al=(n(Ol,_l=I),Ol.prototype.wr=function(t){return this.Hi.Oi("Listen",t)},Ol.prototype.onMessage=function(t){this.Zi.reset();var e=function(t,e){if("targetChange"in e){e.targetChange;var n="NO_CHANGE"===(o=e.targetChange.targetChangeType||"NO_CHANGE")?0:"ADD"===o?1:"REMOVE"===o?2:"CURRENT"===o?3:"RESET"===o?4:Gr(),r=e.targetChange.targetIds||[],i=(s=e.targetChange.resumeToken,t.I?(Qr(void 0===s||"string"==typeof s),hi.fromBase64String(s||"")):(Qr(void 0===s||s instanceof Uint8Array),hi.fromUint8Array(s||new Uint8Array))),o=(a=e.targetChange.cause)&&(u=void 0===(c=a).code?Pr.UNKNOWN:Ls(c.code),new Mr(u,c.message||"")),s=new na(n,r,i,o||null)}else if("documentChange"in e){e.documentChange,(n=e.documentChange).document,n.document.name,n.document.updateTime;var r=ba(t,n.document.name),i=ga(n.document.updateTime),a=new qi({mapValue:{fields:n.document.fields}}),u=(o=ji.newFoundDocument(r,i,a),n.targetIds||[]),c=n.removedTargetIds||[];s=new ta(u,c,o.key,o)}else if("documentDelete"in e)e.documentDelete,(n=e.documentDelete).document,r=ba(t,n.document),i=n.readTime?ga(n.readTime):Jr.min(),a=ji.newNoDocument(r,i),o=n.removedTargetIds||[],s=new ta([],o,a.key,a);else if("documentRemove"in e)e.documentRemove,(n=e.documentRemove).document,r=ba(t,n.document),i=n.removedTargetIds||[],s=new ta([],i,r,null);else{if(!("filter"in e))return Gr();e.filter;e=e.filter;e.targetId,n=e.count||0,r=new Ss(n),i=e.targetId,s=new ea(i,r)}return s}(this.R,t),t=function(t){if(!("targetChange"in t))return Jr.min();t=t.targetChange;return(!t.targetIds||!t.targetIds.length)&&t.readTime?ga(t.readTime):Jr.min()}(t);return this.listener._r(e,t)},Ol.prototype.mr=function(t){var e,n,r,i={};i.database=Ia(this.R),i.addTarget=(e=this.R,(r=Wi(r=(n=t).target)?{documents:Ca(e,r)}:{query:ka(e,r)}).targetId=n.targetId,0<n.resumeToken.approximateByteSize()?r.resumeToken=ya(e,n.resumeToken):0<n.snapshotVersion.compareTo(Jr.min())&&(r.readTime=pa(e,n.snapshotVersion.toTimestamp())),r);var o,t=(this.R,o=t,null==(t=function(){switch(o.purpose){case 0:return null;case 1:return"existence-filter-mismatch";case 2:return"limbo-document";default:return Gr()}}())?null:{"goog-listen-tags":t});t&&(i.labels=t),this.cr(i)},Ol.prototype.yr=function(t){var e={};e.database=Ia(this.R),e.removeTarget=t,this.cr(e)},Ol),Dl=(n(xl,Il=I),Object.defineProperty(xl.prototype,"pr",{get:function(){return this.gr},enumerable:!1,configurable:!0}),xl.prototype.start=function(){this.gr=!1,this.lastStreamToken=void 0,Il.prototype.start.call(this)},xl.prototype.ar=function(){this.gr&&this.Er([])},xl.prototype.wr=function(t){return this.Hi.Oi("Write",t)},xl.prototype.onMessage=function(t){if(Qr(!!t.streamToken),this.lastStreamToken=t.streamToken,this.gr){this.Zi.reset();var e=(n=t.writeResults,r=t.commitTime,n&&0<n.length?(Qr(void 0!==r),n.map(function(t){return n=r,(t=(e=t).updateTime?ga(e.updateTime):ga(n)).isEqual(Jr.min())&&(t=ga(n)),new us(t,e.transformResults||[]);var e,n})):[]),n=ga(t.commitTime);return this.listener.Tr(n,e)}var n,r;return Qr(!t.writeResults||0===t.writeResults.length),this.gr=!0,this.listener.Ir()},xl.prototype.Ar=function(){var t={};t.database=Ia(this.R),this.cr(t)},xl.prototype.Er=function(t){var e=this,t={streamToken:this.lastStreamToken,writes:t.map(function(t){return Da(e.R,t)})};this.cr(t)},xl),Nl=(n(Rl,Tl=function(){}),Rl.prototype.br=function(){if(this.Rr)throw new Mr(Pr.FAILED_PRECONDITION,"The client has already been terminated.")},Rl.prototype.Ni=function(e,n,r){var i=this;return this.br(),this.credentials.getToken().then(function(t){return i.Hi.Ni(e,n,r,t)}).catch(function(t){throw"FirebaseError"===t.name?(t.code===Pr.UNAUTHENTICATED&&i.credentials.invalidateToken(),t):new Mr(Pr.UNKNOWN,t.toString())})},Rl.prototype.$i=function(e,n,r){var i=this;return this.br(),this.credentials.getToken().then(function(t){return i.Hi.$i(e,n,r,t)}).catch(function(t){throw"FirebaseError"===t.name?(t.code===Pr.UNAUTHENTICATED&&i.credentials.invalidateToken(),t):new Mr(Pr.UNKNOWN,t.toString())})},Rl.prototype.terminate=function(){this.Rr=!0},Rl),Cl=(kl.prototype.Sr=function(){var t=this;0===this.vr&&(this.Dr("Unknown"),this.Pr=this.asyncQueue.enqueueAfterDelay("online_state_timeout",1e4,function(){return t.Pr=null,t.Cr("Backend didn't respond within 10 seconds."),t.Dr("Offline"),Promise.resolve()}))},kl.prototype.Nr=function(t){"Online"===this.state?this.Dr("Unknown"):(this.vr++,1<=this.vr&&(this.Fr(),this.Cr("Connection failed 1 times. Most recent error: "+t.toString()),this.Dr("Offline")))},kl.prototype.set=function(t){this.Fr(),this.vr=0,"Online"===t&&(this.Vr=!1),this.Dr(t)},kl.prototype.Dr=function(t){t!==this.state&&(this.state=t,this.onlineStateHandler(t))},kl.prototype.Cr=function(t){t="Could not reach Cloud Firestore backend. "+t+"\nThis typically indicates that your device does not have a healthy Internet connection at the moment. The client will operate in offline mode until it is able to successfully connect to the backend.";this.Vr?(Br(t),this.Vr=!1):qr("OnlineStateTracker",t)},kl.prototype.Fr=function(){null!==this.Pr&&(this.Pr.cancel(),this.Pr=null)},kl);function kl(t,e){this.asyncQueue=t,this.onlineStateHandler=e,this.state="Unknown",this.vr=0,this.Pr=null,this.Vr=!0}function Rl(t,e,n){var r=this;return(r=Tl.call(this)||this).credentials=t,r.Hi=e,r.R=n,r.Rr=!1,r}function xl(t,e,n,r,i){var o=this;return(o=Il.call(this,t,"write_stream_connection_backoff","write_stream_idle",e,n,i)||this).R=r,o.gr=!1,o}function Ol(t,e,n,r,i){var o=this;return(o=_l.call(this,t,"listen_stream_connection_backoff","listen_stream_idle",e,n,i)||this).R=r,o}function Ll(t,e,n,r,i,o){this.Se=t,this.zi=n,this.Hi=r,this.Ji=i,this.listener=o,this.state=0,this.Yi=0,this.Xi=null,this.stream=null,this.Zi=new Sl(t,e)}function Pl(t,e,n,r,i){void 0===n&&(n=1e3),void 0===r&&(r=1.5),void 0===i&&(i=6e4),this.Se=t,this.timerId=e,this.Mi=n,this.Li=r,this.Bi=i,this.qi=0,this.Ui=null,this.Ki=Date.now(),this.reset()}function Ml(r){return y(this,void 0,void 0,function(){var e,n;return g(this,function(t){switch(t.label){case 0:if(!Gl(r))return[3,4];e=0,n=r.Mr,t.label=1;case 1:return e<n.length?[4,(0,n[e])(!0)]:[3,4];case 2:t.sent(),t.label=3;case 3:return e++,[3,1];case 4:return[2]}})})}function Fl(r){return y(this,void 0,void 0,function(){var e,n;return g(this,function(t){switch(t.label){case 0:e=0,n=r.Mr,t.label=1;case 1:return e<n.length?[4,(0,n[e])(!1)]:[3,4];case 2:t.sent(),t.label=3;case 3:return e++,[3,1];case 4:return[2]}})})}function Vl(t,e){t.$r.has(e.targetId)||(t.$r.set(e.targetId,e),Kl(t)?jl(t):Zl(t).er()&&ql(t,e))}function Ul(t,e){var n=t,t=Zl(n);n.$r.delete(e),t.er()&&Bl(n,e),0===n.$r.size&&(t.er()?t.ir():Gl(n)&&n.Br.set("Unknown"))}function ql(t,e){t.qr.U(e.targetId),Zl(t).mr(e)}function Bl(t,e){t.qr.U(e),Zl(t).yr(e)}function jl(e){e.qr=new ia({getRemoteKeysForTarget:function(t){return e.remoteSyncer.getRemoteKeysForTarget(t)},lt:function(t){return e.$r.get(t)||null}}),Zl(e).start(),e.Br.Sr()}function Kl(t){return Gl(t)&&!Zl(t).tr()&&0<t.$r.size}function Gl(t){return 0===t.Or.size}function Ql(t){t.qr=void 0}function Hl(s,a,u){return y(this,void 0,void 0,function(){var n,o;return g(this,function(t){switch(t.label){case 0:if(s.Br.set("Online"),!(a instanceof na&&2===a.state&&a.cause))return[3,6];t.label=1;case 1:return t.trys.push([1,3,,5]),[4,function(o,s){return y(this,void 0,void 0,function(){var e,n,r,i;return g(this,function(t){switch(t.label){case 0:e=s.cause,n=0,r=s.targetIds,t.label=1;case 1:return n<r.length?(i=r[n],o.$r.has(i)?[4,o.remoteSyncer.rejectListen(i,e)]:[3,3]):[3,5];case 2:t.sent(),o.$r.delete(i),o.qr.removeTarget(i),t.label=3;case 3:t.label=4;case 4:return n++,[3,1];case 5:return[2]}})})}(s,a)];case 2:return t.sent(),[3,5];case 3:return n=t.sent(),qr("RemoteStore","Failed to remove targets %s: %s ",a.targetIds.join(","),n),[4,zl(s,n)];case 4:return t.sent(),[3,5];case 5:return[3,13];case 6:if(a instanceof ta?s.qr.X(a):a instanceof ea?s.qr.rt(a):s.qr.et(a),u.isEqual(Jr.min()))return[3,13];t.label=7;case 7:return t.trys.push([7,11,,13]),[4,lh(s.localStore)];case 8:return o=t.sent(),0<=u.compareTo(o)?[4,(i=u,(e=(r=s).qr.ut(i)).targetChanges.forEach(function(t,e){var n;0<t.resumeToken.approximateByteSize()&&((n=r.$r.get(e))&&r.$r.set(e,n.withResumeToken(t.resumeToken,i)))}),e.targetMismatches.forEach(function(t){var e=r.$r.get(t);e&&(r.$r.set(t,e.withResumeToken(hi.EMPTY_BYTE_STRING,e.snapshotVersion)),Bl(r,t),e=new xu(e.target,t,1,e.sequenceNumber),ql(r,e))}),r.remoteSyncer.applyRemoteEvent(e))]:[3,10];case 9:t.sent(),t.label=10;case 10:return[3,13];case 11:return qr("RemoteStore","Failed to raise snapshot:",o=t.sent()),[4,zl(s,o)];case 12:return t.sent(),[3,13];case 13:return[2]}var r,i,e})})}function zl(n,r,i){return y(this,void 0,void 0,function(){var e=this;return g(this,function(t){switch(t.label){case 0:if(!bu(r))throw r;return n.Or.add(1),[4,Fl(n)];case 1:return t.sent(),n.Br.set("Offline"),i=i||function(){return lh(n.localStore)},n.asyncQueue.enqueueRetryable(function(){return y(e,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return qr("RemoteStore","Retrying IndexedDB access"),[4,i()];case 1:return t.sent(),n.Or.delete(1),[4,Ml(n)];case 2:return t.sent(),[2]}})})}),[2]}})})}function Wl(e,n){return n().catch(function(t){return zl(e,t,n)})}function Yl(u){return y(this,void 0,void 0,function(){var i,o,s,a;return g(this,function(t){switch(t.label){case 0:o=tf(i=u),s=0<i.kr.length?i.kr[i.kr.length-1].batchId:-1,t.label=1;case 1:if(!(Gl(i)&&i.kr.length<10))return[3,7];t.label=2;case 2:return t.trys.push([2,4,,6]),[4,(e=i.localStore,n=s,(r=e).persistence.runTransaction("Get next mutation batch","readonly",function(t){return void 0===n&&(n=-1),r._n.getNextMutationBatchAfterBatchId(t,n)}))];case 3:return null===(a=t.sent())?(0===i.kr.length&&o.ir(),[3,7]):(s=a.batchId,function(t,e){t.kr.push(e);t=tf(t);t.er()&&t.pr&&t.Er(e.mutations)}(i,a),[3,6]);case 4:return a=t.sent(),[4,zl(i,a)];case 5:return t.sent(),[3,6];case 6:return[3,1];case 7:return Xl(i)&&$l(i),[2]}var e,n,r})})}function Xl(t){return Gl(t)&&!tf(t).tr()&&0<t.kr.length}function $l(t){tf(t).start()}function Jl(n,r){return y(this,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return e=n,r?(e.Or.delete(2),[4,Ml(e)]):[3,2];case 1:return t.sent(),[3,5];case 2:return r?[3,4]:(e.Or.add(2),[4,Fl(e)]);case 3:t.sent(),e.Br.set("Unknown"),t.label=4;case 4:t.label=5;case 5:return[2]}})})}function Zl(n){var t,e,r,i=this;return n.Ur||(n.Ur=(t=n.datastore,e=n.asyncQueue,r={Ii:function(n){return y(this,void 0,void 0,function(){return g(this,function(t){return n.$r.forEach(function(t,e){ql(n,t)}),[2]})})}.bind(null,n),Ri:function(e,n){return y(this,void 0,void 0,function(){return g(this,function(t){return Ql(e),Kl(e)?(e.Br.Nr(n),jl(e)):e.Br.set("Unknown"),[2]})})}.bind(null,n),_r:Hl.bind(null,n)},t.br(),new Al(e,t.Hi,t.credentials,t.R,r)),n.Mr.push(function(e){return y(i,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return e?(n.Ur.sr(),Kl(n)?jl(n):n.Br.set("Unknown"),[3,3]):[3,1];case 1:return[4,n.Ur.stop()];case 2:t.sent(),Ql(n),t.label=3;case 3:return[2]}})})})),n.Ur}function tf(n){var t,e,r,i=this;return n.Kr||(n.Kr=(t=n.datastore,e=n.asyncQueue,r={Ii:function(e){return y(this,void 0,void 0,function(){return g(this,function(t){return tf(e).Ar(),[2]})})}.bind(null,n),Ri:function(e,n){return y(this,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return n&&tf(e).pr?[4,function(r,i){return y(this,void 0,void 0,function(){var e,n;return g(this,function(t){switch(t.label){case 0:return Os(n=i.code)&&n!==Pr.ABORTED?(e=r.kr.shift(),tf(r).sr(),[4,Wl(r,function(){return r.remoteSyncer.rejectFailedWrite(e.batchId,i)})]):[3,3];case 1:return t.sent(),[4,Yl(r)];case 2:t.sent(),t.label=3;case 3:return[2]}})})}(e,n)]:[3,2];case 1:t.sent(),t.label=2;case 2:return Xl(e)&&$l(e),[2]}})})}.bind(null,n),Ir:function(o){return y(this,void 0,void 0,function(){var e,n,r,i;return g(this,function(t){for(e=tf(o),n=0,r=o.kr;n<r.length;n++)i=r[n],e.Er(i.mutations);return[2]})})}.bind(null,n),Tr:function(r,i,o){return y(this,void 0,void 0,function(){var e,n;return g(this,function(t){switch(t.label){case 0:return e=r.kr.shift(),n=Ru.from(e,i,o),[4,Wl(r,function(){return r.remoteSyncer.applySuccessfulWrite(n)})];case 1:return t.sent(),[4,Yl(r)];case 2:return t.sent(),[2]}})})}.bind(null,n)},t.br(),new Dl(e,t.Hi,t.credentials,t.R,r)),n.Mr.push(function(e){return y(i,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return e?(n.Kr.sr(),[4,Yl(n)]):[3,2];case 1:return t.sent(),[3,4];case 2:return[4,n.Kr.stop()];case 3:t.sent(),0<n.kr.length&&(qr("RemoteStore","Stopping write stream with "+n.kr.length+" pending writes"),n.kr=[]),t.label=4;case 4:return[2]}})})})),n.Kr}var ef=(nf.createAndSchedule=function(t,e,n,r,i){i=new nf(t,e,Date.now()+n,r,i);return i.start(n),i},nf.prototype.start=function(t){var e=this;this.timerHandle=setTimeout(function(){return e.handleDelayElapsed()},t)},nf.prototype.skipDelay=function(){return this.handleDelayElapsed()},nf.prototype.cancel=function(t){null!==this.timerHandle&&(this.clearTimeout(),this.deferred.reject(new Mr(Pr.CANCELLED,"Operation cancelled"+(t?": "+t:""))))},nf.prototype.handleDelayElapsed=function(){var e=this;this.asyncQueue.enqueueAndForget(function(){return null!==e.timerHandle?(e.clearTimeout(),e.op().then(function(t){return e.deferred.resolve(t)})):Promise.resolve()})},nf.prototype.clearTimeout=function(){null!==this.timerHandle&&(this.removalCallback(this),clearTimeout(this.timerHandle),this.timerHandle=null)},nf);function nf(t,e,n,r,i){this.asyncQueue=t,this.timerId=e,this.targetTimeMs=n,this.op=r,this.removalCallback=i,this.deferred=new uu,this.then=this.deferred.promise.then.bind(this.deferred.promise),this.deferred.promise.catch(function(t){})}function rf(t,e){if(Br("AsyncQueue",e+": "+t),bu(t))return new Mr(Pr.UNAVAILABLE,e+": "+t);throw t}function of(){this.queries=new Lc(Vo,Fo),this.onlineState="Unknown",this.Gr=new Set}var sf=(ff.emptySet=function(t){return new ff(t.comparator)},ff.prototype.has=function(t){return null!=this.keyedMap.get(t)},ff.prototype.get=function(t){return this.keyedMap.get(t)},ff.prototype.first=function(){return this.sortedSet.minKey()},ff.prototype.last=function(){return this.sortedSet.maxKey()},ff.prototype.isEmpty=function(){return this.sortedSet.isEmpty()},ff.prototype.indexOf=function(t){t=this.keyedMap.get(t);return t?this.sortedSet.indexOf(t):-1},Object.defineProperty(ff.prototype,"size",{get:function(){return this.sortedSet.size},enumerable:!1,configurable:!0}),ff.prototype.forEach=function(n){this.sortedSet.inorderTraversal(function(t,e){return n(t),!1})},ff.prototype.add=function(t){var e=this.delete(t.key);return e.copy(e.keyedMap.insert(t.key,t),e.sortedSet.insert(t,null))},ff.prototype.delete=function(t){var e=this.get(t);return e?this.copy(this.keyedMap.remove(t),this.sortedSet.remove(e)):this},ff.prototype.isEqual=function(t){if(!(t instanceof ff))return!1;if(this.size!==t.size)return!1;for(var e=this.sortedSet.getIterator(),n=t.sortedSet.getIterator();e.hasNext();){var r=e.getNext().key,i=n.getNext().key;if(!r.isEqual(i))return!1}return!0},ff.prototype.toString=function(){var e=[];return this.forEach(function(t){e.push(t.toString())}),0===e.length?"DocumentSet ()":"DocumentSet (\n  "+e.join("  \n")+"\n)"},ff.prototype.copy=function(t,e){var n=new ff;return n.comparator=this.comparator,n.keyedMap=t,n.sortedSet=e,n},ff),af=(lf.prototype.track=function(t){var e=t.doc.key,n=this.Qr.get(e);!n||0!==t.type&&3===n.type?this.Qr=this.Qr.insert(e,t):3===t.type&&1!==n.type?this.Qr=this.Qr.insert(e,{type:n.type,doc:t.doc}):2===t.type&&2===n.type?this.Qr=this.Qr.insert(e,{type:2,doc:t.doc}):2===t.type&&0===n.type?this.Qr=this.Qr.insert(e,{type:0,doc:t.doc}):1===t.type&&0===n.type?this.Qr=this.Qr.remove(e):1===t.type&&2===n.type?this.Qr=this.Qr.insert(e,{type:1,doc:n.doc}):0===t.type&&1===n.type?this.Qr=this.Qr.insert(e,{type:2,doc:t.doc}):Gr()},lf.prototype.jr=function(){var n=[];return this.Qr.inorderTraversal(function(t,e){n.push(e)}),n},lf),uf=(hf.fromInitialDocuments=function(t,e,n,r){var i=[];return e.forEach(function(t){i.push({type:0,doc:t})}),new hf(t,e,sf.emptySet(e),i,n,r,!0,!1)},Object.defineProperty(hf.prototype,"hasPendingWrites",{get:function(){return!this.mutatedKeys.isEmpty()},enumerable:!1,configurable:!0}),hf.prototype.isEqual=function(t){if(!(this.fromCache===t.fromCache&&this.syncStateChanged===t.syncStateChanged&&this.mutatedKeys.isEqual(t.mutatedKeys)&&Fo(this.query,t.query)&&this.docs.isEqual(t.docs)&&this.oldDocs.isEqual(t.oldDocs)))return!1;var e=this.docChanges,n=t.docChanges;if(e.length!==n.length)return!1;for(var r=0;r<e.length;r++)if(e[r].type!==n[r].type||!e[r].doc.isEqual(n[r].doc))return!1;return!0},hf),cf=function(){this.Wr=void 0,this.listeners=[]};function hf(t,e,n,r,i,o,s,a){this.query=t,this.docs=e,this.oldDocs=n,this.docChanges=r,this.mutatedKeys=i,this.fromCache=o,this.syncStateChanged=s,this.excludesMetadataChanges=a}function lf(){this.Qr=new Ps(Si.comparator)}function ff(n){this.comparator=n?function(t,e){return n(t,e)||Si.comparator(t.key,e.key)}:function(t,e){return Si.comparator(t.key,e.key)},this.keyedMap=zs,this.sortedSet=new Ps(this.comparator)}function df(s,a){return y(this,void 0,void 0,function(){var e,n,r,i,o;return g(this,function(t){switch(t.label){case 0:if(e=s,n=a.query,r=!1,(i=e.queries.get(n))||(r=!0,i=new cf),!r)return[3,4];t.label=1;case 1:return t.trys.push([1,3,,4]),o=i,[4,e.onListen(n)];case 2:return o.Wr=t.sent(),[3,4];case 3:return o=t.sent(),o=rf(o,"Initialization of query '"+Uo(a.query)+"' failed"),[2,void a.onError(o)];case 4:return e.queries.set(n,i),i.listeners.push(a),a.zr(e.onlineState),i.Wr&&a.Hr(i.Wr)&&yf(e),[2]}})})}function pf(s,a){return y(this,void 0,void 0,function(){var e,n,r,i,o;return g(this,function(t){return e=s,n=a.query,r=!1,(i=e.queries.get(n))&&0<=(o=i.listeners.indexOf(a))&&(i.listeners.splice(o,1),r=0===i.listeners.length),r?[2,(e.queries.delete(n),e.onUnlisten(n))]:[2]})})}function yf(t){t.Gr.forEach(function(t){t.next()})}var gf=(If.prototype.Hr=function(t){if(!this.options.includeMetadataChanges){for(var e=[],n=0,r=t.docChanges;n<r.length;n++){var i=r[n];3!==i.type&&e.push(i)}t=new uf(t.query,t.docs,t.oldDocs,e,t.mutatedKeys,t.fromCache,t.syncStateChanged,!0)}var o=!1;return this.Yr?this.Zr(t)&&(this.Jr.next(t),o=!0):this.eo(t,this.onlineState)&&(this.no(t),o=!0),this.Xr=t,o},If.prototype.onError=function(t){this.Jr.error(t)},If.prototype.zr=function(t){this.onlineState=t;var e=!1;return this.Xr&&!this.Yr&&this.eo(this.Xr,t)&&(this.no(this.Xr),e=!0),e},If.prototype.eo=function(t,e){return!t.fromCache||!(this.options.so&&"Offline"!==e||t.docs.isEmpty()&&"Offline"!==e)},If.prototype.Zr=function(t){if(0<t.docChanges.length)return!0;var e=this.Xr&&this.Xr.hasPendingWrites!==t.hasPendingWrites;return!(!t.syncStateChanged&&!e)&&!0===this.options.includeMetadataChanges},If.prototype.no=function(t){t=uf.fromInitialDocuments(t.query,t.docs,t.mutatedKeys,t.fromCache),this.Yr=!0,this.Jr.next(t)},If),mf=(Tf.prototype.io=function(){return"metadata"in this.payload},Tf),vf=(Ef.prototype.qn=function(t){return ba(this.R,t)},Ef.prototype.Un=function(t){return t.metadata.exists?Aa(this.R,t.document,!1):ji.newNoDocument(this.qn(t.metadata.name),this.Kn(t.metadata.readTime))},Ef.prototype.Kn=ga,Ef),wf=(bf.prototype.oo=function(t){this.progress.bytesLoaded+=t.byteLength;var e=this.progress.documentsLoaded;return t.payload.namedQuery?this.queries.push(t.payload.namedQuery):t.payload.documentMetadata?(this.documents.push({metadata:t.payload.documentMetadata}),t.payload.documentMetadata.exists||++e):t.payload.document&&(this.documents[this.documents.length-1].document=t.payload.document,++e),e!==this.progress.documentsLoaded?(this.progress.documentsLoaded=e,Object.assign({},this.progress)):null},bf.prototype.co=function(t){for(var e=new Map,n=new vf(this.R),r=0,i=t;r<i.length;r++){var o=i[r];if(o.metadata.queries)for(var s=n.qn(o.metadata.name),a=0,u=o.metadata.queries;a<u.length;a++){var c=u[a],h=(e.get(c)||Xs()).add(s);e.set(c,h)}}return e},bf.prototype.complete=function(){return y(this,void 0,void 0,function(){var e,n,r,i;return g(this,function(t){switch(t.label){case 0:return[4,function(l,f,d,p){return y(this,void 0,void 0,function(){var n,r,i,o,e,s,a,u,c,h;return g(this,function(t){switch(t.label){case 0:for(n=l,r=Xs(),i=Gs,o=Ws,e=0,s=d;e<s.length;e++)a=s[e],u=f.qn(a.metadata.name),a.document&&(r=r.add(u)),i=i.insert(u,f.Un(a)),o=o.insert(u,f.Kn(a.metadata.readTime));return c=n.On.newChangeBuffer({trackRemovals:!0}),[4,ph(n,Po(No(si.fromString("__bundle__/docs/"+p))))];case 1:return h=t.sent(),[2,n.persistence.runTransaction("Apply bundle documents","readwrite",function(e){return dh(e,c,i,Jr.min(),o).next(function(t){return c.apply(e),t}).next(function(t){return n.qe.removeMatchingKeysForTargetId(e,h.targetId).next(function(){return n.qe.addMatchingKeys(e,r,h.targetId)}).next(function(){return n.Mn.En(e,t)}).next(function(){return t})})})]}})})}(this.localStore,new vf(this.R),this.documents,this.ro.id)];case 1:e=t.sent(),n=this.co(this.documents),r=0,i=this.queries,t.label=2;case 2:return r<i.length?(i=i[r],[4,function(e,i,o){return void 0===o&&(o=Xs()),y(this,void 0,void 0,function(){var n,r;return g(this,function(t){switch(t.label){case 0:return[4,ph(e,Po(Hu(i.bundledQuery)))];case 1:return n=t.sent(),[2,(r=e).persistence.runTransaction("Save named query","readwrite",function(t){var e=ga(i.readTime);if(0<=n.snapshotVersion.compareTo(e))return r.Ke.saveNamedQuery(t,i);e=n.withResumeToken(hi.EMPTY_BYTE_STRING,e);return r.Fn=r.Fn.insert(e.targetId,e),r.qe.updateTargetData(t,e).next(function(){return r.qe.removeMatchingKeysForTargetId(t,n.targetId)}).next(function(){return r.qe.addMatchingKeys(t,o,n.targetId)}).next(function(){return r.Ke.saveNamedQuery(t,i)})})]}})})}(this.localStore,i,n.get(i.name))]):[3,5];case 3:t.sent(),t.label=4;case 4:return r++,[3,2];case 5:return[2,(this.progress.taskState="Success",new Zc(Object.assign({},this.progress),e))]}})})},bf);function bf(t,e,n){this.ro=t,this.localStore=e,this.R=n,this.queries=[],this.documents=[],this.progress=_f(t)}function Ef(t){this.R=t}function Tf(t,e){this.payload=t,this.byteLength=e}function If(t,e,n){this.query=t,this.Jr=e,this.Yr=!1,this.Xr=null,this.onlineState="Unknown",this.options=n||{}}function _f(t){return{taskState:"Running",documentsLoaded:0,bytesLoaded:0,totalDocuments:t.totalDocuments,totalBytes:t.totalBytes}}var Sf=function(t){this.key=t},Af=function(t){this.key=t},Df=(Object.defineProperty(xf.prototype,"wo",{get:function(){return this.uo},enumerable:!1,configurable:!0}),xf.prototype._o=function(t,e){var s=this,a=e?e.mo:new af,u=(e||this).fo,c=(e||this).mutatedKeys,h=u,l=!1,f=Co(this.query)&&u.size===this.query.limit?u.last():null,d=ko(this.query)&&u.size===this.query.limit?u.first():null;if(t.inorderTraversal(function(t,e){var n=u.get(t),r=qo(s.query,e)?e:null,i=!!n&&s.mutatedKeys.has(n.key),o=!!r&&(r.hasLocalMutations||s.mutatedKeys.has(r.key)&&r.hasCommittedMutations),e=!1;n&&r?n.data.isEqual(r.data)?i!==o&&(a.track({type:3,doc:r}),e=!0):s.yo(n,r)||(a.track({type:2,doc:r}),e=!0,(f&&0<s.lo(r,f)||d&&s.lo(r,d)<0)&&(l=!0)):!n&&r?(a.track({type:0,doc:r}),e=!0):n&&!r&&(a.track({type:1,doc:n}),e=!0,(f||d)&&(l=!0)),e&&(c=r?(h=h.add(r),o?c.add(t):c.delete(t)):(h=h.delete(t),c.delete(t)))}),Co(this.query)||ko(this.query))for(;h.size>this.query.limit;){var n=Co(this.query)?h.last():h.first(),h=h.delete(n.key),c=c.delete(n.key);a.track({type:1,doc:n})}return{fo:h,mo:a,Nn:l,mutatedKeys:c}},xf.prototype.yo=function(t,e){return t.hasLocalMutations&&e.hasCommittedMutations&&!e.hasLocalMutations},xf.prototype.applyChanges=function(t,e,n){var o=this,r=this.fo;this.fo=t.fo,this.mutatedKeys=t.mutatedKeys;var i=t.mo.jr();i.sort(function(t,e){return r=t.type,i=e.type,n(r)-n(i)||o.lo(t.doc,e.doc);function n(t){switch(t){case 0:return 1;case 2:case 3:return 2;case 1:return 0;default:return Gr()}}var r,i}),this.po(n);var s=e?this.Eo():[],n=0===this.ho.size&&this.current?1:0,e=n!==this.ao;return this.ao=n,0!==i.length||e?{snapshot:new uf(this.query,t.fo,r,i,t.mutatedKeys,0==n,e,!1),To:s}:{To:s}},xf.prototype.zr=function(t){return this.current&&"Offline"===t?(this.current=!1,this.applyChanges({fo:this.fo,mo:new af,mutatedKeys:this.mutatedKeys,Nn:!1},!1)):{To:[]}},xf.prototype.Io=function(t){return!this.uo.has(t)&&!!this.fo.has(t)&&!this.fo.get(t).hasLocalMutations},xf.prototype.po=function(t){var e=this;t&&(t.addedDocuments.forEach(function(t){return e.uo=e.uo.add(t)}),t.modifiedDocuments.forEach(function(t){}),t.removedDocuments.forEach(function(t){return e.uo=e.uo.delete(t)}),this.current=t.current)},xf.prototype.Eo=function(){var e=this;if(!this.current)return[];var n=this.ho;this.ho=Xs(),this.fo.forEach(function(t){e.Io(t.key)&&(e.ho=e.ho.add(t.key))});var r=[];return n.forEach(function(t){e.ho.has(t)||r.push(new Af(t))}),this.ho.forEach(function(t){n.has(t)||r.push(new Sf(t))}),r},xf.prototype.Ao=function(t){this.uo=t.Bn,this.ho=Xs();t=this._o(t.documents);return this.applyChanges(t,!0)},xf.prototype.Ro=function(){return uf.fromInitialDocuments(this.query,this.fo,this.mutatedKeys,0===this.ao)},xf),Nf=function(t,e,n){this.query=t,this.targetId=e,this.view=n},Cf=function(t){this.key=t,this.bo=!1},kf=(Object.defineProperty(Rf.prototype,"isPrimaryClient",{get:function(){return!0===this.$o},enumerable:!1,configurable:!0}),Rf);function Rf(t,e,n,r,i,o){this.localStore=t,this.remoteStore=e,this.eventManager=n,this.sharedClientState=r,this.currentUser=i,this.maxConcurrentLimboResolutions=o,this.vo={},this.Po=new Lc(Vo,Fo),this.Vo=new Map,this.So=new Set,this.Do=new Ps(Si.comparator),this.Co=new Map,this.No=new Ih,this.xo={},this.Fo=new Map,this.ko=yc.Yt(),this.onlineState="Unknown",this.$o=void 0}function xf(t,e){this.query=t,this.uo=e,this.ao=null,this.current=!1,this.ho=Xs(),this.mutatedKeys=Xs(),this.lo=Bo(t),this.fo=new sf(this.lo)}function Of(i,o,s,a){return y(this,void 0,void 0,function(){var e,n,r;return g(this,function(t){switch(t.label){case 0:return i.Oo=function(t,e,n){return function(r,i,o,s){return y(this,void 0,void 0,function(){var e,n;return g(this,function(t){switch(t.label){case 0:return(e=i.view._o(o)).Nn?[4,gh(r.localStore,i.query,!1).then(function(t){t=t.documents;return i.view._o(t,e)})]:[3,2];case 1:e=t.sent(),t.label=2;case 2:return n=s&&s.targetChanges.get(i.targetId),n=i.view.applyChanges(e,r.isPrimaryClient,n),[2,(Kf(r,i.targetId,n.To),n.snapshot)]}})})}(i,t,e,n)},[4,gh(i.localStore,o,!0)];case 1:return n=t.sent(),r=new Df(o,n.Bn),e=r._o(n.documents),n=Zs.createSynthesizedTargetChangeForCurrentChange(s,a&&"Offline"!==i.onlineState),n=r.applyChanges(e,i.isPrimaryClient,n),Kf(i,s,n.To),r=new Nf(o,s,r),[2,(i.Po.set(o,r),i.Vo.has(s)?i.Vo.get(s).push(o):i.Vo.set(s,[o]),n.snapshot)]}})})}function Lf(f,d,p){return y(this,void 0,void 0,function(){var s,l;return g(this,function(t){switch(t.label){case 0:l=Jf(f),t.label=1;case 1:return t.trys.push([1,5,,6]),[4,(i=l.localStore,a=d,c=i,h=$r.now(),o=a.reduce(function(t,e){return t.add(e.key)},Xs()),c.persistence.runTransaction("Locally write mutations","readwrite",function(s){return c.Mn.pn(s,o).next(function(t){u=t;for(var e=[],n=0,r=a;n<r.length;n++){var i=r[n],o=function(t,e){for(var n=null,r=0,i=t.fieldTransforms;r<i.length;r++){var o=i[r],s=e.data.field(o.field),s=Qo(o.transform,s||null);null!=s&&(n=null==n?qi.empty():n).set(o.field,s)}return n||null}(i,u.get(i.key));null!=o&&e.push(new ws(i.key,o,function s(t){var a=[];return ni(t.fields,function(t,e){var n=new ui([t]);if(Vi(e))if(0===(e=s(e.mapValue).fields).length)a.push(n);else for(var r=0,i=e;r<i.length;r++){var o=i[r];a.push(n.child(o))}else a.push(n)}),new ci(a)}(o.value.mapValue),hs.exists(!0)))}return c._n.addMutationBatch(s,h,e,a)})}).then(function(t){return t.applyToLocalDocumentSet(u),{batchId:t.batchId,changes:u}}))];case 2:return s=t.sent(),l.sharedClientState.addPendingMutation(s.batchId),e=l,n=s.batchId,r=p,i=(i=(i=e.xo[e.currentUser.toKey()])||new Ps(Wr)).insert(n,r),e.xo[e.currentUser.toKey()]=i,[4,Qf(l,s.changes)];case 3:return t.sent(),[4,Yl(l.remoteStore)];case 4:return t.sent(),[3,6];case 5:return l=t.sent(),l=rf(l,"Failed to persist write"),p.reject(l),[3,6];case 6:return[2]}var e,n,r,i,a,u,c,h,o})})}function Pf(r,i){return y(this,void 0,void 0,function(){var n,e;return g(this,function(t){switch(t.label){case 0:n=r,t.label=1;case 1:return t.trys.push([1,4,,6]),[4,fh(n.localStore,i)];case 2:return e=t.sent(),i.targetChanges.forEach(function(t,e){e=n.Co.get(e);e&&(Qr(t.addedDocuments.size+t.modifiedDocuments.size+t.removedDocuments.size<=1),0<t.addedDocuments.size?e.bo=!0:0<t.modifiedDocuments.size?Qr(e.bo):0<t.removedDocuments.size&&(Qr(e.bo),e.bo=!1))}),[4,Qf(n,e,i)];case 3:return t.sent(),[3,6];case 4:return[4,Tc(t.sent())];case 5:return t.sent(),[3,6];case 6:return[2]}})})}function Mf(t,n,e){var r,t=t;(t.isPrimaryClient&&0===e||!t.isPrimaryClient&&1===e)&&(r=[],t.Po.forEach(function(t,e){e=e.view.zr(n);e.snapshot&&r.push(e.snapshot)}),function(t,i){t.onlineState=i;var o=!1;t.queries.forEach(function(t,e){for(var n=0,r=e.listeners;n<r.length;n++)r[n].zr(i)&&(o=!0)}),o&&yf(t)}(t.eventManager,n),r.length&&t.vo._r(r),t.onlineState=n,t.isPrimaryClient&&t.sharedClientState.setOnlineState(n))}function Ff(s,a,u){return y(this,void 0,void 0,function(){var n,o;return g(this,function(t){switch(t.label){case 0:n=s,t.label=1;case 1:return t.trys.push([1,4,,6]),[4,(e=n.localStore,r=a,(i=e).persistence.runTransaction("Reject batch","readwrite-primary",function(e){var n;return i._n.lookupMutationBatch(e,r).next(function(t){return Qr(null!==t),n=t.keys(),i._n.removeMutationBatch(e,t)}).next(function(){return i._n.performConsistencyCheck(e)}).next(function(){return i.Mn.pn(e,n)})}))];case 2:return o=t.sent(),qf(n,a,u),Uf(n,a),n.sharedClientState.updateMutationState(a,"rejected",u),[4,Qf(n,o)];case 3:return t.sent(),[3,6];case 4:return[4,Tc(t.sent())];case 5:return t.sent(),[3,6];case 6:return[2]}var e,r,i})})}function Vf(o,s){return y(this,void 0,void 0,function(){var n,r,i;return g(this,function(t){switch(t.label){case 0:Gl((n=o).remoteStore)||qr("SyncEngine","The network is disabled. The task returned by 'awaitPendingWrites()' will not complete until the network is enabled."),t.label=1;case 1:return t.trys.push([1,3,,4]),[4,(e=n.localStore).persistence.runTransaction("Get highest unacknowledged batch id","readonly",function(t){return e._n.getHighestUnacknowledgedBatchId(t)})];case 2:return-1===(r=t.sent())?[2,void s.resolve()]:((i=n.Fo.get(r)||[]).push(s),n.Fo.set(r,i),[3,4]);case 3:return i=t.sent(),i=rf(i,"Initialization of waitForPendingWrites() operation failed"),s.reject(i),[3,4];case 4:return[2]}var e})})}function Uf(t,e){(t.Fo.get(e)||[]).forEach(function(t){t.resolve()}),t.Fo.delete(e)}function qf(t,e,n){var r=t,i=r.xo[r.currentUser.toKey()];i&&((t=i.get(e))&&(n?t.reject(n):t.resolve(),i=i.remove(e)),r.xo[r.currentUser.toKey()]=i)}function Bf(e,t,n){void 0===n&&(n=null),e.sharedClientState.removeLocalQueryTarget(t);for(var r=0,i=e.Vo.get(t);r<i.length;r++){var o=i[r];e.Po.delete(o),n&&e.vo.Mo(o,n)}e.Vo.delete(t),e.isPrimaryClient&&e.No.Zn(t).forEach(function(t){e.No.containsKey(t)||jf(e,t)})}function jf(t,e){t.So.delete(e.path.canonicalString());var n=t.Do.get(e);null!==n&&(Ul(t.remoteStore,n),t.Do=t.Do.remove(e),t.Co.delete(n),Gf(t))}function Kf(t,e,n){for(var r,i,o,s=0,a=n;s<a.length;s++){var u=a[s];u instanceof Sf?(t.No.addReference(u.key,e),r=t,o=void 0,i=(o=(i=u).key).path.canonicalString(),r.Do.get(o)||r.So.has(i)||(qr("SyncEngine","New document in limbo: "+o),r.So.add(i),Gf(r))):u instanceof Af?(qr("SyncEngine","Document no longer in limbo: "+u.key),t.No.removeReference(u.key,e),t.No.containsKey(u.key)||jf(t,u.key)):Gr()}}function Gf(t){for(;0<t.So.size&&t.Do.size<t.maxConcurrentLimboResolutions;){var e=t.So.values().next().value;t.So.delete(e);var n=new Si(si.fromString(e)),e=t.ko.next();t.Co.set(e,new Cf(n)),t.Do=t.Do.insert(n,e),Vl(t.remoteStore,new xu(Po(No(n.path)),e,2,xr.o))}}function Qf(e,s,a){return y(this,void 0,void 0,function(){var n,r,i,o;return g(this,function(t){switch(t.label){case 0:return r=[],i=[],o=[],(n=e).Po.isEmpty()?[3,3]:(n.Po.forEach(function(t,e){o.push(n.Oo(e,s,a).then(function(t){t&&(n.isPrimaryClient&&n.sharedClientState.updateQueryState(e.targetId,t.fromCache?"not-current":"current"),r.push(t),t=eh.Pn(e.targetId,t),i.push(t))}))}),[4,Promise.all(o)]);case 1:return t.sent(),n.vo._r(r),[4,function(u,c){return y(this,void 0,void 0,function(){var r,e,n,i,o,s,a;return g(this,function(t){switch(t.label){case 0:r=u,t.label=1;case 1:return t.trys.push([1,3,,4]),[4,r.persistence.runTransaction("notifyLocalViewChanges","readwrite",function(n){return cu.forEach(c,function(e){return cu.forEach(e.bn,function(t){return r.persistence.referenceDelegate.addReference(n,e.targetId,t)}).next(function(){return cu.forEach(e.vn,function(t){return r.persistence.referenceDelegate.removeReference(n,e.targetId,t)})})})})];case 2:return t.sent(),[3,4];case 3:if(!bu(e=t.sent()))throw e;return qr("LocalStore","Failed to update sequence numbers: "+e),[3,4];case 4:for(n=0,i=c;n<i.length;n++)a=i[n],o=a.targetId,a.fromCache||(s=r.Fn.get(o),a=s.snapshotVersion,a=s.withLastLimboFreeSnapshotVersion(a),r.Fn=r.Fn.insert(o,a));return[2]}})})}(n.localStore,i)];case 2:t.sent(),t.label=3;case 3:return[2]}})})}function Hf(i,o){return y(this,void 0,void 0,function(){var n,r;return g(this,function(t){switch(t.label){case 0:return(n=i).currentUser.isEqual(o)?[3,3]:(qr("SyncEngine","User change. New user:",o.toKey()),[4,ch(n.localStore,o)]);case 1:return r=t.sent(),n.currentUser=o,(e=n).Fo.forEach(function(t){t.forEach(function(t){t.reject(new Mr(Pr.CANCELLED,"'waitForPendingWrites' promise is rejected due to a user change."))})}),e.Fo.clear(),n.sharedClientState.handleUserChange(o,r.removedBatchIds,r.addedBatchIds),[4,Qf(n,r.Ln)];case 2:t.sent(),t.label=3;case 3:return[2]}var e})})}function zf(a,u,c,h){return y(this,void 0,void 0,function(){var o,s;return g(this,function(t){switch(t.label){case 0:return[4,(e=(o=a).localStore,n=u,i=(r=e)._n,r.persistence.runTransaction("Lookup mutation documents","readonly",function(e){return i.jt(e,n).next(function(t){return t?r.Mn.pn(e,t):cu.resolve(null)})}))];case 1:return null===(s=t.sent())?[3,6]:"pending"!==c?[3,3]:[4,Yl(o.remoteStore)];case 2:return t.sent(),[3,4];case 3:"acknowledged"===c||"rejected"===c?(qf(o,u,h||null),Uf(o,u),o.localStore._n.Gt(u)):Gr(),t.label=4;case 4:return[4,Qf(o,s)];case 5:return t.sent(),[3,7];case 6:qr("SyncEngine","Cannot apply mutation batch with id: "+u),t.label=7;case 7:return[2]}var e,n,r,i})})}function Wf(h,l){return y(this,void 0,void 0,function(){var r,e,i,o,s,a,u,c;return g(this,function(t){switch(t.label){case 0:return $f(r=h),Jf(r),!0!==l||!0===r.$o?[3,3]:(e=r.sharedClientState.getAllActiveQueryTargets(),[4,Yf(r,e.toArray())]);case 1:return i=t.sent(),r.$o=!0,[4,Jl(r.remoteStore,!0)];case 2:for(t.sent(),o=0,s=i;o<s.length;o++)a=s[o],Vl(r.remoteStore,a);return[3,7];case 3:return!1!==l||!1===r.$o?[3,7]:(u=[],c=Promise.resolve(),r.Vo.forEach(function(t,e){r.sharedClientState.isLocalQueryTarget(e)?u.push(e):c=c.then(function(){return Bf(r,e),yh(r.localStore,e,!0)}),Ul(r.remoteStore,e)}),[4,c]);case 4:return t.sent(),[4,Yf(r,u)];case 5:return t.sent(),(n=r).Co.forEach(function(t,e){Ul(n.remoteStore,e)}),n.No.ts(),n.Co=new Map,n.Do=new Ps(Si.comparator),r.$o=!1,[4,Jl(r.remoteStore,!1)];case 6:t.sent(),t.label=7;case 7:return[2]}var n})})}function Yf(d,p){return y(this,void 0,void 0,function(){var e,n,r,i,o,s,a,u,c,h,l,f;return g(this,function(t){switch(t.label){case 0:e=d,n=[],r=[],i=0,o=p,t.label=1;case 1:return i<o.length?(s=o[i],a=void 0,(u=e.Vo.get(s))&&0!==u.length?[4,ph(e.localStore,Po(u[0]))]:[3,7]):[3,13];case 2:a=t.sent(),c=0,h=u,t.label=3;case 3:return c<h.length?(l=h[c],l=e.Po.get(l),[4,function(r,i){return y(this,void 0,void 0,function(){var e,n;return g(this,function(t){switch(t.label){case 0:return[4,gh((e=r).localStore,i.query,!0)];case 1:return n=t.sent(),n=i.view.Ao(n),[2,(e.isPrimaryClient&&Kf(e,i.targetId,n.To),n)]}})})}(e,l)]):[3,6];case 4:(l=t.sent()).snapshot&&r.push(l.snapshot),t.label=5;case 5:return c++,[3,3];case 6:return[3,11];case 7:return[4,mh(e.localStore,s)];case 8:return f=t.sent(),[4,ph(e.localStore,f)];case 9:return a=t.sent(),[4,Of(e,Xf(f),s,!1)];case 10:t.sent(),t.label=11;case 11:n.push(a),t.label=12;case 12:return i++,[3,1];case 13:return[2,(e.vo._r(r),n)]}})})}function Xf(t){return Do(t.path,t.collectionGroup,t.orderBy,t.filters,t.limit,"F",t.startAt,t.endAt)}function $f(t){return t.remoteStore.remoteSyncer.applyRemoteEvent=Pf.bind(null,t),t.remoteStore.remoteSyncer.getRemoteKeysForTarget=function(t,e){var n=t;if((t=n.Co.get(e))&&t.bo)return Xs().add(t.key);var r=Xs();if(!(e=n.Vo.get(e)))return r;for(var i=0,o=e;i<o.length;i++)var s=o[i],s=n.Po.get(s),r=r.unionWith(s.view.wo);return r}.bind(null,t),t.remoteStore.remoteSyncer.rejectListen=function(o,s,a){return y(this,void 0,void 0,function(){var e,n,r,i;return g(this,function(t){switch(t.label){case 0:return(e=o).sharedClientState.updateQueryState(s,"rejected",a),i=e.Co.get(s),(n=i&&i.key)?(r=(r=new Ps(Si.comparator)).insert(n,ji.newNoDocument(n,Jr.min())),i=Xs().add(n),i=new Js(Jr.min(),new Map,new js(Wr),r,i),[4,Pf(e,i)]):[3,2];case 1:return t.sent(),e.Do=e.Do.remove(n),e.Co.delete(s),Gf(e),[3,4];case 2:return[4,yh(e.localStore,s,!1).then(function(){return Bf(e,s,a)}).catch(Tc)];case 3:t.sent(),t.label=4;case 4:return[2]}})})}.bind(null,t),t.vo._r=function(t,e){for(var n=t,r=!1,i=0,o=e;i<o.length;i++){var s=o[i],a=s.query,a=n.queries.get(a);if(a){for(var u=0,c=a.listeners;u<c.length;u++)c[u].Hr(s)&&(r=!0);a.Wr=s}}r&&yf(n)}.bind(null,t.eventManager),t.vo.Mo=function(t,e,n){var r=t;if(t=r.queries.get(e))for(var i=0,o=t.listeners;i<o.length;i++)o[i].onError(n);r.queries.delete(e)}.bind(null,t.eventManager),t}function Jf(t){return t.remoteStore.remoteSyncer.applySuccessfulWrite=function(i,o){return y(this,void 0,void 0,function(){var e,n,r;return g(this,function(t){switch(t.label){case 0:e=i,n=o.batch.batchId,t.label=1;case 1:return t.trys.push([1,4,,6]),[4,hh(e.localStore,o)];case 2:return r=t.sent(),qf(e,n,null),Uf(e,n),e.sharedClientState.updateMutationState(n,"acknowledged"),[4,Qf(e,r)];case 3:return t.sent(),[3,6];case 4:return[4,Tc(t.sent())];case 5:return t.sent(),[3,6];case 6:return[2]}})})}.bind(null,t),t.remoteStore.remoteSyncer.rejectFailedWrite=Ff.bind(null,t),t}function Zf(t,e,n){var r=t;!function(f,d,p){return y(this,void 0,void 0,function(){var a,u,c,h,l;return g(this,function(t){switch(t.label){case 0:return t.trys.push([0,14,,15]),[4,d.getMetadata()];case 1:return a=t.sent(),[4,(r=f.localStore,o=r,s=ga((i=a).createTime),o.persistence.runTransaction("hasNewerBundle","readonly",function(t){return o.Ke.getBundleMetadata(t,i.id)}).then(function(t){return!!t&&0<=t.createTime.compareTo(s)}))];case 2:return t.sent()?[4,d.close()]:[3,4];case 3:return[2,(t.sent(),void p._completeWith({taskState:"Success",documentsLoaded:a.totalDocuments,bytesLoaded:a.totalBytes,totalDocuments:a.totalDocuments,totalBytes:a.totalBytes}))];case 4:return p._updateProgress(_f(a)),u=new wf(a,f.localStore,d.R),[4,d.Lo()];case 5:c=t.sent(),t.label=6;case 6:return c?[4,u.oo(c)]:[3,10];case 7:return(h=t.sent())&&p._updateProgress(h),[4,d.Lo()];case 8:c=t.sent(),t.label=9;case 9:return[3,6];case 10:return[4,u.complete()];case 11:return l=t.sent(),[4,Qf(f,l.wn,void 0)];case 12:return t.sent(),[4,(r=f.localStore,e=a,(n=r).persistence.runTransaction("Save bundle","readwrite",function(t){return n.Ke.saveBundleMetadata(t,e)}))];case 13:return t.sent(),p._completeWith(l.progress),[3,15];case 14:return jr("SyncEngine","Loading bundle failed with "+(l=t.sent())),p._failWith(l),[3,15];case 15:return[2]}var e,n,r,i,o,s})})}(r,e,n).then(function(){r.sharedClientState.notifyBundleLoaded()})}var td,ed,nd=(cd.prototype.initialize=function(e){return y(this,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return this.R=bl(e.databaseInfo.databaseId),this.sharedClientState=this.Bo(e),this.persistence=this.qo(e),[4,this.persistence.start()];case 1:return t.sent(),this.gcScheduler=this.Uo(e),this.localStore=this.Ko(e),[2]}})})},cd.prototype.Uo=function(t){return null},cd.prototype.Ko=function(t){return uh(this.persistence,new nh,t.initialUser,this.R)},cd.prototype.qo=function(t){return new Ch(Rh.bs,this.R)},cd.prototype.Bo=function(t){return new tl},cd.prototype.terminate=function(){return y(this,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return this.gcScheduler&&this.gcScheduler.stop(),[4,this.sharedClientState.shutdown()];case 1:return t.sent(),[4,this.persistence.shutdown()];case 2:return t.sent(),[2]}})})},cd),rd=(n(ud,ed=nd),ud.prototype.initialize=function(e){return y(this,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return[4,ed.prototype.initialize.call(this,e)];case 1:return t.sent(),[4,wh(this.localStore)];case 2:return t.sent(),[4,this.Qo.initialize(this,e)];case 3:return t.sent(),[4,Jf(this.Qo.syncEngine)];case 4:return t.sent(),[4,Yl(this.Qo.remoteStore)];case 5:return t.sent(),[2]}})})},ud.prototype.Ko=function(t){return uh(this.persistence,new nh,t.initialUser,this.R)},ud.prototype.Uo=function(t){var e=this.persistence.referenceDelegate.garbageCollector;return new Sc(e,t.asyncQueue)},ud.prototype.qo=function(t){var e=Jc(t.databaseInfo.databaseId,t.databaseInfo.persistenceKey),n=void 0!==this.cacheSizeBytes?oc.withCacheSize(this.cacheSizeBytes):oc.DEFAULT;return new Wc(this.synchronizeTabs,e,t.clientId,n,t.asyncQueue,vl(),wl(),this.R,this.sharedClientState,!!this.forceOwnership)},ud.prototype.Bo=function(t){return new tl},ud),id=(n(ad,td=rd),ad.prototype.initialize=function(r){return y(this,void 0,void 0,function(){var e,n=this;return g(this,function(t){switch(t.label){case 0:return[4,td.prototype.initialize.call(this,r)];case 1:return t.sent(),e=this.Qo.syncEngine,this.sharedClientState instanceof Zh?(this.sharedClientState.syncEngine={ui:zf.bind(null,e),ai:function(i,o,s,a){return y(this,void 0,void 0,function(){var e,n,r;return g(this,function(t){switch(t.label){case 0:return(e=i).$o?(qr("SyncEngine","Ignoring unexpected query state notification."),[3,8]):[3,1];case 1:if(!e.Vo.has(o))return[3,8];switch(s){case"current":case"not-current":return[3,2];case"rejected":return[3,5]}return[3,7];case 2:return[4,vh(e.localStore)];case 3:return n=t.sent(),r=Js.createSynthesizedRemoteEventForCurrentChange(o,"current"===s),[4,Qf(e,n,r)];case 4:return t.sent(),[3,8];case 5:return[4,yh(e.localStore,o,!0)];case 6:return t.sent(),Bf(e,o,a),[3,8];case 7:Gr(),t.label=8;case 8:return[2]}})})}.bind(null,e),hi:function(c,h,l){return y(this,void 0,void 0,function(){var n,e,r,i,o,s,a,u;return g(this,function(t){switch(t.label){case 0:if(!(n=$f(c)).$o)return[3,10];e=0,r=h,t.label=1;case 1:return e<r.length?(r=r[e],n.Vo.has(r)?(qr("SyncEngine","Adding an already active target "+r),[3,5]):[4,mh(n.localStore,r)]):[3,6];case 2:return i=t.sent(),[4,ph(n.localStore,i)];case 3:return o=t.sent(),[4,Of(n,Xf(i),o.targetId,!1)];case 4:t.sent(),Vl(n.remoteStore,o),t.label=5;case 5:return e++,[3,1];case 6:s=function(e){return g(this,function(t){switch(t.label){case 0:return n.Vo.has(e)?[4,yh(n.localStore,e,!1).then(function(){Ul(n.remoteStore,e),Bf(n,e)}).catch(Tc)]:[3,2];case 1:t.sent(),t.label=2;case 2:return[2]}})},a=0,u=l,t.label=7;case 7:return a<u.length?(u=u[a],[5,s(u)]):[3,10];case 8:t.sent(),t.label=9;case 9:return a++,[3,7];case 10:return[2]}})})}.bind(null,e),fn:function(t){return t.localStore.persistence.fn()}.bind(null,e),ci:function(n){return y(this,void 0,void 0,function(){var e;return g(this,function(t){return[2,vh((e=n).localStore).then(function(t){return Qf(e,t)})]})})}.bind(null,e)},[4,this.sharedClientState.start()]):[3,3];case 2:t.sent(),t.label=3;case 3:return[4,this.persistence.He(function(e){return y(n,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return[4,Wf(this.Qo.syncEngine,e)];case 1:return t.sent(),this.gcScheduler&&(e&&!this.gcScheduler.started?this.gcScheduler.start(this.localStore):e||this.gcScheduler.stop()),[2]}})})})];case 4:return t.sent(),[2]}})})},ad.prototype.Bo=function(t){var e=vl();if(!Zh.yt(e))throw new Mr(Pr.UNIMPLEMENTED,"IndexedDB persistence is only available on platforms that support LocalStorage.");var n=Jc(t.databaseInfo.databaseId,t.databaseInfo.persistenceKey);return new Zh(e,t.asyncQueue,n,t.clientId,t.initialUser)},ad),od=(sd.prototype.initialize=function(n,r){return y(this,void 0,void 0,function(){var e=this;return g(this,function(t){switch(t.label){case 0:return this.localStore?[3,2]:(this.localStore=n.localStore,this.sharedClientState=n.sharedClientState,this.datastore=this.createDatastore(r),this.remoteStore=this.createRemoteStore(r),this.eventManager=this.createEventManager(r),this.syncEngine=this.createSyncEngine(r,!n.synchronizeTabs),this.sharedClientState.onlineStateHandler=function(t){return Mf(e.syncEngine,t,1)},this.remoteStore.remoteSyncer.handleCredentialChange=Hf.bind(null,this.syncEngine),[4,Jl(this.remoteStore,this.syncEngine.isPrimaryClient)]);case 1:t.sent(),t.label=2;case 2:return[2]}})})},sd.prototype.createEventManager=function(t){return new of},sd.prototype.createDatastore=function(t){var e=bl(t.databaseInfo.databaseId),n=(n=t.databaseInfo,new ol(n));return t=t.credentials,new Nl(t,n,e)},sd.prototype.createRemoteStore=function(t){var e=this,n=this.localStore,r=this.datastore,i=t.asyncQueue,o=function(t){return Mf(e.syncEngine,t,0)},t=new(nl.yt()?nl:el);return new El(n,r,i,o,t)},sd.prototype.createSyncEngine=function(t,e){return function(t,e,n,r,i,o,s){o=new kf(t,e,n,r,i,o);return s&&(o.$o=!0),o}(this.localStore,this.remoteStore,this.eventManager,this.sharedClientState,t.initialUser,t.maxConcurrentLimboResolutions,e)},sd.prototype.terminate=function(){return function(n){return y(this,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return e=n,qr("RemoteStore","RemoteStore shutting down."),e.Or.add(5),[4,Fl(e)];case 1:return t.sent(),e.Lr.shutdown(),e.Br.set("Unknown"),[2]}})})}(this.remoteStore)},sd);function sd(){}function ad(t,e){var n=this;return(n=td.call(this,t,e,!1)||this).Qo=t,n.cacheSizeBytes=e,n.synchronizeTabs=!0,n}function ud(t,e,n){var r=this;return(r=ed.call(this)||this).Qo=t,r.cacheSizeBytes=e,r.forceOwnership=n,r.synchronizeTabs=!1,r}function cd(){this.synchronizeTabs=!1}function hd(n,r){void 0===r&&(r=10240);var i=0;return{read:function(){return y(this,void 0,void 0,function(){var e;return g(this,function(t){return i<n.byteLength?(e={value:n.slice(i,i+r),done:!1},[2,(i+=r,e)]):[2,{done:!0}]})})},cancel:function(){return y(this,void 0,void 0,function(){return g(this,function(t){return[2]})})},releaseLock:function(){},closed:Promise.reject("unimplemented")}}var ld=(bd.prototype.next=function(t){this.observer.next&&this.jo(this.observer.next,t)},bd.prototype.error=function(t){this.observer.error?this.jo(this.observer.error,t):console.error("Uncaught Error in snapshot listener:",t)},bd.prototype.Wo=function(){this.muted=!0},bd.prototype.jo=function(t,e){var n=this;this.muted||setTimeout(function(){n.muted||t(e)},0)},bd),fd=(wd.prototype.close=function(){return this.Go.cancel()},wd.prototype.getMetadata=function(){return y(this,void 0,void 0,function(){return g(this,function(t){return[2,this.metadata.promise]})})},wd.prototype.Lo=function(){return y(this,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return[4,this.getMetadata()];case 1:return[2,(t.sent(),this.Ho())]}})})},wd.prototype.Ho=function(){return y(this,void 0,void 0,function(){var e,n,r;return g(this,function(t){switch(t.label){case 0:return[4,this.Jo()];case 1:return null===(e=t.sent())?[2,null]:(r=this.zo.decode(e),n=Number(r),isNaN(n)&&this.Yo("length string ("+r+") is not valid number"),[4,this.Xo(n)]);case 2:return r=t.sent(),[2,new mf(JSON.parse(r),e.length+n)]}})})},wd.prototype.Zo=function(){return this.buffer.findIndex(function(t){return t==="{".charCodeAt(0)})},wd.prototype.Jo=function(){return y(this,void 0,void 0,function(){var e,n;return g(this,function(t){switch(t.label){case 0:return this.Zo()<0?[4,this.tc()]:[3,3];case 1:if(t.sent())return[3,3];t.label=2;case 2:return[3,0];case 3:return 0===this.buffer.length?[2,null]:((e=this.Zo())<0&&this.Yo("Reached the end of bundle when a length string is expected."),n=this.buffer.slice(0,e),[2,(this.buffer=this.buffer.slice(e),n)])}})})},wd.prototype.Xo=function(n){return y(this,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return this.buffer.length<n?[4,this.tc()]:[3,3];case 1:t.sent()&&this.Yo("Reached the end of bundle when more is expected."),t.label=2;case 2:return[3,0];case 3:return e=this.zo.decode(this.buffer.slice(0,n)),[2,(this.buffer=this.buffer.slice(n),e)]}})})},wd.prototype.Yo=function(t){throw this.Go.cancel(),new Error("Invalid bundle format: "+t)},wd.prototype.tc=function(){return y(this,void 0,void 0,function(){var e,n;return g(this,function(t){switch(t.label){case 0:return[4,this.Go.read()];case 1:return(e=t.sent()).done||((n=new Uint8Array(this.buffer.length+e.value.length)).set(this.buffer),n.set(e.value,this.buffer.length),this.buffer=n),[2,e.done]}})})},wd),dd=(vd.prototype.lookup=function(r){return y(this,void 0,void 0,function(){var e,n=this;return g(this,function(t){switch(t.label){case 0:if(this.ensureCommitNotCalled(),0<this.mutations.length)throw new Mr(Pr.INVALID_ARGUMENT,"Firestore transactions require all reads to be executed before all writes.");return[4,function(s,a){return y(this,void 0,void 0,function(){var n,e,r,i,o;return g(this,function(t){switch(t.label){case 0:return e=Ia((n=s).R)+"/documents",r={documents:a.map(function(t){return wa(n.R,t)})},[4,n.$i("BatchGetDocuments",e,r)];case 1:return r=t.sent(),i=new Map,r.forEach(function(t){var e,t=(e=n.R,"found"in(t=t)?function(t,e){Qr(!!e.found),e.found.name,e.found.updateTime;var n=ba(t,e.found.name),t=ga(e.found.updateTime),e=new qi({mapValue:{fields:e.found.fields}});return ji.newFoundDocument(n,t,e)}(e,t):"missing"in t?function(t,e){Qr(!!e.missing),Qr(!!e.readTime);t=ba(t,e.missing),e=ga(e.readTime);return ji.newNoDocument(t,e)}(e,t):Gr());i.set(t.key.toString(),t)}),o=[],[2,(a.forEach(function(t){t=i.get(t.toString());Qr(!!t),o.push(t)}),o)]}})})}(this.datastore,r)];case 1:return[2,((e=t.sent()).forEach(function(t){return n.recordVersion(t)}),e)]}})})},vd.prototype.set=function(t,e){this.write(e.toMutation(t,this.precondition(t))),this.writtenDocs.add(t.toString())},vd.prototype.update=function(t,e){try{this.write(e.toMutation(t,this.preconditionForUpdate(t)))}catch(t){this.lastWriteError=t}this.writtenDocs.add(t.toString())},vd.prototype.delete=function(t){this.write(new Cs(t,this.precondition(t))),this.writtenDocs.add(t.toString())},vd.prototype.commit=function(){return y(this,void 0,void 0,function(){var e,n=this;return g(this,function(t){switch(t.label){case 0:if(this.ensureCommitNotCalled(),this.lastWriteError)throw this.lastWriteError;return e=this.readVersions,this.mutations.forEach(function(t){e.delete(t.key.toString())}),e.forEach(function(t,e){e=Si.fromPath(e);n.mutations.push(new ks(e,n.precondition(e)))}),[4,function(i,o){return y(this,void 0,void 0,function(){var e,n,r;return g(this,function(t){switch(t.label){case 0:return n=Ia((e=i).R)+"/documents",r={writes:o.map(function(t){return Da(e.R,t)})},[4,e.Ni("Commit",n,r)];case 1:return t.sent(),[2]}})})}(this.datastore,this.mutations)];case 1:return t.sent(),this.committed=!0,[2]}})})},vd.prototype.recordVersion=function(t){var e;if(t.isFoundDocument())e=t.version;else{if(!t.isNoDocument())throw Gr();e=Jr.min()}var n=this.readVersions.get(t.key.toString());if(n){if(!e.isEqual(n))throw new Mr(Pr.ABORTED,"Document version changed between two reads.")}else this.readVersions.set(t.key.toString(),e)},vd.prototype.precondition=function(t){var e=this.readVersions.get(t.toString());return!this.writtenDocs.has(t.toString())&&e?hs.updateTime(e):hs.none()},vd.prototype.preconditionForUpdate=function(t){var e=this.readVersions.get(t.toString());if(this.writtenDocs.has(t.toString())||!e)return hs.exists(!0);if(e.isEqual(Jr.min()))throw new Mr(Pr.INVALID_ARGUMENT,"Can't update a document that doesn't exist.");return hs.updateTime(e)},vd.prototype.write=function(t){this.ensureCommitNotCalled(),this.mutations.push(t)},vd.prototype.ensureCommitNotCalled=function(){},vd),pd=(md.prototype.run=function(){--this.ec,this.nc()},md.prototype.nc=function(){var t=this;this.Zi.ji(function(){return y(t,void 0,void 0,function(){var e,n,r=this;return g(this,function(t){return e=new dd(this.datastore),(n=this.sc(e))&&n.then(function(t){r.asyncQueue.enqueueAndForget(function(){return e.commit().then(function(){r.deferred.resolve(t)}).catch(function(t){r.ic(t)})})}).catch(function(t){r.ic(t)}),[2]})})})},md.prototype.sc=function(t){try{var e=this.updateFunction(t);return!Ti(e)&&e.catch&&e.then?e:(this.deferred.reject(Error("Transaction callback must return a Promise")),null)}catch(t){return this.deferred.reject(t),null}},md.prototype.ic=function(t){var e=this;0<this.ec&&this.rc(t)?(--this.ec,this.asyncQueue.enqueueAndForget(function(){return e.nc(),Promise.resolve()})):this.deferred.reject(t)},md.prototype.rc=function(t){if("FirebaseError"!==t.name)return!1;t=t.code;return"aborted"===t||"failed-precondition"===t||!Os(t)},md),yd=(gd.prototype.getConfiguration=function(){return y(this,void 0,void 0,function(){return g(this,function(t){return[2,{asyncQueue:this.asyncQueue,databaseInfo:this.databaseInfo,clientId:this.clientId,credentials:this.credentials,initialUser:this.user,maxConcurrentLimboResolutions:100}]})})},gd.prototype.setCredentialChangeListener=function(t){this.credentialListener=t},gd.prototype.verifyNotTerminated=function(){if(this.asyncQueue.isShuttingDown)throw new Mr(Pr.FAILED_PRECONDITION,"The client has already been terminated.")},gd.prototype.terminate=function(){var t=this;this.asyncQueue.enterRestrictedMode();var n=new uu;return this.asyncQueue.enqueueAndForgetEvenWhileRestricted(function(){return y(t,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return t.trys.push([0,5,,6]),this.onlineComponents?[4,this.onlineComponents.terminate()]:[3,2];case 1:t.sent(),t.label=2;case 2:return this.offlineComponents?[4,this.offlineComponents.terminate()]:[3,4];case 3:t.sent(),t.label=4;case 4:return this.credentials.removeChangeListener(),n.resolve(),[3,6];case 5:return e=t.sent(),e=rf(e,"Failed to shutdown persistence"),n.reject(e),[3,6];case 6:return[2]}})})}),n.promise},gd);function gd(t,e,n){var r=this;this.credentials=t,this.asyncQueue=e,this.databaseInfo=n,this.user=xh.UNAUTHENTICATED,this.clientId=Hr.u(),this.credentialListener=function(){return Promise.resolve()},this.credentials.setChangeListener(e,function(e){return y(r,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return qr("FirestoreClient","Received user=",e.uid),[4,this.credentialListener(e)];case 1:return t.sent(),this.user=e,[2]}})})})}function md(t,e,n,r){this.asyncQueue=t,this.datastore=e,this.updateFunction=n,this.deferred=r,this.ec=5,this.Zi=new Sl(this.asyncQueue,"transaction_retry")}function vd(t){this.datastore=t,this.readVersions=new Map,this.mutations=[],this.committed=!1,this.lastWriteError=null,this.writtenDocs=new Set}function wd(t,e){var n=this;this.Go=t,this.R=e,this.metadata=new uu,this.buffer=new Uint8Array,this.zo=new TextDecoder("utf-8"),this.Ho().then(function(t){t&&t.io()?n.metadata.resolve(t.payload.metadata):n.metadata.reject(new Error("The first element of the bundle is not a metadata, it is\n             "+JSON.stringify(null==t?void 0:t.payload)))},function(t){return n.metadata.reject(t)})}function bd(t){this.observer=t,this.muted=!1}function Ed(i,o){return y(this,void 0,void 0,function(){var e,n,r=this;return g(this,function(t){switch(t.label){case 0:return i.asyncQueue.verifyOperationInProgress(),qr("FirestoreClient","Initializing OfflineComponentProvider"),[4,i.getConfiguration()];case 1:return e=t.sent(),[4,o.initialize(e)];case 2:return t.sent(),n=e.initialUser,i.setCredentialChangeListener(function(e){return y(r,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return n.isEqual(e)?[3,2]:[4,ch(o.localStore,e)];case 1:t.sent(),n=e,t.label=2;case 2:return[2]}})})}),o.persistence.setDatabaseDeletedListener(function(){return i.terminate()}),i.offlineComponents=o,[2]}})})}function Td(r,i){return y(this,void 0,void 0,function(){var e,n;return g(this,function(t){switch(t.label){case 0:return r.asyncQueue.verifyOperationInProgress(),[4,Id(r)];case 1:return e=t.sent(),qr("FirestoreClient","Initializing OnlineComponentProvider"),[4,r.getConfiguration()];case 2:return n=t.sent(),[4,i.initialize(e,n)];case 3:return t.sent(),r.setCredentialChangeListener(function(t){return function(r,i){return y(this,void 0,void 0,function(){var e,n;return g(this,function(t){switch(t.label){case 0:return(e=r).asyncQueue.verifyOperationInProgress(),qr("RemoteStore","RemoteStore received new credentials"),n=Gl(e),e.Or.add(3),[4,Fl(e)];case 1:return t.sent(),n&&e.Br.set("Unknown"),[4,e.remoteSyncer.handleCredentialChange(i)];case 2:return t.sent(),e.Or.delete(3),[4,Ml(e)];case 3:return t.sent(),[2]}})})}(i.remoteStore,t)}),r.onlineComponents=i,[2]}})})}function Id(e){return y(this,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return e.offlineComponents?[3,2]:(qr("FirestoreClient","Using default OfflineComponentProvider"),[4,Ed(e,new nd)]);case 1:t.sent(),t.label=2;case 2:return[2,e.offlineComponents]}})})}function _d(e){return y(this,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return e.onlineComponents?[3,2]:(qr("FirestoreClient","Using default OnlineComponentProvider"),[4,Td(e,new od)]);case 1:t.sent(),t.label=2;case 2:return[2,e.onlineComponents]}})})}function Sd(t){return Id(t).then(function(t){return t.persistence})}function Ad(t){return Id(t).then(function(t){return t.localStore})}function Dd(t){return _d(t).then(function(t){return t.remoteStore})}function Nd(t){return _d(t).then(function(t){return t.syncEngine})}function Cd(r){return y(this,void 0,void 0,function(){var e,n;return g(this,function(t){switch(t.label){case 0:return[4,_d(r)];case 1:return e=t.sent(),[2,((n=e.eventManager).onListen=function(s,a){return y(this,void 0,void 0,function(){var e,n,r,i,o;return g(this,function(t){switch(t.label){case 0:return e=$f(s),(o=e.Po.get(a))?(n=o.targetId,e.sharedClientState.addLocalQueryTarget(n),r=o.view.Ro(),[3,4]):[3,1];case 1:return[4,ph(e.localStore,Po(a))];case 2:return i=t.sent(),o=e.sharedClientState.addLocalQueryTarget(i.targetId),n=i.targetId,[4,Of(e,a,n,"current"===o)];case 3:r=t.sent(),e.isPrimaryClient&&Vl(e.remoteStore,i),t.label=4;case 4:return[2,r]}})})}.bind(null,e.syncEngine),n.onUnlisten=function(i,o){return y(this,void 0,void 0,function(){var e,n,r;return g(this,function(t){switch(t.label){case 0:return n=(e=i).Po.get(o),1<(r=e.Vo.get(n.targetId)).length?[2,(e.Vo.set(n.targetId,r.filter(function(t){return!Fo(t,o)})),void e.Po.delete(o))]:e.isPrimaryClient?(e.sharedClientState.removeLocalQueryTarget(n.targetId),e.sharedClientState.isActiveQueryTarget(n.targetId)?[3,2]:[4,yh(e.localStore,n.targetId,!1).then(function(){e.sharedClientState.clearQueryState(n.targetId),Ul(e.remoteStore,n.targetId),Bf(e,n.targetId)}).catch(Tc)]):[3,3];case 1:t.sent(),t.label=2;case 2:return[3,5];case 3:return Bf(e,n.targetId),[4,yh(e.localStore,n.targetId,!0)];case 4:t.sent(),t.label=5;case 5:return[2]}})})}.bind(null,e.syncEngine),n)]}})})}function kd(n,r,i){var t=this;void 0===i&&(i={});var o=new uu;return n.asyncQueue.enqueueAndForget(function(){return y(t,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return e=function(n,r,i,o,s){var t=new ld({next:function(t){r.enqueueAndForget(function(){return pf(n,a)});var e=t.docs.has(i);!e&&t.fromCache?s.reject(new Mr(Pr.UNAVAILABLE,"Failed to get document because the client is offline.")):e&&t.fromCache&&o&&"server"===o.source?s.reject(new Mr(Pr.UNAVAILABLE,'Failed to get document from server. (However, this document does exist in the local cache. Run again without setting source to "server" to retrieve the cached document.)')):s.resolve(t)},error:function(t){return s.reject(t)}}),a=new gf(No(i.path),t,{includeMetadataChanges:!0,so:!0});return df(n,a)},[4,Cd(n)];case 1:return[2,e.apply(void 0,[t.sent(),n.asyncQueue,r,i,o])]}})})}),o.promise}function Rd(n,r,i){var t=this;void 0===i&&(i={});var o=new uu;return n.asyncQueue.enqueueAndForget(function(){return y(t,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return e=function(e,n,t,r,i){var o=new ld({next:function(t){n.enqueueAndForget(function(){return pf(e,s)}),t.fromCache&&"server"===r.source?i.reject(new Mr(Pr.UNAVAILABLE,'Failed to get documents from server. (However, these documents may exist in the local cache. Run again without setting source to "server" to retrieve the cached documents.)')):i.resolve(t)},error:function(t){return i.reject(t)}}),s=new gf(t,o,{includeMetadataChanges:!0,so:!0});return df(e,s)},[4,Cd(n)];case 1:return[2,e.apply(void 0,[t.sent(),n.asyncQueue,r,i,o])]}})})}),o.promise}function xd(t,e){this.user=e,this.type="OAuth",this.authHeaders={},this.authHeaders.Authorization="Bearer "+t}var Od=function(t,e,n,r,i,o,s,a){this.databaseId=t,this.appId=e,this.persistenceKey=n,this.host=r,this.ssl=i,this.forceLongPolling=o,this.autoDetectLongPolling=s,this.useFetchStreams=a},Ld=(Object.defineProperty(Hd.prototype,"isDefaultDatabase",{get:function(){return"(default)"===this.database},enumerable:!1,configurable:!0}),Hd.prototype.isEqual=function(t){return t instanceof Hd&&t.projectId===this.projectId&&t.database===this.database},Hd),Pd=new Map,Md=(Qd.prototype.getToken=function(){return Promise.resolve(null)},Qd.prototype.invalidateToken=function(){},Qd.prototype.setChangeListener=function(t,e){this.changeListener=e,t.enqueueRetryable(function(){return e(xh.UNAUTHENTICATED)})},Qd.prototype.removeChangeListener=function(){this.changeListener=null},Qd),Fd=(Gd.prototype.getToken=function(){return Promise.resolve(this.token)},Gd.prototype.invalidateToken=function(){},Gd.prototype.setChangeListener=function(t,e){var n=this;this.changeListener=e,t.enqueueRetryable(function(){return e(n.token.user)})},Gd.prototype.removeChangeListener=function(){this.changeListener=null},Gd),Vd=(Kd.prototype.getToken=function(){var e=this,n=this.cc,t=this.forceRefresh;return this.forceRefresh=!1,this.auth?this.auth.getToken(t).then(function(t){return e.cc!==n?(qr("FirebaseCredentialsProvider","getToken aborted due to token change."),e.getToken()):t?(Qr("string"==typeof t.accessToken),new xd(t.accessToken,e.currentUser)):null}):Promise.resolve(null)},Kd.prototype.invalidateToken=function(){this.forceRefresh=!0},Kd.prototype.setChangeListener=function(t,e){var n=this;this.asyncQueue=t,this.asyncQueue.enqueueRetryable(function(){return y(n,void 0,void 0,function(){return g(this,function(t){switch(t.label){case 0:return[4,this.oc.promise];case 1:return t.sent(),[4,e(this.currentUser)];case 2:return t.sent(),this.changeListener=e,[2]}})})})},Kd.prototype.removeChangeListener=function(){this.auth&&this.auth.removeAuthTokenListener(this.uc),this.changeListener=function(){return Promise.resolve()}},Kd.prototype.ac=function(){var t=this.auth&&this.auth.getUid();return Qr(null===t||"string"==typeof t),new xh(t)},Kd),Ud=(Object.defineProperty(jd.prototype,"authHeaders",{get:function(){var t={"X-Goog-AuthUser":this.lc},e=this.hc.auth.getAuthHeaderValueForFirstParty([]);return e&&(t.Authorization=e),this.fc&&(t["X-Goog-Iam-Authorization-Token"]=this.fc),t},enumerable:!1,configurable:!0}),jd),qd=(Bd.prototype.getToken=function(){return Promise.resolve(new Ud(this.hc,this.lc,this.fc))},Bd.prototype.setChangeListener=function(t,e){t.enqueueRetryable(function(){return e(xh.FIRST_PARTY)})},Bd.prototype.removeChangeListener=function(){},Bd.prototype.invalidateToken=function(){},Bd);function Bd(t,e,n){this.hc=t,this.lc=e,this.fc=n}function jd(t,e,n){this.hc=t,this.lc=e,this.fc=n,this.type="FirstParty",this.user=xh.FIRST_PARTY}function Kd(e){var n=this;this.currentUser=xh.UNAUTHENTICATED,this.oc=new uu,this.cc=0,this.forceRefresh=!1,this.auth=null,this.asyncQueue=null,this.uc=function(){n.cc++,n.currentUser=n.ac(),n.oc.resolve(),n.changeListener&&n.asyncQueue.enqueueRetryable(function(){return n.changeListener(n.currentUser)})};function r(t){qr("FirebaseCredentialsProvider","Auth detected"),n.auth=t,n.auth.addAuthTokenListener(n.uc)}e.onInit(r),setTimeout(function(){var t;n.auth||((t=e.getImmediate({optional:!0}))?r(t):(qr("FirebaseCredentialsProvider","Auth not yet detected"),n.oc.resolve()))},0)}function Gd(t){this.token=t,this.changeListener=null}function Qd(){this.changeListener=null}function Hd(t,e){this.projectId=t,this.database=e||"(default)"}function zd(t,e,n){if(!n)throw new Mr(Pr.INVALID_ARGUMENT,"Function "+t+"() cannot be called with an empty "+e+".")}function Wd(t,e){if(void 0===e)return{merge:!1};if(void 0!==e.mergeFields&&void 0!==e.merge)throw new Mr(Pr.INVALID_ARGUMENT,"Invalid options passed to function "+t+'(): You cannot specify both "merge" and "mergeFields".');return e}function Yd(t,e,n,r){if(!0===e&&!0===r)throw new Mr(Pr.INVALID_ARGUMENT,t+" and "+n+" cannot be used together.")}function Xd(t){if(!Si.isDocumentKey(t))throw new Mr(Pr.INVALID_ARGUMENT,"Invalid document reference. Document references must have an even number of segments, but "+t+" has "+t.length+".")}function $d(t){if(Si.isDocumentKey(t))throw new Mr(Pr.INVALID_ARGUMENT,"Invalid collection reference. Collection references must have an odd number of segments, but "+t+" has "+t.length+".")}function Jd(e){if(void 0===e)return"undefined";if(null===e)return"null";if("string"==typeof e)return 20<e.length&&(e=e.substring(0,20)+"..."),JSON.stringify(e);if("number"==typeof e||"boolean"==typeof e)return""+e;if("object"!=typeof e)return"function"==typeof e?"a function":Gr();if(e instanceof Array)return"an array";var t=function(){if(e.constructor){var t=/function\s+([^\s(]+)\s*\(/.exec(e.constructor.toString());if(t&&1<t.length)return t[1]}return null}();return t?"a custom "+t+" object":"an object"}function Zd(t,e){if((t="_delegate"in t?t._delegate:t)instanceof e)return t;if(e.name===t.constructor.name)throw new Mr(Pr.INVALID_ARGUMENT,"Type does not match the expected instance. Did you pass a reference from a different Firestore SDK?");t=Jd(t);throw new Mr(Pr.INVALID_ARGUMENT,"Expected type '"+e.name+"', but it was: "+t)}function tp(t,e){if(e<=0)throw new Mr(Pr.INVALID_ARGUMENT,"Function "+t+"() requires a positive number, but it was: "+e+".")}var ep,np=(lp.prototype.isEqual=function(t){return this.host===t.host&&this.ssl===t.ssl&&this.credentials===t.credentials&&this.cacheSizeBytes===t.cacheSizeBytes&&this.experimentalForceLongPolling===t.experimentalForceLongPolling&&this.experimentalAutoDetectLongPolling===t.experimentalAutoDetectLongPolling&&this.ignoreUndefinedProperties===t.ignoreUndefinedProperties&&this.useFetchStreams===t.useFetchStreams},lp),rp=(Object.defineProperty(hp.prototype,"app",{get:function(){if(!this._app)throw new Mr(Pr.FAILED_PRECONDITION,"Firestore was not initialized using the Firebase SDK. 'app' is not available");return this._app},enumerable:!1,configurable:!0}),Object.defineProperty(hp.prototype,"_initialized",{get:function(){return this._settingsFrozen},enumerable:!1,configurable:!0}),Object.defineProperty(hp.prototype,"_terminated",{get:function(){return void 0!==this._terminateTask},enumerable:!1,configurable:!0}),hp.prototype._setSettings=function(t){if(this._settingsFrozen)throw new Mr(Pr.FAILED_PRECONDITION,"Firestore has already been started and its settings can no longer be changed. You can only modify settings before calling any other methods on a Firestore object.");this._settings=new np(t),void 0!==t.credentials&&(this._credentials=function(t){if(!t)return new Md;switch(t.type){case"gapi":var e=t.client;return Qr(!("object"!=typeof e||null===e||!e.auth||!e.auth.getAuthHeaderValueForFirstParty)),new qd(e,t.sessionIndex||"0",t.iamToken||null);case"provider":return t.client;default:throw new Mr(Pr.INVALID_ARGUMENT,"makeCredentialsProvider failed due to invalid credential type")}}(t.credentials))},hp.prototype._getSettings=function(){return this._settings},hp.prototype._freezeSettings=function(){return this._settingsFrozen=!0,this._settings},hp.prototype._delete=function(){return this._terminateTask||(this._terminateTask=this._terminate()),this._terminateTask},hp.prototype.toJSON=function(){return{app:this._app,databaseId:this._databaseId,settings:this._settings}},hp.prototype._terminate=function(){return(t=Pd.get(this))&&(qr("ComponentProvider","Removing Datastore"),Pd.delete(this),t.terminate()),Promise.resolve();var t},hp),ip=(Object.defineProperty(cp.prototype,"_path",{get:function(){return this._key.path},enumerable:!1,configurable:!0}),Object.defineProperty(cp.prototype,"id",{get:function(){return this._key.path.lastSegment()},enumerable:!1,configurable:!0}),Object.defineProperty(cp.prototype,"path",{get:function(){return this._key.path.canonicalString()},enumerable:!1,configurable:!0}),Object.defineProperty(cp.prototype,"parent",{get:function(){return new sp(this.firestore,this.converter,this._key.path.popLast())},enumerable:!1,configurable:!0}),cp.prototype.withConverter=function(t){return new cp(this.firestore,t,this._key)},cp),op=(up.prototype.withConverter=function(t){return new up(this.firestore,t,this._query)},up),sp=(n(ap,ep=op),Object.defineProperty(ap.prototype,"id",{get:function(){return this._query.path.lastSegment()},enumerable:!1,configurable:!0}),Object.defineProperty(ap.prototype,"path",{get:function(){return this._query.path.canonicalString()},enumerable:!1,configurable:!0}),Object.defineProperty(ap.prototype,"parent",{get:function(){var t=this._path.popLast();return t.isEmpty()?null:new ip(this.firestore,null,new Si(t))},enumerable:!1,configurable:!0}),ap.prototype.withConverter=function(t){return new ap(this.firestore,t,this._path)},ap);function ap(t,e,n){var r=this;return(r=ep.call(this,t,e,No(n))||this)._path=n,r.type="collection",r}function up(t,e,n){this.converter=e,this._query=n,this.type="query",this.firestore=t}function cp(t,e,n){this.converter=e,this._key=n,this.type="document",this.firestore=t}function hp(t,e){this.type="firestore-lite",this._persistenceKey="(lite)",this._settings=new np({}),this._settingsFrozen=!1,t instanceof Ld?(this._databaseId=t,this._credentials=new Md):(this._app=t,this._databaseId=function(t){if(!Object.prototype.hasOwnProperty.apply(t.options,["projectId"]))throw new Mr(Pr.INVALID_ARGUMENT,'"projectId" not provided in firebase.initializeApp.');return new Ld(t.options.projectId)}(t),this._credentials=new Vd(e))}function lp(t){var e;if(void 0===t.host){if(void 0!==t.ssl)throw new Mr(Pr.INVALID_ARGUMENT,"Can't provide ssl option if host option is not set");this.host="firestore.googleapis.com",this.ssl=!0}else this.host=t.host,this.ssl=null===(e=t.ssl)||void 0===e||e;if(this.credentials=t.credentials,this.ignoreUndefinedProperties=!!t.ignoreUndefinedProperties,void 0===t.cacheSizeBytes)this.cacheSizeBytes=41943040;else{if(-1!==t.cacheSizeBytes&&t.cacheSizeBytes<1048576)throw new Mr(Pr.INVALID_ARGUMENT,"cacheSizeBytes must be at least 1048576");this.cacheSizeBytes=t.cacheSizeBytes}this.experimentalForceLongPolling=!!t.experimentalForceLongPolling,this.experimentalAutoDetectLongPolling=!!t.experimentalAutoDetectLongPolling,this.useFetchStreams=!!t.useFetchStreams,Yd("experimentalForceLongPolling",t.experimentalForceLongPolling,"experimentalAutoDetectLongPolling",t.experimentalAutoDetectLongPolling)}function fp(t,e){for(var n,r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];if(t=v(t),zd("collection","path",e),t instanceof rp)return $d(n=si.fromString.apply(si,s([e],r))),new sp(t,null,n);if(!(t instanceof ip||t instanceof sp))throw new Mr(Pr.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore");return $d(n=si.fromString.apply(si,s([t.path],r)).child(si.fromString(e))),new sp(t.firestore,null,n)}function dp(t,e){for(var n,r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];if(t=v(t),zd("doc","path",e=1===arguments.length?Hr.u():e),t instanceof rp)return Xd(n=si.fromString.apply(si,s([e],r))),new ip(t,null,new Si(n));if(!(t instanceof ip||t instanceof sp))throw new Mr(Pr.INVALID_ARGUMENT,"Expected first argument to collection() to be a CollectionReference, a DocumentReference or FirebaseFirestore");return Xd(n=t._path.child(si.fromString.apply(si,s([e],r)))),new ip(t.firestore,t instanceof sp?t.converter:null,new Si(n))}function pp(t,e){return t=v(t),e=v(e),(t instanceof ip||t instanceof sp)&&(e instanceof ip||e instanceof sp)&&t.firestore===e.firestore&&t.path===e.path&&t.converter===e.converter}function yp(t,e){return t=v(t),e=v(e),t instanceof op&&e instanceof op&&t.firestore===e.firestore&&Fo(t._query,e._query)&&t.converter===e.converter}var gp=(Object.defineProperty(mp.prototype,"isShuttingDown",{get:function(){return this._c},enumerable:!1,configurable:!0}),mp.prototype.enqueueAndForget=function(t){this.enqueue(t)},mp.prototype.enqueueAndForgetEvenWhileRestricted=function(t){this.Ic(),this.Ac(t)},mp.prototype.enterRestrictedMode=function(){var t;this._c||(this._c=!0,(t=wl())&&"function"==typeof t.removeEventListener&&t.removeEventListener("visibilitychange",this.Tc))},mp.prototype.enqueue=function(t){return this.Ic(),this._c?new Promise(function(t){}):this.Ac(t)},mp.prototype.enqueueRetryable=function(t){var e=this;this.enqueueAndForget(function(){return e.wc.push(t),e.Rc()})},mp.prototype.Rc=function(){return y(this,void 0,void 0,function(){var e,n=this;return g(this,function(t){switch(t.label){case 0:if(0===this.wc.length)return[3,5];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,this.wc[0]()];case 2:return t.sent(),this.wc.shift(),this.Zi.reset(),[3,4];case 3:if(!bu(e=t.sent()))throw e;return qr("AsyncQueue","Operation failed with retryable error: "+e),[3,4];case 4:0<this.wc.length&&this.Zi.ji(function(){return n.Rc()}),t.label=5;case 5:return[2]}})})},mp.prototype.Ac=function(t){var r=this,e=this.dc.then(function(){return r.gc=!0,t().catch(function(t){throw r.yc=t,r.gc=!1,Br("INTERNAL UNHANDLED ERROR: ",(n=(e=t).message||"",n=e.stack?e.stack.includes(e.message)?e.stack:e.message+"\n"+e.stack:n)),t;var e,n}).then(function(t){return r.gc=!1,t})});return this.dc=e},mp.prototype.enqueueAfterDelay=function(t,e,n){var r=this;this.Ic(),-1<this.Ec.indexOf(t)&&(e=0);n=ef.createAndSchedule(this,t,e,n,function(t){return r.bc(t)});return this.mc.push(n),n},mp.prototype.Ic=function(){this.yc&&Gr()},mp.prototype.verifyOperationInProgress=function(){},mp.prototype.vc=function(){return y(this,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return[4,e=this.dc];case 1:t.sent(),t.label=2;case 2:if(e!==this.dc)return[3,0];t.label=3;case 3:return[2]}})})},mp.prototype.Pc=function(t){for(var e=0,n=this.mc;e<n.length;e++)if(n[e].timerId===t)return!0;return!1},mp.prototype.Vc=function(r){var i=this;return this.vc().then(function(){i.mc.sort(function(t,e){return t.targetTimeMs-e.targetTimeMs});for(var t=0,e=i.mc;t<e.length;t++){var n=e[t];if(n.skipDelay(),"all"!==r&&n.timerId===r)break}return i.vc()})},mp.prototype.Sc=function(t){this.Ec.push(t)},mp.prototype.bc=function(t){t=this.mc.indexOf(t);this.mc.splice(t,1)},mp);function mp(){var e=this;this.dc=Promise.resolve(),this.wc=[],this._c=!1,this.mc=[],this.yc=null,this.gc=!1,this.Ec=[],this.Zi=new Sl(this,"async_queue_retry"),this.Tc=function(){var t=wl();t&&qr("AsyncQueue","Visibility state changed to "+t.visibilityState),e.Zi.Gi()};var t=wl();t&&"function"==typeof t.addEventListener&&t.addEventListener("visibilitychange",this.Tc)}function vp(i){return function(){if("object"==typeof i&&null!==i)for(var t=i,e=0,n=["next","error","complete"];e<n.length;e++){var r=n[e];if(r in t&&"function"==typeof t[r])return 1}}()}var wp,bp=(Ip.prototype.onProgress=function(t,e,n){this._progressObserver={next:t,error:e,complete:n}},Ip.prototype.catch=function(t){return this._taskCompletionResolver.promise.catch(t)},Ip.prototype.then=function(t,e){return this._taskCompletionResolver.promise.then(t,e)},Ip.prototype._completeWith=function(t){this._updateProgress(t),this._progressObserver.complete&&this._progressObserver.complete(),this._taskCompletionResolver.resolve(t)},Ip.prototype._failWith=function(t){this._lastProgress.taskState="Error",this._progressObserver.next&&this._progressObserver.next(this._lastProgress),this._progressObserver.error&&this._progressObserver.error(t),this._taskCompletionResolver.reject(t)},Ip.prototype._updateProgress=function(t){this._lastProgress=t,this._progressObserver.next&&this._progressObserver.next(t)},Ip),Ep=(n(Tp,wp=rp),Tp.prototype._terminate=function(){return this._firestoreClient||Sp(this),this._firestoreClient.terminate()},Tp);function Tp(t,e){var n=this;return(n=wp.call(this,t,e)||this).type="firestore",n._queue=new gp,n._persistenceKey="name"in t?t.name:"[DEFAULT]",n}function Ip(){this._progressObserver={},this._taskCompletionResolver=new uu,this._lastProgress={taskState:"Running",totalBytes:0,totalDocuments:0,bytesLoaded:0,documentsLoaded:0}}function _p(t){return t._firestoreClient||Sp(t),t._firestoreClient.verifyNotTerminated(),t._firestoreClient}function Sp(t){var e,n,r,i=t._freezeSettings(),i=(e=t._databaseId,n=(null===(r=t._app)||void 0===r?void 0:r.options.appId)||"",r=t._persistenceKey,new Od(e,n,r,i.host,i.ssl,i.experimentalForceLongPolling,i.experimentalAutoDetectLongPolling,i.useFetchStreams));t._firestoreClient=new yd(t._credentials,t._queue,i)}function Ap(n,r,i){var t=this,o=new uu;return n.asyncQueue.enqueue(function(){return y(t,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return t.trys.push([0,3,,4]),[4,Ed(n,i)];case 1:return t.sent(),[4,Td(n,r)];case 2:return t.sent(),o.resolve(),[3,4];case 3:if(!("FirebaseError"===(e=t.sent()).name?e.code===Pr.FAILED_PRECONDITION||e.code===Pr.UNIMPLEMENTED:!("undefined"!=typeof DOMException&&e instanceof DOMException)||22===e.code||20===e.code||11===e.code))throw e;return console.warn("Error enabling offline persistence. Falling back to persistence disabled: "+e),o.reject(e),[3,4];case 4:return[2]}})})}).then(function(){return o.promise})}function Dp(t){if(t._initialized||t._terminated)throw new Mr(Pr.FAILED_PRECONDITION,"Firestore has already been started and persistence can no longer be enabled. You can only enable persistence before calling any other methods on a Firestore object.")}var Np=(Up.prototype.isEqual=function(t){return this._internalPath.isEqual(t._internalPath)},Up),Cp=(Vp.fromBase64String=function(t){try{return new Vp(hi.fromBase64String(t))}catch(t){throw new Mr(Pr.INVALID_ARGUMENT,"Failed to construct data from Base64 string: "+t)}},Vp.fromUint8Array=function(t){return new Vp(hi.fromUint8Array(t))},Vp.prototype.toBase64=function(){return this._byteString.toBase64()},Vp.prototype.toUint8Array=function(){return this._byteString.toUint8Array()},Vp.prototype.toString=function(){return"Bytes(base64: "+this.toBase64()+")"},Vp.prototype.isEqual=function(t){return this._byteString.isEqual(t._byteString)},Vp),kp=function(t){this._methodName=t},Rp=(Object.defineProperty(Fp.prototype,"latitude",{get:function(){return this._lat},enumerable:!1,configurable:!0}),Object.defineProperty(Fp.prototype,"longitude",{get:function(){return this._long},enumerable:!1,configurable:!0}),Fp.prototype.isEqual=function(t){return this._lat===t._lat&&this._long===t._long},Fp.prototype.toJSON=function(){return{latitude:this._lat,longitude:this._long}},Fp.prototype._compareTo=function(t){return Wr(this._lat,t._lat)||Wr(this._long,t._long)},Fp),xp=/^__.*__$/,Op=(Mp.prototype.toMutation=function(t,e){return null!==this.fieldMask?new ws(t,this.data,this.fieldMask,e,this.fieldTransforms):new vs(t,this.data,e,this.fieldTransforms)},Mp),Lp=(Pp.prototype.toMutation=function(t,e){return new ws(t,this.data,this.fieldMask,e,this.fieldTransforms)},Pp);function Pp(t,e,n){this.data=t,this.fieldMask=e,this.fieldTransforms=n}function Mp(t,e,n){this.data=t,this.fieldMask=e,this.fieldTransforms=n}function Fp(t,e){if(!isFinite(t)||t<-90||90<t)throw new Mr(Pr.INVALID_ARGUMENT,"Latitude must be a number between -90 and 90, but was: "+t);if(!isFinite(e)||e<-180||180<e)throw new Mr(Pr.INVALID_ARGUMENT,"Longitude must be a number between -180 and 180, but was: "+e);this._lat=t,this._long=e}function Vp(t){this._byteString=t}function Up(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0;n<t.length;++n)if(0===t[n].length)throw new Mr(Pr.INVALID_ARGUMENT,"Invalid field name at argument $(i + 1). Field names must not be empty.");this._internalPath=new ui(t)}function qp(t){switch(t){case 0:case 2:case 1:return 1;case 3:case 4:return;default:throw Gr()}}var Bp=(Object.defineProperty(Gp.prototype,"path",{get:function(){return this.settings.path},enumerable:!1,configurable:!0}),Object.defineProperty(Gp.prototype,"Cc",{get:function(){return this.settings.Cc},enumerable:!1,configurable:!0}),Gp.prototype.Nc=function(t){return new Gp(Object.assign(Object.assign({},this.settings),t),this.databaseId,this.R,this.ignoreUndefinedProperties,this.fieldTransforms,this.fieldMask)},Gp.prototype.xc=function(t){var e=null===(e=this.path)||void 0===e?void 0:e.child(t),e=this.Nc({path:e,Fc:!1});return e.kc(t),e},Gp.prototype.$c=function(t){var e,t=null===(e=this.path)||void 0===e?void 0:e.child(t),t=this.Nc({path:t,Fc:!1});return t.Dc(),t},Gp.prototype.Oc=function(t){return this.Nc({path:void 0,Fc:!0})},Gp.prototype.Mc=function(t){return wy(t,this.settings.methodName,this.settings.Lc||!1,this.path,this.settings.Bc)},Gp.prototype.contains=function(e){return void 0!==this.fieldMask.find(function(t){return e.isPrefixOf(t)})||void 0!==this.fieldTransforms.find(function(t){return e.isPrefixOf(t.field)})},Gp.prototype.Dc=function(){if(this.path)for(var t=0;t<this.path.length;t++)this.kc(this.path.get(t))},Gp.prototype.kc=function(t){if(0===t.length)throw this.Mc("Document fields must not be empty");if(qp(this.Cc)&&xp.test(t))throw this.Mc('Document fields cannot begin and end with "__"')},Gp),jp=(Kp.prototype.qc=function(t,e,n,r){return void 0===r&&(r=!1),new Bp({Cc:t,methodName:e,Bc:n,path:ui.emptyPath(),Fc:!1,Lc:r},this.databaseId,this.R,this.ignoreUndefinedProperties)},Kp);function Kp(t,e,n){this.databaseId=t,this.ignoreUndefinedProperties=e,this.R=n||bl(t)}function Gp(t,e,n,r,i,o){this.settings=t,this.databaseId=e,this.R=n,this.ignoreUndefinedProperties=r,void 0===i&&this.Dc(),this.fieldTransforms=i||[],this.fieldMask=o||[]}function Qp(t){var e=t._freezeSettings(),n=bl(t._databaseId);return new jp(t._databaseId,!!e.ignoreUndefinedProperties,n)}function Hp(t,e,n,r,i,o){var s=t.qc((o=void 0===o?{}:o).merge||o.mergeFields?2:0,e,n,i);yy("Data must be an object, but it was:",s,r);var a,u,r=dy(r,s);if(o.merge)a=new ci(s.fieldMask),u=s.fieldTransforms;else if(o.mergeFields){for(var c=[],h=0,l=o.mergeFields;h<l.length;h++){var f=gy(e,l[h],n);if(!s.contains(f))throw new Mr(Pr.INVALID_ARGUMENT,"Field '"+f+"' is specified in your field mask but missing from your input data.");by(c,f)||c.push(f)}a=new ci(c),u=s.fieldTransforms.filter(function(t){return a.covers(t.field)})}else a=null,u=s.fieldTransforms;return new Op(new qi(r),a,u)}var zp,Wp=(n(Yp,zp=kp),Yp.prototype._toFieldTransform=function(t){if(2!==t.Cc)throw 1===t.Cc?t.Mc(this._methodName+"() can only appear at the top level of your update data"):t.Mc(this._methodName+"() cannot be used with set() unless you pass {merge:true}");return t.fieldMask.push(t.path),null},Yp.prototype.isEqual=function(t){return t instanceof Yp},Yp);function Yp(){return null!==zp&&zp.apply(this,arguments)||this}function Xp(t,e,n){return new Bp({Cc:3,Bc:e.settings.Bc,methodName:t._methodName,Fc:n},e.databaseId,e.R,e.ignoreUndefinedProperties)}var $p,Jp,Zp,ty,ey=(n(uy,ty=kp),uy.prototype._toFieldTransform=function(t){return new cs(t.path,new Wo)},uy.prototype.isEqual=function(t){return t instanceof uy},uy),ny=(n(ay,Zp=kp),ay.prototype._toFieldTransform=function(t){var e=Xp(this,t,!0),n=this.Uc.map(function(t){return fy(t,e)}),n=new Yo(n);return new cs(t.path,n)},ay.prototype.isEqual=function(t){return this===t},ay),ry=(n(sy,Jp=kp),sy.prototype._toFieldTransform=function(t){var e=Xp(this,t,!0),n=this.Uc.map(function(t){return fy(t,e)}),n=new ts(n);return new cs(t.path,n)},sy.prototype.isEqual=function(t){return this===t},sy),iy=(n(oy,$p=kp),oy.prototype._toFieldTransform=function(t){var e=new is(t.R,Go(t.R,this.Kc));return new cs(t.path,e)},oy.prototype.isEqual=function(t){return this===t},oy);function oy(t,e){var n=this;return(n=$p.call(this,t)||this).Kc=e,n}function sy(t,e){var n=this;return(n=Jp.call(this,t)||this).Uc=e,n}function ay(t,e){var n=this;return(n=Zp.call(this,t)||this).Uc=e,n}function uy(){return null!==ty&&ty.apply(this,arguments)||this}function cy(t,r,i,e){var o=t.qc(1,r,i);yy("Data must be an object, but it was:",o,e);var s=[],a=qi.empty();ni(e,function(t,e){var n=vy(r,t,i);e=v(e);t=o.$c(n);e instanceof Wp?s.push(n):null!=(t=fy(e,t))&&(s.push(n),a.set(n,t))});e=new ci(s);return new Lp(a,e,o.fieldTransforms)}function hy(t,e,n,r,i,o){var s=t.qc(1,e,n),a=[gy(e,r,n)],u=[i];if(o.length%2!=0)throw new Mr(Pr.INVALID_ARGUMENT,"Function "+e+"() needs to be called with an even number of arguments that alternate between field names and values.");for(var c=0;c<o.length;c+=2)a.push(gy(e,o[c])),u.push(o[c+1]);for(var h,l,f,d=[],p=qi.empty(),y=a.length-1;0<=y;--y)by(d,a[y])||(h=a[y],l=v(l=u[y]),f=s.$c(h),l instanceof Wp?d.push(h):null!=(f=fy(l,f))&&(d.push(h),p.set(h,f)));i=new ci(d);return new Lp(p,i,s.fieldTransforms)}function ly(t,e,n,r){return fy(n,t.qc((r=void 0===r?!1:r)?4:3,e))}function fy(s,t){if(py(s=v(s)))return yy("Unsupported field value:",t,s),dy(s,t);if(s instanceof kp)return function(t,e){if(!qp(e.Cc))throw e.Mc(t._methodName+"() can only be used with update() and set()");if(!e.path)throw e.Mc(t._methodName+"() is not currently supported inside arrays");t=t._toFieldTransform(e);t&&e.fieldTransforms.push(t)}(s,t),null;if(void 0===s&&t.ignoreUndefinedProperties)return null;if(t.path&&t.fieldMask.push(t.path),s instanceof Array){if(t.settings.Fc&&4!==t.Cc)throw t.Mc("Nested arrays are not supported");return function(t){for(var e=[],n=0,r=0,i=s;r<i.length;r++){var o=fy(i[r],t.Oc(n));null==o&&(o={nullValue:"NULL_VALUE"}),e.push(o),n++}return{arrayValue:{values:e}}}(t)}return function(t,e){if(null===(t=v(s)))return{nullValue:"NULL_VALUE"};if("number"==typeof t)return Go(e.R,t);if("boolean"==typeof t)return{booleanValue:t};if("string"==typeof t)return{stringValue:t};if(t instanceof Date){var n=$r.fromDate(t);return{timestampValue:pa(e.R,n)}}if(t instanceof $r)return n=new $r(t.seconds,1e3*Math.floor(t.nanoseconds/1e3)),{timestampValue:pa(e.R,n)};if(t instanceof Rp)return{geoPointValue:{latitude:t.latitude,longitude:t.longitude}};if(t instanceof Cp)return{bytesValue:ya(e.R,t._byteString)};if(t instanceof ip){var n=e.databaseId,r=t.firestore._databaseId;if(!r.isEqual(n))throw e.Mc("Document reference is for database "+r.projectId+"/"+r.database+" but should be for database "+n.projectId+"/"+n.database);return{referenceValue:ma(t.firestore._databaseId||e.databaseId,t._key.path)}}throw e.Mc("Unsupported field value: "+Jd(t))}(0,t)}function dy(t,n){var r={};return ri(t)?n.path&&0<n.path.length&&n.fieldMask.push(n.path):ni(t,function(t,e){e=fy(e,n.xc(t));null!=e&&(r[t]=e)}),{mapValue:{fields:r}}}function py(t){return!("object"!=typeof t||null===t||t instanceof Array||t instanceof Date||t instanceof $r||t instanceof Rp||t instanceof Cp||t instanceof ip||t instanceof kp)}function yy(t,e,n){if(!py(n)||("object"!=typeof(r=n)||null===r||Object.getPrototypeOf(r)!==Object.prototype&&null!==Object.getPrototypeOf(r))){n=Jd(n);throw"an object"===n?e.Mc(t+" a custom object"):e.Mc(t+" "+n)}var r}function gy(t,e,n){if((e=v(e))instanceof Np)return e._internalPath;if("string"==typeof e)return vy(t,e);throw wy("Field path arguments must be of type string or FieldPath.",t,!1,void 0,n)}var my=new RegExp("[~\\*/\\[\\]]");function vy(e,n,r){if(0<=n.search(my))throw wy("Invalid field path ("+n+"). Paths must not contain '~', '*', '/', '[', or ']'",e,!1,void 0,r);try{return(new(Np.bind.apply(Np,s([void 0],n.split(".")))))._internalPath}catch(t){throw wy("Invalid field path ("+n+"). Paths must not be empty, begin with '.', end with '.', or contain '..'",e,!1,void 0,r)}}function wy(t,e,n,r,i){var o=r&&!r.isEmpty(),s=void 0!==i,e="Function "+e+"() called with invalid data";n&&(e+=" (via `toFirestore()`)");n="";return(o||s)&&(n+=" (found",o&&(n+=" in field "+r),s&&(n+=" in document "+i),n+=")"),new Mr(Pr.INVALID_ARGUMENT,(e+=". ")+t+n)}function by(t,e){return t.some(function(t){return t.isEqual(e)})}var Ey,Ty=(Object.defineProperty(Sy.prototype,"id",{get:function(){return this._key.path.lastSegment()},enumerable:!1,configurable:!0}),Object.defineProperty(Sy.prototype,"ref",{get:function(){return new ip(this._firestore,this._converter,this._key)},enumerable:!1,configurable:!0}),Sy.prototype.exists=function(){return null!==this._document},Sy.prototype.data=function(){if(this._document){if(this._converter){var t=new Iy(this._firestore,this._userDataWriter,this._key,this._document,null);return this._converter.fromFirestore(t)}return this._userDataWriter.convertValue(this._document.data.value)}},Sy.prototype.get=function(t){if(this._document){t=this._document.data.field(Ay("DocumentSnapshot.get",t));if(null!==t)return this._userDataWriter.convertValue(t)}},Sy),Iy=(n(_y,Ey=Ty),_y.prototype.data=function(){return Ey.prototype.data.call(this)},_y);function _y(){return null!==Ey&&Ey.apply(this,arguments)||this}function Sy(t,e,n,r,i){this._firestore=t,this._userDataWriter=e,this._key=n,this._document=r,this._converter=i}function Ay(t,e){return"string"==typeof e?vy(t,e):(e instanceof Np?e:e._delegate)._internalPath}var Dy,Ny,Cy=(My.prototype.isEqual=function(t){return this.hasPendingWrites===t.hasPendingWrites&&this.fromCache===t.fromCache},My),ky=(n(Py,Ny=Ty),Py.prototype.exists=function(){return Ny.prototype.exists.call(this)},Py.prototype.data=function(t){if(void 0===t&&(t={}),this._document){if(this._converter){var e=new Ry(this._firestore,this._userDataWriter,this._key,this._document,this.metadata,null);return this._converter.fromFirestore(e,t)}return this._userDataWriter.convertValue(this._document.data.value,t.serverTimestamps)}},Py.prototype.get=function(t,e){if(void 0===e&&(e={}),this._document){t=this._document.data.field(Ay("DocumentSnapshot.get",t));if(null!==t)return this._userDataWriter.convertValue(t,e.serverTimestamps)}},Py),Ry=(n(Ly,Dy=ky),Ly.prototype.data=function(t){return Dy.prototype.data.call(this,t=void 0===t?{}:t)},Ly),xy=(Object.defineProperty(Oy.prototype,"docs",{get:function(){var e=[];return this.forEach(function(t){return e.push(t)}),e},enumerable:!1,configurable:!0}),Object.defineProperty(Oy.prototype,"size",{get:function(){return this._snapshot.docs.size},enumerable:!1,configurable:!0}),Object.defineProperty(Oy.prototype,"empty",{get:function(){return 0===this.size},enumerable:!1,configurable:!0}),Oy.prototype.forEach=function(e,n){var r=this;this._snapshot.docs.forEach(function(t){e.call(n,new Ry(r._firestore,r._userDataWriter,t.key,t,new Cy(r._snapshot.mutatedKeys.has(t.key),r._snapshot.fromCache),r.query.converter))})},Oy.prototype.docChanges=function(t){t=!!(t=void 0===t?{}:t).includeMetadataChanges;if(t&&this._snapshot.excludesMetadataChanges)throw new Mr(Pr.INVALID_ARGUMENT,"To include metadata changes with your document changes, you must also pass { includeMetadataChanges:true } to onSnapshot().");return this._cachedChanges&&this._cachedChangesIncludeMetadataChanges===t||(this._cachedChanges=function(i,e){if(i._snapshot.oldDocs.isEmpty()){var n=0;return i._snapshot.docChanges.map(function(t){return{type:"added",doc:new Ry(i._firestore,i._userDataWriter,t.doc.key,t.doc,new Cy(i._snapshot.mutatedKeys.has(t.doc.key),i._snapshot.fromCache),i.query.converter),oldIndex:-1,newIndex:n++}})}var o=i._snapshot.oldDocs;return i._snapshot.docChanges.filter(function(t){return e||3!==t.type}).map(function(t){var e=new Ry(i._firestore,i._userDataWriter,t.doc.key,t.doc,new Cy(i._snapshot.mutatedKeys.has(t.doc.key),i._snapshot.fromCache),i.query.converter),n=-1,r=-1;return 0!==t.type&&(n=o.indexOf(t.doc.key),o=o.delete(t.doc.key)),1!==t.type&&(r=(o=o.add(t.doc)).indexOf(t.doc.key)),{type:function(t){switch(t){case 0:return"added";case 2:case 3:return"modified";case 1:return"removed";default:return Gr()}}(t.type),doc:e,oldIndex:n,newIndex:r}})}(this,t),this._cachedChangesIncludeMetadataChanges=t),this._cachedChanges},Oy);function Oy(t,e,n,r){this._firestore=t,this._userDataWriter=e,this._snapshot=r,this.metadata=new Cy(r.hasPendingWrites,r.fromCache),this.query=n}function Ly(){return null!==Dy&&Dy.apply(this,arguments)||this}function Py(t,e,n,r,i,o){var s=this;return(s=Ny.call(this,t,e,n,r,o)||this)._firestore=t,s._firestoreImpl=t,s.metadata=i,s}function My(t,e){this.hasPendingWrites=t,this.fromCache=e}function Fy(t,e){return t instanceof ky&&e instanceof ky?t._firestore===e._firestore&&t._key.isEqual(e._key)&&(null===t._document?null===e._document:t._document.isEqual(e._document))&&t._converter===e._converter:t instanceof xy&&e instanceof xy&&t._firestore===e._firestore&&yp(t.query,e.query)&&t.metadata.isEqual(e.metadata)&&t._snapshot.isEqual(e._snapshot)}function Vy(t){if(ko(t)&&0===t.explicitOrderBy.length)throw new Mr(Pr.UNIMPLEMENTED,"limitToLast() queries require specifying at least one orderBy() clause")}N=function(){};function Uy(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];for(var r=0,i=e;r<i.length;r++)t=i[r]._apply(t);return t}var qy,By,jy,Ky,Gy,Qy=(n(tg,Gy=N),tg.prototype._apply=function(t){var e=Qp(t.firestore),e=function(t,e,n,r,i,o){if(r.isKeyField()){if("array-contains"===i||"array-contains-any"===i)throw new Mr(Pr.INVALID_ARGUMENT,"Invalid Query. You can't perform '"+i+"' queries on FieldPath.documentId().");if("in"===i||"not-in"===i){rg(o,i);for(var s=[],a=0,u=o;a<u.length;a++){var c=u[a];s.push(ng(n,t,c))}h={arrayValue:{values:s}}}else h=ng(n,t,o)}else"in"!==i&&"not-in"!==i&&"array-contains-any"!==i||rg(o,i),h=ly(e,"where",o,"in"===i||"not-in"===i);var h=Xi.create(r,i,h);return function(t,e){if(e.g()){var n=xo(t);if(null!==n&&!n.isEqual(e.field))throw new Mr(Pr.INVALID_ARGUMENT,"Invalid query. All where filters with an inequality (<, <=, !=, not-in, >, or >=) must be on the same field. But you have inequality filters on '"+n.toString()+"' and '"+e.field.toString()+"'");n=Ro(t);null!==n&&ig(0,e.field,n)}t=function(t,e){for(var n=0,r=t.filters;n<r.length;n++){var i=r[n];if(0<=e.indexOf(i.op))return i.op}return null}(t,function(){switch(e.op){case"!=":return["!=","not-in"];case"array-contains":return["array-contains","array-contains-any","not-in"];case"in":return["array-contains-any","in","not-in"];case"array-contains-any":return["array-contains","array-contains-any","in","not-in"];case"not-in":return["array-contains","array-contains-any","in","not-in","!="];default:return[]}}());if(null!==t)throw t===e.op?new Mr(Pr.INVALID_ARGUMENT,"Invalid query. You cannot use more than one '"+e.op.toString()+"' filter."):new Mr(Pr.INVALID_ARGUMENT,"Invalid query. You cannot use '"+e.op.toString()+"' filters with '"+t.toString()+"' filters.")}(t,h),h}(t._query,e,t.firestore._databaseId,this.Qc,this.jc,this.Wc);return new op(t.firestore,t.converter,(t=t._query,e=t.filters.concat([e]),new Ao(t.path,t.collectionGroup,t.explicitOrderBy.slice(),e,t.limit,t.limitType,t.startAt,t.endAt)))},tg),Hy=(n(Zy,Ky=N),Zy.prototype._apply=function(t){var e=function(t,e,n){if(null!==t.startAt)throw new Mr(Pr.INVALID_ARGUMENT,"Invalid query. You must not call startAt() or startAfter() before calling orderBy().");if(null!==t.endAt)throw new Mr(Pr.INVALID_ARGUMENT,"Invalid query. You must not call endAt() or endBefore() before calling orderBy().");var r=new Io(e,n);return e=r,null!==Ro(n=t)||null!==(t=xo(n))&&ig(0,t,e.field),r}(t._query,this.Qc,this.Gc);return new op(t.firestore,t.converter,(t=t._query,e=t.explicitOrderBy.concat([e]),new Ao(t.path,t.collectionGroup,e,t.filters.slice(),t.limit,t.limitType,t.startAt,t.endAt)))},Zy),zy=(n(Jy,jy=N),Jy.prototype._apply=function(t){return new op(t.firestore,t.converter,Mo(t._query,this.zc,this.Hc))},Jy),Wy=(n($y,By=N),$y.prototype._apply=function(t){var e=eg(t,this.type,this.Jc,this.Yc);return new op(t.firestore,t.converter,(t=t._query,e=e,new Ao(t.path,t.collectionGroup,t.explicitOrderBy.slice(),t.filters.slice(),t.limit,t.limitType,e,t.endAt)))},$y),Yy=(n(Xy,qy=N),Xy.prototype._apply=function(t){var e=eg(t,this.type,this.Jc,this.Yc);return new op(t.firestore,t.converter,(t=t._query,e=e,new Ao(t.path,t.collectionGroup,t.explicitOrderBy.slice(),t.filters.slice(),t.limit,t.limitType,t.startAt,e)))},Xy);function Xy(t,e,n){var r=this;return(r=qy.call(this)||this).type=t,r.Jc=e,r.Yc=n,r}function $y(t,e,n){var r=this;return(r=By.call(this)||this).type=t,r.Jc=e,r.Yc=n,r}function Jy(t,e,n){var r=this;return(r=jy.call(this)||this).type=t,r.zc=e,r.Hc=n,r}function Zy(t,e){var n=this;return(n=Ky.call(this)||this).Qc=t,n.Gc=e,n.type="orderBy",n}function tg(t,e,n){var r=this;return(r=Gy.call(this)||this).Qc=t,r.jc=e,r.Wc=n,r.type="where",r}function eg(t,c,e,n){if(e[0]=v(e[0]),e[0]instanceof Ty)return function(t,e,n,r){if(!n)throw new Mr(Pr.NOT_FOUND,"Can't use a DocumentSnapshot that doesn't exist for "+c+"().");for(var i=[],o=0,s=Lo(t);o<s.length;o++){var a=s[o];if(a.field.isKeyField())i.push(Oi(e,n.key));else{var u=n.data.field(a.field);if(bi(u))throw new Mr(Pr.INVALID_ARGUMENT,'Invalid query. You are trying to start or end a query using a document for which the field "'+a.field+'" is an uncommitted server timestamp. (Since the value of this field is unknown, you cannot start/end a query with it.)');if(null===u){a=a.field.canonicalString();throw new Mr(Pr.INVALID_ARGUMENT,"Invalid query. You are trying to start or end a query using a document for which the field '"+a+"' (used as the orderBy) does not exist.")}i.push(u)}}return new mo(i,r)}(t._query,t.firestore._databaseId,e[0]._document,n);var r=Qp(t.firestore);return function(t,e,n,r,i,o){var s=t.explicitOrderBy;if(i.length>s.length)throw new Mr(Pr.INVALID_ARGUMENT,"Too many arguments provided to "+r+"(). The number of arguments must be less than or equal to the number of orderBy() clauses");for(var a=[],u=0;u<i.length;u++){var c=i[u];if(s[u].field.isKeyField()){if("string"!=typeof c)throw new Mr(Pr.INVALID_ARGUMENT,"Invalid query. Expected a string for document ID in "+r+"(), but got a "+typeof c);if(!Oo(t)&&-1!==c.indexOf("/"))throw new Mr(Pr.INVALID_ARGUMENT,"Invalid query. When querying a collection and ordering by FieldPath.documentId(), the value passed to "+r+"() must be a plain document ID, but '"+c+"' contains a slash.");var h=t.path.child(si.fromString(c));if(!Si.isDocumentKey(h))throw new Mr(Pr.INVALID_ARGUMENT,"Invalid query. When querying a collection group and ordering by FieldPath.documentId(), the value passed to "+r+"() must result in a valid document path, but '"+h+"' is not because it contains an odd number of segments.");h=new Si(h);a.push(Oi(e,h))}else{c=ly(n,r,c);a.push(c)}}return new mo(a,o)}(t._query,t.firestore._databaseId,r,c,e,n)}function ng(t,e,n){if("string"==typeof(n=v(n))){if(""===n)throw new Mr(Pr.INVALID_ARGUMENT,"Invalid query. When querying with FieldPath.documentId(), you must provide a valid document ID, but it was an empty string.");if(!Oo(e)&&-1!==n.indexOf("/"))throw new Mr(Pr.INVALID_ARGUMENT,"Invalid query. When querying a collection by FieldPath.documentId(), you must provide a plain document ID, but '"+n+"' contains a '/' character.");e=e.path.child(si.fromString(n));if(!Si.isDocumentKey(e))throw new Mr(Pr.INVALID_ARGUMENT,"Invalid query. When querying a collection group by FieldPath.documentId(), the value provided must result in a valid document path, but '"+e+"' is not because it has an odd number of segments ("+e.length+").");return Oi(t,new Si(e))}if(n instanceof ip)return Oi(t,n._key);throw new Mr(Pr.INVALID_ARGUMENT,"Invalid query. When querying with FieldPath.documentId(), you must provide a valid string or a DocumentReference, but it was: "+Jd(n)+".")}function rg(t,e){if(!Array.isArray(t)||0===t.length)throw new Mr(Pr.INVALID_ARGUMENT,"Invalid Query. A non-empty array is required for '"+e.toString()+"' filters.");if(10<t.length)throw new Mr(Pr.INVALID_ARGUMENT,"Invalid Query. '"+e.toString()+"' filters support a maximum of 10 elements in the value array.")}function ig(t,e,n){if(!n.isEqual(e))throw new Mr(Pr.INVALID_ARGUMENT,"Invalid query. You have a where filter with an inequality (<, <=, !=, not-in, >, or >=) on field '"+e.toString()+"' and so you must also use '"+e.toString()+"' as your first argument to orderBy(), but your first orderBy() is on field '"+n.toString()+"' instead.")}og.prototype.convertValue=function(t,e){switch(void 0===e&&(e="none"),Di(t)){case 0:return null;case 1:return t.booleanValue;case 2:return vi(t.integerValue||t.doubleValue);case 3:return this.convertTimestamp(t.timestampValue);case 4:return this.convertServerTimestamp(t,e);case 5:return t.stringValue;case 6:return this.convertBytes(wi(t.bytesValue));case 7:return this.convertReference(t.referenceValue);case 8:return this.convertGeoPoint(t.geoPointValue);case 9:return this.convertArray(t.arrayValue,e);case 10:return this.convertObject(t.mapValue,e);default:throw Gr()}},og.prototype.convertObject=function(t,n){var r=this,i={};return ni(t.fields,function(t,e){i[t]=r.convertValue(e,n)}),i},og.prototype.convertGeoPoint=function(t){return new Rp(vi(t.latitude),vi(t.longitude))},og.prototype.convertArray=function(t,e){var n=this;return(t.values||[]).map(function(t){return n.convertValue(t,e)})},og.prototype.convertServerTimestamp=function(t,e){switch(e){case"previous":var n=function t(e){e=e.mapValue.fields.__previous_value__;return bi(e)?t(e):e}(t);return null==n?null:this.convertValue(n,e);case"estimate":return this.convertTimestamp(Ei(t));default:return null}},og.prototype.convertTimestamp=function(t){t=mi(t);return new $r(t.seconds,t.nanos)},og.prototype.convertDocumentKey=function(t,e){var n=si.fromString(t);Qr(Va(n));t=new Ld(n.get(1),n.get(3)),n=new Si(n.popFirst(5));return t.isEqual(e)||Br("Document "+n+" contains a document reference within a different database ("+t.projectId+"/"+t.database+") which is not supported. It will be treated as a reference in the current database ("+e.projectId+"/"+e.database+") instead."),n},I=og;function og(){}function sg(t,e,n){return t?n&&(n.merge||n.mergeFields)?t.toFirestore(e,n):t.toFirestore(e):e}var ag,ug=(n(lg,ag=I),lg.prototype.convertBytes=function(t){return new Cp(t)},lg.prototype.convertReference=function(t){t=this.convertDocumentKey(t,this.firestore._databaseId);return new ip(this.firestore,null,t)},lg),cg=(hg.prototype.set=function(t,e,n){this._verifyNotCommitted();t=fg(t,this._firestore),e=sg(t.converter,e,n),n=Hp(this._dataReader,"WriteBatch.set",t._key,e,null!==t.converter,n);return this._mutations.push(n.toMutation(t._key,hs.none())),this},hg.prototype.update=function(t,e,n){for(var r=[],i=3;i<arguments.length;i++)r[i-3]=arguments[i];this._verifyNotCommitted();t=fg(t,this._firestore),e="string"==typeof(e=v(e))||e instanceof Np?hy(this._dataReader,"WriteBatch.update",t._key,e,n,r):cy(this._dataReader,"WriteBatch.update",t._key,e);return this._mutations.push(e.toMutation(t._key,hs.exists(!0))),this},hg.prototype.delete=function(t){this._verifyNotCommitted();t=fg(t,this._firestore);return this._mutations=this._mutations.concat(new Cs(t._key,hs.none())),this},hg.prototype.commit=function(){return this._verifyNotCommitted(),this._committed=!0,0<this._mutations.length?this._commitHandler(this._mutations):Promise.resolve()},hg.prototype._verifyNotCommitted=function(){if(this._committed)throw new Mr(Pr.FAILED_PRECONDITION,"A write batch can no longer be used after commit() has been called.")},hg);function hg(t,e){this._firestore=t,this._commitHandler=e,this._mutations=[],this._committed=!1,this._dataReader=Qp(t)}function lg(t){var e=this;return(e=ag.call(this)||this).firestore=t,e}function fg(t,e){if((t=v(t)).firestore!==e)throw new Mr(Pr.INVALID_ARGUMENT,"Provided document reference is from a different Firestore instance.");return t}var dg,pg=(n(yg,dg=I),yg.prototype.convertBytes=function(t){return new Cp(t)},yg.prototype.convertReference=function(t){t=this.convertDocumentKey(t,this.firestore._databaseId);return new ip(this.firestore,null,t)},yg);function yg(t){var e=this;return(e=dg.call(this)||this).firestore=t,e}function gg(t,e,n){for(var r=[],i=3;i<arguments.length;i++)r[i-3]=arguments[i];t=Zd(t,ip);var o=Zd(t.firestore,Ep),s=Qp(o);return vg(o,[("string"==typeof(e=v(e))||e instanceof Np?hy(s,"updateDoc",t._key,e,n,r):cy(s,"updateDoc",t._key,e)).toMutation(t._key,hs.exists(!0))])}function mg(e){for(var n=[],t=1;t<arguments.length;t++)n[t-1]=arguments[t];e=v(e);var r={includeMetadataChanges:!1},i=0;"object"!=typeof n[i]||vp(n[i])||(r=n[i],i++);var o,s,a,u,c,h,l={includeMetadataChanges:r.includeMetadataChanges};return vp(n[i])&&(o=n[i],n[i]=null===(r=o.next)||void 0===r?void 0:r.bind(o),n[i+1]=null===(r=o.error)||void 0===r?void 0:r.bind(o),n[i+2]=null===(r=o.complete)||void 0===r?void 0:r.bind(o)),e instanceof ip?(a=Zd(e.firestore,Ep),u=No(e._key.path),h={next:function(t){n[i]&&n[i](wg(a,e,t))},error:n[i+1],complete:n[i+2]}):(s=Zd(e,op),a=Zd(s.firestore,Ep),u=s._query,c=new pg(a),h={next:function(t){n[i]&&n[i](new xy(a,c,s,t))},error:n[i+1],complete:n[i+2]},Vy(e._query)),function(n,t,e){var r=this,i=new ld(h),o=new gf(t,i,e);return n.asyncQueue.enqueueAndForget(function(){return y(r,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return e=df,[4,Cd(n)];case 1:return[2,e.apply(void 0,[t.sent(),o])]}})})}),function(){i.Wo(),n.asyncQueue.enqueueAndForget(function(){return y(r,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return e=pf,[4,Cd(n)];case 1:return[2,e.apply(void 0,[t.sent(),o])]}})})})}}(_p(a),u,l)}function vg(t,e){return function(n,r){var t=this,i=new uu;return n.asyncQueue.enqueueAndForget(function(){return y(t,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return e=Lf,[4,Nd(n)];case 1:return[2,e.apply(void 0,[t.sent(),r,i])]}})})}),i.promise}(_p(t),e)}function wg(t,e,n){var r=n.docs.get(e._key),i=new pg(t);return new ky(t,i,e._key,r,new Cy(n.hasPendingWrites,n.fromCache),e.converter)}var bg,Eg=(Ig.prototype.get=function(t){var e=this,n=fg(t,this._firestore),r=new ug(this._firestore);return this._transaction.lookup([n._key]).then(function(t){if(!t||1!==t.length)return Gr();t=t[0];if(t.isFoundDocument())return new Ty(e._firestore,r,t.key,t,n.converter);if(t.isNoDocument())return new Ty(e._firestore,r,n._key,null,n.converter);throw Gr()})},Ig.prototype.set=function(t,e,n){t=fg(t,this._firestore),e=sg(t.converter,e,n),n=Hp(this._dataReader,"Transaction.set",t._key,e,null!==t.converter,n);return this._transaction.set(t._key,n),this},Ig.prototype.update=function(t,e,n){for(var r=[],i=3;i<arguments.length;i++)r[i-3]=arguments[i];t=fg(t,this._firestore),e="string"==typeof(e=v(e))||e instanceof Np?hy(this._dataReader,"Transaction.update",t._key,e,n,r):cy(this._dataReader,"Transaction.update",t._key,e);return this._transaction.update(t._key,e),this},Ig.prototype.delete=function(t){t=fg(t,this._firestore);return this._transaction.delete(t._key),this},n(Tg,bg=Ig),Tg.prototype.get=function(t){var e=this,n=fg(t,this._firestore),r=new pg(this._firestore);return bg.prototype.get.call(this,t).then(function(t){return new ky(e._firestore,r,n._key,t._document,new Cy(!1,!1),n.converter)})},Tg);function Tg(t,e){var n=this;return(n=bg.call(this,t,e)||this)._firestore=t,n}function Ig(t,e){this._firestore=t,this._transaction=e,this._dataReader=Qp(t)}function _g(){if("undefined"==typeof Uint8Array)throw new Mr(Pr.UNIMPLEMENTED,"Uint8Arrays are not available in this environment.")}function Sg(){if("undefined"==typeof atob)throw new Mr(Pr.UNIMPLEMENTED,"Blobs are unavailable in Firestore in this environment.")}var Ag,Dg=(Lg.fromBase64String=function(t){return Sg(),new Lg(Cp.fromBase64String(t))},Lg.fromUint8Array=function(t){return _g(),new Lg(Cp.fromUint8Array(t))},Lg.prototype.toBase64=function(){return Sg(),this._delegate.toBase64()},Lg.prototype.toUint8Array=function(){return _g(),this._delegate.toUint8Array()},Lg.prototype.isEqual=function(t){return this._delegate.isEqual(t._delegate)},Lg.prototype.toString=function(){return"Blob(base64: "+this.toBase64()+")"},Lg),Ng=(Og.prototype.enableIndexedDbPersistence=function(t,e){return function(t,e){Dp(t=Zd(t,Ep));var n=_p(t),r=t._freezeSettings(),t=new od;return Ap(n,t,new rd(t,r.cacheSizeBytes,null==e?void 0:e.forceOwnership))}(t._delegate,{forceOwnership:e})},Og.prototype.enableMultiTabIndexedDbPersistence=function(t){return function(t){Dp(t=Zd(t,Ep));var e=_p(t),n=t._freezeSettings(),t=new od;return Ap(e,t,new id(t,n.cacheSizeBytes))}(t._delegate)},Og.prototype.clearIndexedDbPersistence=function(t){return function(n){var t=this;if(n._initialized&&!n._terminated)throw new Mr(Pr.FAILED_PRECONDITION,"Persistence can only be cleared before a Firestore instance is initialized or after it is terminated.");var r=new uu;return n._queue.enqueueAndForgetEvenWhileRestricted(function(){return y(t,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,function(n){return y(this,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return lu.yt()?(e=n+"main",[4,lu.delete(e)]):[2,Promise.resolve()];case 1:return t.sent(),[2]}})})}(Jc(n._databaseId,n._persistenceKey))];case 1:return t.sent(),r.resolve(),[3,3];case 2:return e=t.sent(),r.reject(e),[3,3];case 3:return[2]}})})}),r.promise}(t._delegate)},Og),Cg=(Object.defineProperty(xg.prototype,"_databaseId",{get:function(){return this._delegate._databaseId},enumerable:!1,configurable:!0}),xg.prototype.settings=function(t){var e=this._delegate._getSettings();t.merge||e.host===t.host||jr("You are overriding the original host. If you did not intend to override your settings, use {merge: true}."),t.merge&&delete(t=Object.assign(Object.assign({},e),t)).merge,this._delegate._setSettings(t)},xg.prototype.useEmulator=function(t,e,n){(function(t,e,n,r){void 0===r&&(r={});var i=(t=Zd(t,rp))._getSettings();if("firestore.googleapis.com"!==i.host&&i.host!==e&&jr("Host has been set in both settings() and useEmulator(), emulator host will be used"),t._setSettings(Object.assign(Object.assign({},i),{host:e+":"+n,ssl:!1})),r.mockUserToken){n=function(t,e){if(t.uid)throw new Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');var n=e||"demo-project",r=t.iat||0;if(!(e=t.sub||t.user_id))throw new Error("mockUserToken must contain 'sub' or 'user_id' field!");return t=o({iss:"https://securetoken.google.com/"+n,aud:n,iat:r,exp:r+3600,auth_time:r,sub:e,user_id:e,firebase:{sign_in_provider:"custom",identities:{}}},t),[a.encodeString(JSON.stringify({alg:"none",type:"JWT"}),!1),a.encodeString(JSON.stringify(t),!1),""].join(".")}(r.mockUserToken),r=r.mockUserToken.sub||r.mockUserToken.user_id;if(!r)throw new Mr(Pr.INVALID_ARGUMENT,"mockUserToken must contain 'sub' or 'user_id' field!");t._credentials=new Fd(new xd(n,new xh(r)))}})(this._delegate,t,e,n=void 0===n?{}:n)},xg.prototype.enableNetwork=function(){return function(i){var t=this;return i.asyncQueue.enqueue(function(){return y(t,void 0,void 0,function(){var n,r;return g(this,function(t){switch(t.label){case 0:return[4,Sd(i)];case 1:return n=t.sent(),[4,Dd(i)];case 2:return r=t.sent(),[2,(n.setNetworkEnabled(!0),(e=r).Or.delete(0),Ml(e))]}var e})})})}(_p(Zd(this._delegate,Ep)))},xg.prototype.disableNetwork=function(){return function(r){var t=this;return r.asyncQueue.enqueue(function(){return y(t,void 0,void 0,function(){var e,n;return g(this,function(t){switch(t.label){case 0:return[4,Sd(r)];case 1:return e=t.sent(),[4,Dd(r)];case 2:return n=t.sent(),[2,(e.setNetworkEnabled(!1),function(n){return y(this,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return(e=n).Or.add(0),[4,Fl(e)];case 1:return t.sent(),e.Br.set("Offline"),[2]}})})}(n))]}})})})}(_p(Zd(this._delegate,Ep)))},xg.prototype.enablePersistence=function(t){var e=!1,n=!1;return t&&Yd("synchronizeTabs",e=!!t.synchronizeTabs,"experimentalForceOwningTab",n=!!t.experimentalForceOwningTab),e?this.Xc.enableMultiTabIndexedDbPersistence(this):this.Xc.enableIndexedDbPersistence(this,n)},xg.prototype.clearPersistence=function(){return this.Xc.clearIndexedDbPersistence(this)},xg.prototype.terminate=function(){return this.Zc&&(this.Zc._removeServiceInstance("firestore"),this.Zc._removeServiceInstance("firestore-exp")),this._delegate._delete()},xg.prototype.waitForPendingWrites=function(){return function(n){var t=this,r=new uu;return n.asyncQueue.enqueueAndForget(function(){return y(t,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return e=Vf,[4,Nd(n)];case 1:return[2,e.apply(void 0,[t.sent(),r])]}})})}),r.promise}(_p(Zd(this._delegate,Ep)))},xg.prototype.onSnapshotsInSync=function(t){return e=this._delegate,t=t,function(n,t){var e=this,r=new ld(t);return n.asyncQueue.enqueueAndForget(function(){return y(e,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return e=function(t,e){t.Gr.add(e),e.next()},[4,Cd(n)];case 1:return[2,e.apply(void 0,[t.sent(),r])]}})})}),function(){r.Wo(),n.asyncQueue.enqueueAndForget(function(){return y(e,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return e=function(t,e){t.Gr.delete(e)},[4,Cd(n)];case 1:return[2,e.apply(void 0,[t.sent(),r])]}})})})}}(_p(Zd(e,Ep)),vp(t)?t:{next:t});var e},Object.defineProperty(xg.prototype,"app",{get:function(){if(!this.Zc)throw new Mr(Pr.FAILED_PRECONDITION,"Firestore was not initialized using the Firebase SDK. 'app' is not available");return this.Zc},enumerable:!1,configurable:!0}),xg.prototype.collection=function(t){try{return new Zg(this,fp(this._delegate,t))}catch(t){throw Kg(t,"collection()","Firestore.collection()")}},xg.prototype.doc=function(t){try{return new Bg(this,dp(this._delegate,t))}catch(t){throw Kg(t,"doc()","Firestore.doc()")}},xg.prototype.collectionGroup=function(t){try{return new Xg(this,function(t,e){if(t=Zd(t,rp),zd("collectionGroup","collection id",e),0<=e.indexOf("/"))throw new Mr(Pr.INVALID_ARGUMENT,"Invalid collection ID '"+e+"' passed to function collectionGroup(). Collection IDs must not contain '/'.");return new op(t,null,(e=e,new Ao(si.emptyPath(),e)))}(this._delegate,t))}catch(t){throw Kg(t,"collectionGroup()","Firestore.collectionGroup()")}},xg.prototype.runTransaction=function(e){var n,r=this;return function(n,r){var t=this,i=new uu;return n.asyncQueue.enqueueAndForget(function(){return y(t,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return[4,_d(n).then(function(t){return t.datastore})];case 1:return e=t.sent(),new pd(n.asyncQueue,e,r,i).run(),[2]}})})}),i.promise}(_p(n=this._delegate),function(t){return t=new Eg(n,t),e(new Pg(r,t))})},xg.prototype.batch=function(){var e=this;return _p(this._delegate),new Mg(new cg(this._delegate,function(t){return vg(e._delegate,t)}))},xg.prototype.loadBundle=function(t){throw new Mr(Pr.FAILED_PRECONDITION,'"loadBundle()" does not exist, have you imported "firebase/firestore/bundle"?')},xg.prototype.namedQuery=function(t){throw new Mr(Pr.FAILED_PRECONDITION,'"namedQuery()" does not exist, have you imported "firebase/firestore/bundle"?')},xg),kg=(n(Rg,Ag=I),Rg.prototype.convertBytes=function(t){return new Dg(new Cp(t))},Rg.prototype.convertReference=function(t){t=this.convertDocumentKey(t,this.firestore._databaseId);return Bg.tu(t,this.firestore,null)},Rg);function Rg(t){var e=this;return(e=Ag.call(this)||this).firestore=t,e}function xg(t,e,n){var r=this;this._delegate=e,this.Xc=n,this.INTERNAL={delete:function(){return r.terminate()}},t instanceof Ld||(this.Zc=t)}function Og(){}function Lg(t){this._delegate=t}var Pg=(qg.prototype.get=function(t){var e=this,n=sm(t);return this._delegate.get(n).then(function(t){return new Wg(e._firestore,new ky(e._firestore._delegate,e._userDataWriter,t._key,t._document,t.metadata,n.converter))})},qg.prototype.set=function(t,e,n){t=sm(t);return n?(Wd("Transaction.set",n),this._delegate.set(t,e,n)):this._delegate.set(t,e),this},qg.prototype.update=function(t,e,n){for(var r=[],i=3;i<arguments.length;i++)r[i-3]=arguments[i];var o=sm(t);return 2===arguments.length?this._delegate.update(o,e):(t=this._delegate).update.apply(t,s([o,e,n],r)),this},qg.prototype.delete=function(t){t=sm(t);return this._delegate.delete(t),this},qg),Mg=(Ug.prototype.set=function(t,e,n){t=sm(t);return n?(Wd("WriteBatch.set",n),this._delegate.set(t,e,n)):this._delegate.set(t,e),this},Ug.prototype.update=function(t,e,n){for(var r=[],i=3;i<arguments.length;i++)r[i-3]=arguments[i];var o=sm(t);return 2===arguments.length?this._delegate.update(o,e):(t=this._delegate).update.apply(t,s([o,e,n],r)),this},Ug.prototype.delete=function(t){t=sm(t);return this._delegate.delete(t),this},Ug.prototype.commit=function(){return this._delegate.commit()},Ug),Fg=(Vg.prototype.fromFirestore=function(t,e){t=new Ry(this._firestore._delegate,this._userDataWriter,t._key,t._document,t.metadata,null);return this._delegate.fromFirestore(new Yg(this._firestore,t),null!=e?e:{})},Vg.prototype.toFirestore=function(t,e){return e?this._delegate.toFirestore(t,e):this._delegate.toFirestore(t)},Vg.eu=function(t,e){var n=Vg.nu,r=n.get(t);r||(r=new WeakMap,n.set(t,r));n=r.get(e);return n||(n=new Vg(t,new kg(t),e),r.set(e,n)),n},Vg);function Vg(t,e,n){this._firestore=t,this._userDataWriter=e,this._delegate=n}function Ug(t){this._delegate=t}function qg(t,e){this._firestore=t,this._delegate=e,this._userDataWriter=new kg(t)}Fg.nu=new WeakMap;var Bg=(jg.su=function(t,e,n){if(t.length%2!=0)throw new Mr(Pr.INVALID_ARGUMENT,"Invalid document reference. Document references must have an even number of segments, but "+t.canonicalString()+" has "+t.length);return new jg(e,new ip(e._delegate,n,new Si(t)))},jg.tu=function(t,e,n){return new jg(e,new ip(e._delegate,n,t))},Object.defineProperty(jg.prototype,"id",{get:function(){return this._delegate.id},enumerable:!1,configurable:!0}),Object.defineProperty(jg.prototype,"parent",{get:function(){return new Zg(this.firestore,this._delegate.parent)},enumerable:!1,configurable:!0}),Object.defineProperty(jg.prototype,"path",{get:function(){return this._delegate.path},enumerable:!1,configurable:!0}),jg.prototype.collection=function(t){try{return new Zg(this.firestore,fp(this._delegate,t))}catch(t){throw Kg(t,"collection()","DocumentReference.collection()")}},jg.prototype.isEqual=function(t){return(t=v(t))instanceof ip&&pp(this._delegate,t)},jg.prototype.set=function(t,e){e=Wd("DocumentReference.set",e);try{return function(t,e,n){t=Zd(t,ip);var r=Zd(t.firestore,Ep),e=sg(t.converter,e,n);return vg(r,[Hp(Qp(r),"setDoc",t._key,e,null!==t.converter,n).toMutation(t._key,hs.none())])}(this._delegate,t,e)}catch(t){throw Kg(t,"setDoc()","DocumentReference.set()")}},jg.prototype.update=function(t,e){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];try{return 1===arguments.length?gg(this._delegate,t):gg.apply(void 0,s([this._delegate,t,e],n))}catch(t){throw Kg(t,"updateDoc()","DocumentReference.update()")}},jg.prototype.delete=function(){return vg(Zd((t=this._delegate).firestore,Ep),[new Cs(t._key,hs.none())]);var t},jg.prototype.onSnapshot=function(){for(var e=this,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=Gg(t),i=Qg(t,function(t){return new Wg(e.firestore,new ky(e.firestore._delegate,e._userDataWriter,t._key,t._document,t.metadata,e._delegate.converter))});return mg(this._delegate,r,i)},jg.prototype.get=function(t){var e=this;return("cache"===(null==t?void 0:t.source)?function(e){e=Zd(e,ip);var n=Zd(e.firestore,Ep),t=_p(n),r=new pg(n);return function(n,r){var t=this,i=new uu;return n.asyncQueue.enqueueAndForget(function(){return y(t,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return e=function(o,s,a){return y(this,void 0,void 0,function(){var r,i;return g(this,function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,(e=s,(n=o).persistence.runTransaction("read document","readonly",function(t){return n.Mn.mn(t,e)}))];case 1:return(i=t.sent()).isFoundDocument()?a.resolve(i):i.isNoDocument()?a.resolve(null):a.reject(new Mr(Pr.UNAVAILABLE,"Failed to get document from cache. (However, this document may exist on the server. Run again without setting 'source' in the GetOptions to attempt to retrieve the document from the server.)")),[3,3];case 2:return r=t.sent(),i=rf(r,"Failed to get document '"+s+" from cache"),a.reject(i),[3,3];case 3:return[2]}var e,n})})},[4,Ad(n)];case 1:return[2,e.apply(void 0,[t.sent(),r,i])]}})})}),i.promise}(t,e._key).then(function(t){return new ky(n,r,e._key,t,new Cy(null!==t&&t.hasLocalMutations,!0),e.converter)})}:"server"===(null==t?void 0:t.source)?function(e){e=Zd(e,ip);var n=Zd(e.firestore,Ep);return kd(_p(n),e._key,{source:"server"}).then(function(t){return wg(n,e,t)})}:function(e){e=Zd(e,ip);var n=Zd(e.firestore,Ep);return kd(_p(n),e._key).then(function(t){return wg(n,e,t)})})(this._delegate).then(function(t){return new Wg(e.firestore,new ky(e.firestore._delegate,e._userDataWriter,t._key,t._document,t.metadata,e._delegate.converter))})},jg.prototype.withConverter=function(t){return new jg(this.firestore,t?this._delegate.withConverter(Fg.eu(this.firestore,t)):this._delegate.withConverter(null))},jg);function jg(t,e){this.firestore=t,this._delegate=e,this._userDataWriter=new kg(t)}function Kg(t,e,n){return t.message=t.message.replace(e,n),t}function Gg(t){for(var e=0,n=t;e<n.length;e++){var r=n[e];if("object"==typeof r&&!vp(r))return r}return{}}function Qg(t,e){var n;return{next:function(t){n.next&&n.next(e(t))},error:null===(t=(n=vp(t[0])?t[0]:vp(t[1])?t[1]:"function"==typeof t[0]?{next:t[0],error:t[1],complete:t[2]}:{next:t[1],error:t[2],complete:t[3]}).error)||void 0===t?void 0:t.bind(n),complete:null===(t=n.complete)||void 0===t?void 0:t.bind(n)}}var Hg,zg,Wg=(Object.defineProperty(om.prototype,"ref",{get:function(){return new Bg(this._firestore,this._delegate.ref)},enumerable:!1,configurable:!0}),Object.defineProperty(om.prototype,"id",{get:function(){return this._delegate.id},enumerable:!1,configurable:!0}),Object.defineProperty(om.prototype,"metadata",{get:function(){return this._delegate.metadata},enumerable:!1,configurable:!0}),Object.defineProperty(om.prototype,"exists",{get:function(){return this._delegate.exists()},enumerable:!1,configurable:!0}),om.prototype.data=function(t){return this._delegate.data(t)},om.prototype.get=function(t,e){return this._delegate.get(t,e)},om.prototype.isEqual=function(t){return Fy(this._delegate,t._delegate)},om),Yg=(n(im,zg=Wg),im.prototype.data=function(t){return this._delegate.data(t)},im),Xg=(rm.prototype.where=function(t,e,n){try{return new rm(this.firestore,Uy(this._delegate,(r=n,i=e,o=Ay("where",t),new Qy(o,i,r))))}catch(t){throw Kg(t,/(orderBy|where)\(\)/,"Query.$1()")}var r,i,o},rm.prototype.orderBy=function(t,e){try{return new rm(this.firestore,Uy(this._delegate,(n=void 0===e?"asc":e,r=Ay("orderBy",t),new Hy(r,n))))}catch(t){throw Kg(t,/(orderBy|where)\(\)/,"Query.$1()")}var n,r},rm.prototype.limit=function(t){try{return new rm(this.firestore,Uy(this._delegate,(tp("limit",e=t),new zy("limit",e,"F"))))}catch(t){throw Kg(t,"limit()","Query.limit()")}var e},rm.prototype.limitToLast=function(t){try{return new rm(this.firestore,Uy(this._delegate,(tp("limitToLast",e=t),new zy("limitToLast",e,"L"))))}catch(t){throw Kg(t,"limitToLast()","Query.limitToLast()")}var e},rm.prototype.startAt=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];try{return new rm(this.firestore,Uy(this._delegate,function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return new Wy("startAt",t,!0)}.apply(void 0,t)))}catch(t){throw Kg(t,"startAt()","Query.startAt()")}},rm.prototype.startAfter=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];try{return new rm(this.firestore,Uy(this._delegate,function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return new Wy("startAfter",t,!1)}.apply(void 0,t)))}catch(t){throw Kg(t,"startAfter()","Query.startAfter()")}},rm.prototype.endBefore=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];try{return new rm(this.firestore,Uy(this._delegate,function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return new Yy("endBefore",t,!0)}.apply(void 0,t)))}catch(t){throw Kg(t,"endBefore()","Query.endBefore()")}},rm.prototype.endAt=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];try{return new rm(this.firestore,Uy(this._delegate,function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return new Yy("endAt",t,!1)}.apply(void 0,t)))}catch(t){throw Kg(t,"endAt()","Query.endAt()")}},rm.prototype.isEqual=function(t){return yp(this._delegate,t._delegate)},rm.prototype.get=function(t){var e=this;return("cache"===(null==t?void 0:t.source)?function(e){e=Zd(e,op);var n=Zd(e.firestore,Ep),t=_p(n),r=new pg(n);return function(n,r){var t=this,i=new uu;return n.asyncQueue.enqueueAndForget(function(){return y(t,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return e=function(i,o,s){return y(this,void 0,void 0,function(){var e,n,r;return g(this,function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,gh(i,o,!0)];case 1:return r=t.sent(),e=new Df(o,r.Bn),n=e._o(r.documents),n=e.applyChanges(n,!1),s.resolve(n.snapshot),[3,3];case 2:return n=t.sent(),r=rf(n,"Failed to execute query '"+o+" against cache"),s.reject(r),[3,3];case 3:return[2]}})})},[4,Ad(n)];case 1:return[2,e.apply(void 0,[t.sent(),r,i])]}})})}),i.promise}(t,e._query).then(function(t){return new xy(n,r,e,t)})}:"server"===(null==t?void 0:t.source)?function(e){e=Zd(e,op);var n=Zd(e.firestore,Ep),t=_p(n),r=new pg(n);return Rd(t,e._query,{source:"server"}).then(function(t){return new xy(n,r,e,t)})}:function(e){e=Zd(e,op);var n=Zd(e.firestore,Ep),t=_p(n),r=new pg(n);return Vy(e._query),Rd(t,e._query).then(function(t){return new xy(n,r,e,t)})})(this._delegate).then(function(t){return new Jg(e.firestore,new xy(e.firestore._delegate,e._userDataWriter,e._delegate,t._snapshot))})},rm.prototype.onSnapshot=function(){for(var e=this,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=Gg(t),i=Qg(t,function(t){return new Jg(e.firestore,new xy(e.firestore._delegate,e._userDataWriter,e._delegate,t._snapshot))});return mg(this._delegate,r,i)},rm.prototype.withConverter=function(t){return new rm(this.firestore,t?this._delegate.withConverter(Fg.eu(this.firestore,t)):this._delegate.withConverter(null))},rm),$g=(Object.defineProperty(nm.prototype,"type",{get:function(){return this._delegate.type},enumerable:!1,configurable:!0}),Object.defineProperty(nm.prototype,"doc",{get:function(){return new Yg(this._firestore,this._delegate.doc)},enumerable:!1,configurable:!0}),Object.defineProperty(nm.prototype,"oldIndex",{get:function(){return this._delegate.oldIndex},enumerable:!1,configurable:!0}),Object.defineProperty(nm.prototype,"newIndex",{get:function(){return this._delegate.newIndex},enumerable:!1,configurable:!0}),nm),Jg=(Object.defineProperty(em.prototype,"query",{get:function(){return new Xg(this._firestore,this._delegate.query)},enumerable:!1,configurable:!0}),Object.defineProperty(em.prototype,"metadata",{get:function(){return this._delegate.metadata},enumerable:!1,configurable:!0}),Object.defineProperty(em.prototype,"size",{get:function(){return this._delegate.size},enumerable:!1,configurable:!0}),Object.defineProperty(em.prototype,"empty",{get:function(){return this._delegate.empty},enumerable:!1,configurable:!0}),Object.defineProperty(em.prototype,"docs",{get:function(){var e=this;return this._delegate.docs.map(function(t){return new Yg(e._firestore,t)})},enumerable:!1,configurable:!0}),em.prototype.docChanges=function(t){var e=this;return this._delegate.docChanges(t).map(function(t){return new $g(e._firestore,t)})},em.prototype.forEach=function(e,n){var r=this;this._delegate.forEach(function(t){e.call(n,new Yg(r._firestore,t))})},em.prototype.isEqual=function(t){return Fy(this._delegate,t._delegate)},em),Zg=(n(tm,Hg=Xg),Object.defineProperty(tm.prototype,"id",{get:function(){return this._delegate.id},enumerable:!1,configurable:!0}),Object.defineProperty(tm.prototype,"path",{get:function(){return this._delegate.path},enumerable:!1,configurable:!0}),Object.defineProperty(tm.prototype,"parent",{get:function(){var t=this._delegate.parent;return t?new Bg(this.firestore,t):null},enumerable:!1,configurable:!0}),tm.prototype.doc=function(t){try{return new Bg(this.firestore,void 0===t?dp(this._delegate):dp(this._delegate,t))}catch(t){throw Kg(t,"doc()","CollectionReference.doc()")}},tm.prototype.add=function(t){var e,n,r,i=this;return e=this._delegate,n=t,t=Zd(e.firestore,Ep),r=dp(e),n=sg(e.converter,n),vg(t,[Hp(Qp(e.firestore),"addDoc",r._key,n,null!==e.converter,{}).toMutation(r._key,hs.exists(!1))]).then(function(){return r}).then(function(t){return new Bg(i.firestore,t)})},tm.prototype.isEqual=function(t){return pp(this._delegate,t._delegate)},tm.prototype.withConverter=function(t){return new tm(this.firestore,t?this._delegate.withConverter(Fg.eu(this.firestore,t)):this._delegate.withConverter(null))},tm);function tm(t,e){var n=this;return(n=Hg.call(this,t,e)||this).firestore=t,n._delegate=e,n}function em(t,e){this._firestore=t,this._delegate=e}function nm(t,e){this._firestore=t,this._delegate=e}function rm(t,e){this.firestore=t,this._delegate=e,this._userDataWriter=new kg(t)}function im(){return null!==zg&&zg.apply(this,arguments)||this}function om(t,e){this._firestore=t,this._delegate=e}function sm(t){return Zd(t,ip)}um.documentId=function(){return new um(ui.keyField().canonicalString())},um.prototype.isEqual=function(t){return(t=v(t))instanceof Np&&this._delegate._internalPath.isEqual(t._internalPath)},N=um,am.serverTimestamp=function(){var t=new ey("serverTimestamp");return t._methodName="FieldValue.serverTimestamp",new am(t)},am.delete=function(){var t=new Wp("deleteField");return t._methodName="FieldValue.delete",new am(t)},am.arrayUnion=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return new ny("arrayUnion",t)}.apply(void 0,t);return n._methodName="FieldValue.arrayUnion",new am(n)},am.arrayRemove=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return new ry("arrayRemove",t)}.apply(void 0,t);return n._methodName="FieldValue.arrayRemove",new am(n)},am.increment=function(t){t=new iy("increment",t);return t._methodName="FieldValue.increment",new am(t)},am.prototype.isEqual=function(t){return this._delegate.isEqual(t._delegate)},I=am;function am(t){this._delegate=t}function um(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this._delegate=new(Np.bind.apply(Np,s([void 0],t)))}function cm(t){return e=this._delegate,s=t,n=_p(e=Zd(e,Ep)),t=new bp,function(n,t,r){var e,i=this,o=(e=s,t=bl(t),e=function(t){if(t instanceof Uint8Array)return hd(t,void 0);if(t instanceof ArrayBuffer)return hd(new Uint8Array(t),void 0);if(t instanceof ReadableStream)return t.getReader();throw new Error("Source of `toByteStreamReader` has to be a ArrayBuffer or ReadableStream")}("string"==typeof e?(new TextEncoder).encode(e):e),new fd(e,t));n.asyncQueue.enqueueAndForget(function(){return y(i,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return e=Zf,[4,Nd(n)];case 1:return e.apply(void 0,[t.sent(),o,r]),[2]}})})})}(n,e._databaseId,t),t;var e,s,n}function hm(t){var e=this,n=this._delegate,t=t;return function(n,r){var t=this;return n.asyncQueue.enqueue(function(){return y(t,void 0,void 0,function(){var e;return g(this,function(t){switch(t.label){case 0:return e=function(t,e){var n=t;return n.persistence.runTransaction("Get named query","readonly",function(t){return n.Ke.getNamedQuery(t,e)})},[4,Ad(n)];case 1:return[2,e.apply(void 0,[t.sent(),r])]}})})})}(_p(n=Zd(n,Ep)),t).then(function(t){return t?new op(n,null,t.query):null}).then(function(t){return t?new Xg(e,t):null})}var lm=(fm.prototype.setInstantiationMode=function(t){return this.instantiationMode=t,this},fm.prototype.setMultipleInstances=function(t){return this.multipleInstances=t,this},fm.prototype.setServiceProps=function(t){return this.serviceProps=t,this},fm.prototype.setInstanceCreatedCallback=function(t){return this.onInstanceCreated=t,this},fm);function fm(t,e,n){this.name=t,this.instanceFactory=e,this.type=n,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}var dm,pm,ym={Firestore:Cg,GeoPoint:Rp,Timestamp:$r,Blob:Dg,Transaction:Pg,WriteBatch:Mg,DocumentReference:Bg,DocumentSnapshot:Wg,Query:Xg,QueryDocumentSnapshot:Yg,QuerySnapshot:Jg,CollectionReference:Zg,FieldPath:N,FieldValue:I,setLogLevel:function(t){Fr.setLogLevel(t)},CACHE_SIZE_UNLIMITED:-1};(dm=e.default).INTERNAL.registerComponent(new lm("firestore",function(t){var e=t.getProvider("app").getImmediate();return e=e,t=t.getProvider("auth-internal"),new Cg(e,new Ep(e,t),new Ng)},"PUBLIC").setServiceProps(Object.assign({},ym))),dm.registerVersion("@firebase/firestore","2.3.7"),(pm=Cg).prototype.loadBundle=cm,pm.prototype.namedQuery=hm}.apply(this,arguments)}catch(t){throw console.error(t),new Error("Cannot instantiate firebase-firestore.js - be sure to load firebase-app.js first.")}});
//# sourceMappingURL=firebase-firestore.js.map