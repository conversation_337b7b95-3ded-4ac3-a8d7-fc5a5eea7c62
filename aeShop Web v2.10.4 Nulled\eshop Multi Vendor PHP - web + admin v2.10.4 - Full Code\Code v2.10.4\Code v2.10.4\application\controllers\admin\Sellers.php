<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Sellers extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->load->database();
        $this->load->library(['ion_auth', 'form_validation', 'upload']);
        $this->load->helper(['url', 'language', 'file']);
        $this->load->model('Seller_model');
        $this->data['firebase_project_id'] = get_settings('firebase_project_id');
        $this->data['service_account_file'] = get_settings('service_account_file');
        if (!has_permissions('read', 'seller')) {
            $this->session->set_flashdata('authorize_flag', PERMISSION_ERROR_MSG);
            redirect('admin/home', 'refresh');
        }
    }

    public function index()
    {
        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin()) {
            $this->data['main_page'] = TABLES . 'manage-seller';
            $settings = get_settings('system_settings', true);
            $this->data['title'] = 'Seller Management | ' . $settings['app_name'];
            $this->data['meta_description'] = ' Seller Management  | ' . $settings['app_name'];
            $this->data['sellers_status'] = $this->db->select(' u.username as seller_name,u.id as seller_id,sd.category_ids,sd.id as seller_data_id,sd.status as seller_status')
                ->join('users_groups ug', ' ug.user_id = u.id ')
                ->join('seller_data sd', ' sd.user_id = u.id ')
                ->where(['ug.group_id' => '4'])
                ->get('users u')->result_array();
            $this->load->view('admin/template', $this->data);
        } else {
            redirect('admin/login', 'refresh');
        }
    }

    public function manage_seller()
    {
        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin()) {
            $this->data['main_page'] = FORMS . 'seller';
            $settings = get_settings('system_settings', true);
            $shipping_method = get_settings('shipping_method', true);
            $this->data['title'] = 'Add Seller | ' . $settings['app_name'];
            $this->data['meta_description'] = 'Add Seller | ' . $settings['app_name'];
            $this->data['categories'] = $this->category_model->get_categories();
            $this->data['shipping_method'] = $shipping_method;
            $this->data['cities'] = fetch_details('cities');
            $this->data['category_flag'] = "1";

            if (isset($_GET['edit_id']) && !empty($_GET['edit_id'])) {


                $seller_id = $_GET['edit_id'];
                $seller_data = fetch_details(table: "seller_data", where: ["user_id" => $seller_id]);
                $cate_ids = fetch_details(table: "seller_data", where: ["user_id" => $seller_id], fields: ["category_ids"]);
                if (empty($seller_data)) {
                    // Redirect to orders page if the order does not exist
                    redirect('admin/sellers', 'refresh');
                    return;
                }

                $selected_categories = (!empty($cate_ids[0]['category_ids'])) ? (explode(separator: ",", string: $cate_ids[0]['category_ids'])) : (array());
                $available_categories = fetch_details(table: "seller_commission", where: ["seller_id" => $seller_id], fields: ["category_id"]);

                if (count($available_categories) === count($selected_categories)) {
                    $this->data['category_flag'] = "0";
                }



                $this->data['title'] = 'Update Seller | ' . $settings['app_name'];
                $this->data['meta_description'] = 'Update Seller | ' . $settings['app_name'];
                $this->data['fetched_data'] = $this->db->select(' u.*,sd.* ')
                    ->join('users_groups ug', ' ug.user_id = u.id ')
                    ->join('seller_data sd', ' sd.user_id = u.id ')
                    ->where(['ug.group_id' => '4', 'ug.user_id' => $_GET['edit_id']])
                    ->get('users u')
                    ->result_array();
            }
            $this->load->view('admin/template', $this->data);
        } else {
            redirect('admin/login', 'refresh');
        }
    }

    public function view_sellers()
    {
        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin()) {
            if ($_GET['seller_status'] && !empty($_GET['seller_status'])) {
                return $this->Seller_model->get_sellers_list($_GET['seller_status']);
            }
            return $this->Seller_model->get_sellers_list();
        } else {
            redirect('admin/login', 'refresh');
        }
    }

    public function remove_sellers()
    {
        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin()) {

            if (print_msg(!has_permissions('delete', 'seller'), PERMISSION_ERROR_MSG, 'seller', false)) {
                return true;
            }

            if (!isset($_GET['id']) && empty($_GET['id'])) {
                $this->response['error'] = true;
                $this->response['message'] = 'Seller id is required';
                print_r(json_encode($this->response));
                return;
                exit();
            }
            $all_status = [0, 1, 2, 7];
            $status = $this->input->get('status', true);
            $id = $this->input->get('id', true);
            if (!in_array($status, $all_status)) {
                $this->response['error'] = true;
                $this->response['message'] = 'Invalid status';
                print_r(json_encode($this->response));
                return;
                exit();
            }
            if ($status == 2) {
                $this->response['error'] = true;
                $this->response['message'] = 'Please approve seller first for delete only seller.';
                print_r(json_encode($this->response));
                return;
                exit();
            }
            $status = ($status == 7) ? 1 : (($status == 1) ? 7 : 1);

            if (update_details(['status' => $status], ['user_id' => $id], 'seller_data') == TRUE) {
                $this->response['error'] = false;
                $this->response['message'] = 'Seller removed succesfully';
                print_r(json_encode($this->response));
            } else {
                $this->response['error'] = true;
                $this->response['message'] = 'Something Went Wrong';
                print_r(json_encode($this->response));
            }
        } else {
            redirect('admin/login', 'refresh');
        }
    }

    public function delete_sellers()
    {
        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin()) {

            if (print_msg(!has_permissions('delete', 'seller'), PERMISSION_ERROR_MSG, 'seller', false)) {
                return true;
            }

            if (!isset($_GET['id']) && empty($_GET['id'])) {
                $this->response['error'] = true;
                $this->response['message'] = 'Seller id is required';
                print_r(json_encode($this->response));
                return;
                exit();
            }
            $id = $this->input->get('id', true);
            $user = fetch_details('users', ['id' => $id]);

            $delete = array(
                'users' => 0,
                "media" => 0,
                "payment_requests" => 0,
                "products" => 0,
                "product_attributes" => 0,
                "order_items" => 0,
                "orders" => 0,
                "order_bank_transfer" => 0,
                "seller_commission" => 0,
                "seller_data" => 0,
            );

            $seller_media = fetch_details('seller_data', ['user_id' => $id], 'id,logo,authorized_signature,national_identity_card,address_proof');
            if (!empty($seller_media)) {
                if (!empty($seller_media[0])) {
                    if (!empty($seller_media[0]['logo'])) {
                        unlink(FCPATH . $seller_media[0]['logo']);
                    }
                    if (!empty($seller_media[0]['national_identity_card'])) {
                        unlink(FCPATH . $seller_media[0]['national_identity_card']);
                    }
                    if (!empty($seller_media[0]['address_proof'])) {
                        unlink(FCPATH . $seller_media[0]['address_proof']);
                    }
                    if (!empty($seller_media[0]['authorized_signature'])) {
                        unlink(FCPATH . $seller_media[0]['authorized_signature']);
                    }
                }
            }

            if (update_details(['seller_id' => 0], ['seller_id' => $id], 'media')) {
                $delete['media'] = 1;
            }

            /* check for retur requesst if seller's product have */
            $return_req = $this->db->where(['p.seller_id' => $id])->join('products p', 'p.id=rr.product_id')->get('return_requests rr')->result_array();
            if (!empty($return_req)) {
                $this->response['error'] = true;
                $this->response['message'] = 'Seller could not be deleted.Either found some order items which has return request.Finalize those before deleting it';
                print_r(json_encode($this->response));
                return;
                exit();
            }
            $pr_ids = fetch_details("products", ['seller_id' => $id], "id");
            if (delete_details(['seller_id' => $id], 'products')) {
                $delete['products'] = 1;
            }
            foreach ($pr_ids as $row) {
                if (delete_details(['product_id' => $row['id']], 'product_attributes')) {
                    $delete['product_attributes'] = 1;
                }
            }

            /* check order items */
            $order_items = fetch_details('order_items', ['seller_id' => $id], 'id,order_id');
            if (delete_details(['seller_id' => $id], 'order_items')) {
                $delete['order_items'] = 1;
            }
            if (!empty($order_items)) {
                $res_order_id = array_values(array_unique(array_column($order_items, "order_id")));
                for ($i = 0; $i < count($res_order_id); $i++) {
                    $orders = $this->db->where('oi.seller_id != ' . $id . ' and oi.order_id=' . $res_order_id[$i])->join('orders o', 'o.id=oi.order_id', 'right')->get('order_items oi')->result_array();
                    if (empty($orders)) {
                        // delete orders
                        if (delete_details(['seller_id' => $id], 'order_items')) {
                            $delete['order_items'] = 1;
                        }
                        if (delete_details(['id' => $res_order_id[$i]], 'orders')) {
                            $delete['orders'] = 1;
                        }
                        if (delete_details(['order_id' => $res_order_id[$i]], 'order_bank_transfer')) {
                            $delete['order_bank_transfer'] = 1;
                        }
                    }
                }
            } else {
                $delete['order_items'] = 1;
                $delete['orders'] = 1;
                $delete['order_bank_transfer'] = 1;
            }
            if (!empty($res_order_id)) {

                if (delete_details(['id' => $res_order_id[$i]], 'orders')) {
                    $delete['orders'] = 1;
                }
            } else {
                $delete['orders'] = 1;
            }

            if (delete_details(['seller_id' => $id], 'seller_commission')) {
                $delete['seller_commission'] = 1;
            }
            if (delete_details(['user_id' => $id], 'seller_data')) {
                $delete['seller_data'] = 1;
            }

            $deleted = FALSE;
            if (isset($delete['seller_data']) && !empty($delete['seller_data']) && isset($delete['seller_commission']) && !empty($delete['seller_commission'])) {
                $deleted = TRUE;
            }
            if (delete_details(['id' => $id], 'users')) {
                $delete['users'] = 1;
            }
            if (delete_details(['user_id' => $id], 'users_groups')) {
                $delete['users'] = 1;
                $this->response['error'] = false;
                $this->response['message'] = 'Seller deleted from seller succesfully';
                print_r(json_encode($this->response));
            } else {
                $this->response['error'] = true;
                $this->response['message'] = 'Something Went Wrong';
                print_r(json_encode($this->response));
            }
        } else {
            redirect('admin/login', 'refresh');
        }
    }


    public function add_seller()
    {


        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin()) {

            if (isset($_POST['edit_seller'])) {
                if (print_msg(!has_permissions('update', 'seller'), PERMISSION_ERROR_MSG, 'seller')) {
                    return true;
                }
            } else {
                if (print_msg(!has_permissions('create', 'seller'), PERMISSION_ERROR_MSG, 'seller')) {
                    return true;
                }
            }
            $user = $this->ion_auth->user()->row();
            $this->form_validation->set_rules('name', 'Name', 'trim|required|xss_clean');
            $this->form_validation->set_rules('email', 'Mail', 'trim|required|xss_clean');
            if (!isset($_POST['edit_seller'])) {
                $this->form_validation->set_rules('mobile', 'Mobile', 'trim|required|numeric|xss_clean|min_length[5]|max_length[16]|edit_unique[users.mobile.' . $user->id . ']');
                $this->form_validation->set_rules('password', 'Password', 'trim|required|xss_clean');
                $this->form_validation->set_rules('confirm_password', 'Confirm password', 'trim|required|matches[password]|xss_clean');
            }
            $this->form_validation->set_rules('address', 'Address', 'trim|required|xss_clean');
            $this->form_validation->set_rules('store_name', 'Store Name', 'trim|required|xss_clean');
            $this->form_validation->set_rules('tax_name', 'Tax Name', 'trim|xss_clean');
            $this->form_validation->set_rules('tax_number', 'Tax Number', 'trim|xss_clean');
            $this->form_validation->set_rules('status', 'Status', 'trim|required|xss_clean');
            //seo validation
            $this->form_validation->set_rules('seo_page_title', ' SEO Page Title', 'trim|xss_clean');
            $this->form_validation->set_rules('seo_meta_keywords', 'SEO Meta Keywords', 'trim|xss_clean');
            $this->form_validation->set_rules('seo_meta_description', 'SEO Meta Description', 'trim|xss_clean');
            $this->form_validation->set_rules('seo_og_image', 'SEO Open Graph Image', 'trim|xss_clean');
            $this->form_validation->set_rules('deliverable_zipcode_type', 'Deliverable zipcode type', 'trim|xss_clean');
            $this->form_validation->set_rules('deliverable_city_type', 'Deliverable city type', 'trim|xss_clean');

            if (!isset($_POST['edit_seller'])) {
                if (isset($_POST['global_commission']) && empty($_POST['global_commission'])) {
                    $this->form_validation->set_rules('commission_data', 'Category Commission data or Global Commission is missing', 'trim|required|xss_clean');
                }
            }

            $shipping_method = get_settings('shipping_method', true);

            if (isset($shipping_method['pincode_wise_deliverability']) && !empty($shipping_method['pincode_wise_deliverability']) && ($shipping_method['pincode_wise_deliverability'] == 1) && isset($shipping_method['local_shipping_method']) &&  $shipping_method['local_shipping_method'] == 1) {
                if (isset($_POST['deliverable_zipcode_type']) && !empty($_POST['deliverable_zipcode_type']) && ($_POST['deliverable_zipcode_type'] == INCLUDED)) {
                    $this->form_validation->set_rules('serviceable_zipcodes[]', 'Serviceable Zipcodes', 'trim|required|xss_clean');
                }
            }
            if (isset($shipping_method['city_wise_deliverability']) && !empty($shipping_method['city_wise_deliverability']) && ($shipping_method['city_wise_deliverability'] == 1) && isset($shipping_method['local_shipping_method']) &&  $shipping_method['local_shipping_method'] == 1) {
                if (isset($_POST['deliverable_city_type']) && !empty($_POST['deliverable_city_type']) && ($_POST['deliverable_city_type'] == INCLUDED)) {
                    $this->form_validation->set_rules('serviceable_cities[]', 'Serviceable Cities', 'trim|required|xss_clean');
                }
            }


            if (!isset($_POST['edit_seller'])) {
                if (isset($_FILES) && !empty($_FILES) && empty($_FILES['store_logo']['name'])) {
                    $this->form_validation->set_rules('store_logo', 'Store Logo', 'trim|required|xss_clean');
                }
                if (isset($_FILES) && !empty($_FILES) && empty($_FILES['authorized_signature']['name'])) {
                    $this->form_validation->set_rules('authorized_signature', 'Authorized Signature', 'trim|required|xss_clean');
                }
            }

            if (!$this->form_validation->run()) {

                $this->response['error'] = true;
                $this->response['csrfName'] = $this->security->get_csrf_token_name();
                $this->response['csrfHash'] = $this->security->get_csrf_hash();
                $this->response['message'] = validation_errors();
                print_r(json_encode($this->response));
                return false;
            }

            // process images of seller

            if (!file_exists(FCPATH . SELLER_DOCUMENTS_PATH)) {
                mkdir(FCPATH . SELLER_DOCUMENTS_PATH, 0777);
            }

            //process store logo
            $temp_array_logo = $store_logo_doc = array();
            $logo_files = $_FILES;
            $store_logo_error = "";
            $config = [
                'upload_path' => FCPATH . SELLER_DOCUMENTS_PATH,
                'allowed_types' => 'jpg|png|jpeg|gif',
                'max_size' => 8000,
            ];
            if (isset($logo_files['store_logo']) && !empty($logo_files['store_logo']['name']) && isset($logo_files['store_logo']['name'])) {
                $other_img = $this->upload;
                $other_img->initialize($config);

                if (isset($_POST['edit_seller']) && !empty($_POST['edit_seller']) && isset($_POST['old_store_logo']) && !empty($_POST['old_store_logo'])) {
                    $old_logo = explode('/', $this->input->post('old_store_logo', true));
                    delete_images(SELLER_DOCUMENTS_PATH, $old_logo[2]);
                }

                if (!empty($logo_files['store_logo']['name'])) {

                    $_FILES['temp_image']['name'] = $logo_files['store_logo']['name'];
                    $_FILES['temp_image']['type'] = $logo_files['store_logo']['type'];
                    $_FILES['temp_image']['tmp_name'] = $logo_files['store_logo']['tmp_name'];
                    $_FILES['temp_image']['error'] = $logo_files['store_logo']['error'];
                    $_FILES['temp_image']['size'] = $logo_files['store_logo']['size'];
                    if (!$other_img->do_upload('temp_image')) {
                        $store_logo_error = 'Images :' . $store_logo_error . ' ' . $other_img->display_errors();
                    } else {
                        $temp_array_logo = $other_img->data();
                        resize_review_images($temp_array_logo, FCPATH . SELLER_DOCUMENTS_PATH);
                        $store_logo_doc = SELLER_DOCUMENTS_PATH . $temp_array_logo['file_name'];
                    }
                } else {
                    $_FILES['temp_image']['name'] = $logo_files['store_logo']['name'];
                    $_FILES['temp_image']['type'] = $logo_files['store_logo']['type'];
                    $_FILES['temp_image']['tmp_name'] = $logo_files['store_logo']['tmp_name'];
                    $_FILES['temp_image']['error'] = $logo_files['store_logo']['error'];
                    $_FILES['temp_image']['size'] = $logo_files['store_logo']['size'];
                    if (!$other_img->do_upload('temp_image')) {
                        $store_logo_error = $other_img->display_errors();
                    }
                }
                //Deleting Uploaded Images if any overall error occured
                if ($store_logo_error != NULL || !$this->form_validation->run()) {
                    if (isset($store_logo_doc) && !empty($store_logo_doc || !$this->form_validation->run())) {
                        foreach ($store_logo_doc as $key => $val) {
                            unlink(FCPATH . SELLER_DOCUMENTS_PATH . $store_logo_doc[$key]);
                        }
                    }
                }
            }

            if ($store_logo_error != NULL) {
                $this->response['error'] = true;
                $this->response['csrfName'] = $this->security->get_csrf_token_name();
                $this->response['csrfHash'] = $this->security->get_csrf_hash();
                $this->response['message'] = $store_logo_error;
                print_r(json_encode($this->response));
                return;
            }


            //process Authorized Signature
            $temp_array_authorized_signature = $authorized_signature_doc = array();
            $authorized_signature_files = $_FILES;
            $authorized_signature_error = "";
            $config = [
                'upload_path' => FCPATH . SELLER_DOCUMENTS_PATH,
                'allowed_types' => 'jpg|png|jpeg|gif',
                'max_size' => 8000,
            ];
            if (isset($authorized_signature_files['authorized_signature']) && !empty($authorized_signature_files['authorized_signature']['name']) && isset($authorized_signature_files['authorized_signature']['name'])) {
                $other_img = $this->upload;
                $other_img->initialize($config);

                if (isset($_POST['edit_seller']) && !empty($_POST['edit_seller']) && isset($_POST['old_authorized_signature']) && !empty($_POST['old_authorized_signature'])) {
                    $old_authorized_signature = explode('/', $this->input->post('old_authorized_signature', true));
                    delete_images(SELLER_DOCUMENTS_PATH, $old_authorized_signature[2]);
                }

                if (!empty($authorized_signature_files['authorized_signature']['name'])) {

                    $_FILES['temp_image']['name'] = $authorized_signature_files['authorized_signature']['name'];
                    $_FILES['temp_image']['type'] = $authorized_signature_files['authorized_signature']['type'];
                    $_FILES['temp_image']['tmp_name'] = $authorized_signature_files['authorized_signature']['tmp_name'];
                    $_FILES['temp_image']['error'] = $authorized_signature_files['authorized_signature']['error'];
                    $_FILES['temp_image']['size'] = $authorized_signature_files['authorized_signature']['size'];
                    if (!$other_img->do_upload('temp_image')) {
                        $authorized_signature_error = 'Images :' . $authorized_signature_error . ' ' . $other_img->display_errors();
                    } else {
                        $temp_array_authorized_signature = $other_img->data();
                        resize_review_images($temp_array_authorized_signature, FCPATH . SELLER_DOCUMENTS_PATH);
                        $authorized_signature_doc = SELLER_DOCUMENTS_PATH . $temp_array_authorized_signature['file_name'];
                    }
                } else {
                    $_FILES['temp_image']['name'] = $authorized_signature_files['authorized_signature']['name'];
                    $_FILES['temp_image']['type'] = $authorized_signature_files['authorized_signature']['type'];
                    $_FILES['temp_image']['tmp_name'] = $authorized_signature_files['authorized_signature']['tmp_name'];
                    $_FILES['temp_image']['error'] = $authorized_signature_files['authorized_signature']['error'];
                    $_FILES['temp_image']['size'] = $authorized_signature_files['authorized_signature']['size'];
                    if (!$other_img->do_upload('temp_image')) {
                        $authorized_signature_error = $other_img->display_errors();
                    }
                }
                //Deleting Uploaded Images if any overall error occured
                if ($authorized_signature_error != NULL || !$this->form_validation->run()) {
                    if (isset($authorized_signature_doc) && !empty($authorized_signature_doc || !$this->form_validation->run())) {
                        foreach ($authorized_signature_doc as $key => $val) {
                            unlink(FCPATH . SELLER_DOCUMENTS_PATH . $authorized_signature_doc[$key]);
                        }
                    }
                }
            }

            if ($authorized_signature_error != NULL) {
                $this->response['error'] = true;
                $this->response['csrfName'] = $this->security->get_csrf_token_name();
                $this->response['csrfHash'] = $this->security->get_csrf_hash();
                $this->response['message'] = $authorized_signature_error;
                print_r(json_encode($this->response));
                return;
            }

            //process national_identity_card
            $temp_array_id_card = $id_card_doc = array();
            $id_card_files = $_FILES;
            $id_card_error = "";
            $config = [
                'upload_path' => FCPATH . SELLER_DOCUMENTS_PATH,
                'allowed_types' => 'jpg|png|jpeg|gif',
                'max_size' => 8000,
            ];
            if (isset($id_card_files['national_identity_card']) && !empty($id_card_files['national_identity_card']['name']) && isset($id_card_files['national_identity_card']['name'])) {
                $other_img = $this->upload;
                $other_img->initialize($config);

                if (isset($_POST['edit_seller']) && !empty($_POST['edit_seller']) && isset($_POST['old_national_identity_card']) && !empty($_POST['old_national_identity_card'])) {
                    $old_national_identity_card = explode('/', $this->input->post('old_national_identity_card', true));
                    delete_images(SELLER_DOCUMENTS_PATH, $old_national_identity_card[2]);
                }

                if (!empty($id_card_files['national_identity_card']['name'])) {

                    $_FILES['temp_image']['name'] = $id_card_files['national_identity_card']['name'];
                    $_FILES['temp_image']['type'] = $id_card_files['national_identity_card']['type'];
                    $_FILES['temp_image']['tmp_name'] = $id_card_files['national_identity_card']['tmp_name'];
                    $_FILES['temp_image']['error'] = $id_card_files['national_identity_card']['error'];
                    $_FILES['temp_image']['size'] = $id_card_files['national_identity_card']['size'];
                    if (!$other_img->do_upload('temp_image')) {
                        $id_card_error = 'Images :' . $id_card_error . ' ' . $other_img->display_errors();
                    } else {
                        $temp_array_id_card = $other_img->data();
                        resize_review_images($temp_array_id_card, FCPATH . SELLER_DOCUMENTS_PATH);
                        $id_card_doc = SELLER_DOCUMENTS_PATH . $temp_array_id_card['file_name'];
                    }
                } else {
                    $_FILES['temp_image']['name'] = $id_card_files['national_identity_card']['name'];
                    $_FILES['temp_image']['type'] = $id_card_files['national_identity_card']['type'];
                    $_FILES['temp_image']['tmp_name'] = $id_card_files['national_identity_card']['tmp_name'];
                    $_FILES['temp_image']['error'] = $id_card_files['national_identity_card']['error'];
                    $_FILES['temp_image']['size'] = $id_card_files['national_identity_card']['size'];
                    if (!$other_img->do_upload('temp_image')) {
                        $id_card_error = $other_img->display_errors();
                    }
                }
                //Deleting Uploaded Images if any overall error occured
                if ($id_card_error != NULL || !$this->form_validation->run()) {
                    if (isset($id_card_doc) && !empty($id_card_doc || !$this->form_validation->run())) {
                        foreach ($id_card_doc as $key => $val) {
                            unlink(FCPATH . SELLER_DOCUMENTS_PATH . $id_card_doc[$key]);
                        }
                    }
                }
            }

            if ($id_card_error != NULL) {
                $this->response['error'] = true;
                $this->response['csrfName'] = $this->security->get_csrf_token_name();
                $this->response['csrfHash'] = $this->security->get_csrf_hash();
                $this->response['message'] = $id_card_error;
                print_r(json_encode($this->response));
                return;
            }

            //process address_proof
            $temp_array_proof = $proof_doc = array();
            $proof_files = $_FILES;
            $proof_error = "";
            $config = [
                'upload_path' => FCPATH . SELLER_DOCUMENTS_PATH,
                'allowed_types' => 'jpg|png|jpeg|gif',
                'max_size' => 8000,
            ];
            if (isset($proof_files['address_proof']) && !empty($proof_files['address_proof']['name']) && isset($proof_files['address_proof']['name'])) {
                $other_img = $this->upload;
                $other_img->initialize($config);

                if (isset($_POST['edit_seller']) && !empty($_POST['edit_seller']) && isset($_POST['old_address_proof']) && !empty($_POST['old_address_proof'])) {
                    $old_address_proof = explode('/', $this->input->post('old_address_proof', true));
                    delete_images(SELLER_DOCUMENTS_PATH, $old_address_proof[2]);
                }

                if (!empty($proof_files['address_proof']['name'])) {

                    $_FILES['temp_image']['name'] = $proof_files['address_proof']['name'];
                    $_FILES['temp_image']['type'] = $proof_files['address_proof']['type'];
                    $_FILES['temp_image']['tmp_name'] = $proof_files['address_proof']['tmp_name'];
                    $_FILES['temp_image']['error'] = $proof_files['address_proof']['error'];
                    $_FILES['temp_image']['size'] = $proof_files['address_proof']['size'];
                    if (!$other_img->do_upload('temp_image')) {
                        $proof_error = 'Images :' . $proof_error . ' ' . $other_img->display_errors();
                    } else {
                        $temp_array_proof = $other_img->data();
                        resize_review_images($temp_array_proof, FCPATH . SELLER_DOCUMENTS_PATH);
                        $proof_doc = SELLER_DOCUMENTS_PATH . $temp_array_proof['file_name'];
                    }
                } else {
                    $_FILES['temp_image']['name'] = $proof_files['address_proof']['name'];
                    $_FILES['temp_image']['type'] = $proof_files['address_proof']['type'];
                    $_FILES['temp_image']['tmp_name'] = $proof_files['address_proof']['tmp_name'];
                    $_FILES['temp_image']['error'] = $proof_files['address_proof']['error'];
                    $_FILES['temp_image']['size'] = $proof_files['address_proof']['size'];
                    if (!$other_img->do_upload('temp_image')) {
                        $proof_error = $other_img->display_errors();
                    }
                }
                //Deleting Uploaded Images if any overall error occured
                if ($proof_error != NULL || !$this->form_validation->run()) {
                    if (isset($proof_doc) && !empty($proof_doc || !$this->form_validation->run())) {
                        foreach ($proof_doc as $key => $val) {
                            unlink(FCPATH . SELLER_DOCUMENTS_PATH . $proof_doc[$key]);
                        }
                    }
                }
            }

            if ($proof_error != NULL) {
                $this->response['error'] = true;
                $this->response['csrfName'] = $this->security->get_csrf_token_name();
                $this->response['csrfHash'] = $this->security->get_csrf_hash();
                $this->response['message'] = $proof_error;
                print_r(json_encode($this->response));
                return;
            }

            $categories = "";
            // process categories
            if (isset($_POST['commission_data']) && !empty($_POST['commission_data'])) {

                $commission_data = json_decode($this->input->post('commission_data'), true);
                if (!is_array($commission_data['category_id'])) {
                    $categories = $commission_data['category_id'];
                } else {
                    if (count($commission_data['category_id']) >= 2) {
                        $categories = implode(",", array_unique($commission_data['category_id']));
                    }
                }
            }

            // process permissions of sellers
            $permmissions = array();
            $permmissions['require_products_approval'] = (isset($_POST['require_products_approval']) && !empty($_POST['require_products_approval'])) ? 1 : 0;
            $permmissions['customer_privacy'] = (isset($_POST['customer_privacy']) && !empty($_POST['customer_privacy'])) ? 1 : 0;
            $permmissions['view_order_otp'] = (isset($_POST['view_order_otp']) && !empty($_POST['view_order_otp'])) ? 1 : 1;
            $permmissions['assign_delivery_boy'] = (isset($_POST['assign_delivery_boy']) && !empty($_POST['assign_delivery_boy'])) ? 1 : 1;

            if (isset($_POST['edit_seller'])) {
                if (empty($_POST['commission_data'])) {
                    $category_ids = fetch_details("seller_data", ['id' => $this->input->post('edit_seller_data_id', true)], "category_ids");
                    $categories = $category_ids[0]['category_ids'];
                }
                $current_status = fetch_details('seller_data', ['user_id' => $this->input->post('edit_seller')], 'status')[0];

                if ($current_status['status'] != $this->input->post('status', true)) {
                    $system_settings = get_settings('system_settings', true);
                    if ($this->input->post('status', true) == 0 || $this->input->post('status', true) == '0') {
                        $title = 'Account Deactivation Notice';
                        $fcm_admin_msg = 'We hope this message finds you well. We are writing to inform you about the deactivation of your seller account on our platform.';
                        $mail_admin_msg = 'We hope this message finds you well. We are writing to inform you about the deactivation of your seller account on our platform.Please be aware that this action is not reversible, and your access to the seller dashboard and associated services will be terminated.';
                    }
                    if ($this->input->post('status', true) == 1 || $this->input->post('status', true) == '1') {
                        $title = 'Congratulations! Your Seller Account Has Been Approved';
                        $fcm_admin_msg = 'We are delighted to inform you that your application to become an approved seller on our platform has been successful! Congratulations on this significant milestone.';
                        $mail_admin_msg = 'We are delighted to inform you that your application to become an approved seller on our platform has been successful! Congratulations on this significant milestone.With your approval, you gain access to a range of exclusive features and tools that will help you manage your business effectively. Our platform is designed to empower sellers like you, providing all the necessary resources to enhance your success.';
                    }
                    if ($this->input->post('status', true) == 2 || $this->input->post('status', true) == '2') {
                        $title = 'Update on Your Seller Account Application';
                        $fcm_admin_msg = 'We hope this message finds you well. We wanted to take a moment to inform you about the status of your recent seller account application with ' . $system_settings['app_name'];
                        $mail_admin_msg = 'We hope this message finds you well. We wanted to take a moment to inform you about the status of your recent seller account application with ' . $system_settings['app_name'] . 'We appreciate your interest in becoming a seller on our platform and thank you for taking the time to submit your application. We understand that starting your journey as a seller requires dedication and effort, and we value your commitment to becoming part of our growing community.';
                    }
                    $seller_fcm = fetch_details('users', ['id' => $this->input->post('edit_seller')], 'fcm_id,email,username,platform_type');
                    // Step 1: Group by platform
                    $groupedByPlatform = [];
                    foreach ($seller_fcm as $item) {
                        $platform = $item['platform_type'];
                        $groupedByPlatform[$platform][] = $item['fcm_id'];
                    }

                    // Step 2: Chunk each platform group into arrays of 1000
                    $fcm_ids = [];
                    foreach ($groupedByPlatform as $platform => $fcmIds) {
                        $fcm_ids[$platform] = array_chunk($fcmIds, 1000);
                    }

                    $seller_fcm_id[0] = $seller_fcm[0]['fcm_id'];

                    $registrationIDs_chunks = $fcm_ids;
                    $firebase_project_id = $this->data['firebase_project_id'];
                    $service_account_file = $this->data['service_account_file'];
                    $email_settings = get_settings('email_settings', true);

                    if (!empty($seller_fcm_id) && isset($firebase_project_id) && isset($service_account_file) && !empty($firebase_project_id) && !empty($service_account_file)) {
                        $fcmMsg = array(
                            'title' => $title,
                            'body' => $fcm_admin_msg,
                            'type' => "seller_account_update",
                        );
                        send_notification($fcmMsg, $registrationIDs_chunks, $fcmMsg);
                    }
                    if (isset($email_settings) && !empty($email_settings)) {
                        $email_message = array(
                            'username' => 'Hello, Dear <b>' . ucfirst($seller_fcm[0]['username']) . '</b>, ',
                            'subject' => $title,
                            'email' => $seller_fcm[0]['email'],
                            'message' => $mail_admin_msg
                        );
                        send_mail($seller_fcm[0]['email'], $title, $this->load->view('admin/pages/view/contact-email-template', $email_message, TRUE));
                    }
                }

                if (isset($_POST['serviceable_zipcodes']) && !empty($_POST['serviceable_zipcodes'])) {
                    $serviceable_zipcodes = implode(",", $this->input->post('serviceable_zipcodes', true));
                } else {
                    $serviceable_zipcodes = NULL;
                }
                if (isset($_POST['serviceable_cities']) && !empty($_POST['serviceable_cities'])) {
                    $serviceable_cities = implode(",", $this->input->post('serviceable_cities', true));
                } else {
                    $serviceable_cities = NULL;
                }

                if (isset($_POST['seo_meta_keywords']) && $_POST['seo_meta_keywords'] != '') {
                    $_POST['seo_meta_keywords'] = json_decode($_POST['seo_meta_keywords'], 1);
                    $seo_meta_keywords = array_column($_POST['seo_meta_keywords'], 'value');
                    $_POST['seo_meta_keywords'] = implode(",", $seo_meta_keywords);
                }
                $seller_data = array(
                    'user_id' => $this->input->post('edit_seller', true),
                    'edit_seller_data_id' => $this->input->post('edit_seller_data_id', true),
                    'store_logo' => (!empty($store_logo_doc)) ? $store_logo_doc : $this->input->post('old_store_logo', true),
                    'authorized_signature' => (!empty($authorized_signature_doc)) ? $authorized_signature_doc : $this->input->post('old_authorized_signature', true),
                    'status' => $this->input->post('status', true),
                    'tax_number' => $this->input->post('tax_number', true),
                    'tax_name' => $this->input->post('tax_name', true),
                    'store_description' => $this->input->post('store_description', true),
                    'store_url' => $this->input->post('store_url', true),
                    'store_name' => $this->input->post('store_name', true),
                    'global_commission' => (isset($_POST['global_commission']) && !empty($_POST['global_commission'])) ? $this->input->post('global_commission', true) : 0,
                    'categories' => $categories,
                    'permissions' => $permmissions,
                    'deliverable_zipcode_type' => $this->input->post('deliverable_zipcode_type', true),
                    'deliverable_city_type' => $this->input->post('deliverable_city_type', true),
                    'serviceable_zipcodes' => $serviceable_zipcodes,
                    'serviceable_cities' => $serviceable_cities,
                    'seo_page_title' => $this->input->post('seo_page_title', true),
                    'seo_meta_keywords' => $this->input->post('seo_meta_keywords', true),
                    'seo_meta_description' => $this->input->post('seo_meta_description', true),
                    'seo_og_image' => $this->input->post('seo_og_image', true),
                    'slug' => create_unique_slug($this->input->post('store_name', true), 'seller_data')
                );
                $seller_profile = array(
                    'name' => $this->input->post('name', true),
                    'email' => $this->input->post('email', true),
                    'mobile' => $this->input->post('mobile', true),
                    'password' => $this->input->post('password', true),
                    'address' => $this->input->post('address', true),
                    'latitude' => $this->input->post('latitude', true),
                    'longitude' => $this->input->post('longitude', true)
                );

                $com_data = array();
                if (isset($_POST['commission_data']) && !empty($_POST['commission_data'])) {
                    $commission_data = json_decode($this->input->post('commission_data'), true);
                    if (is_array($commission_data['category_id'])) {
                        if (count($commission_data['category_id']) >= 2) {
                            $cat_array = array_unique($commission_data['category_id']);
                            foreach ($commission_data['commission'] as $key => $val) {
                                if (!array_key_exists($key, $cat_array)) unset($commission_data['commission'][$key]);
                            }
                            $cat_array = array_values($cat_array);
                            $com_array = array_values($commission_data['commission']);

                            for ($i = 0; $i < count($cat_array); $i++) {
                                $tmp['seller_id'] = $this->input->post('edit_seller', true);
                                $tmp['category_id'] = $cat_array[$i];
                                $tmp['commission'] = $com_array[$i];
                                $com_data[] = $tmp;
                            }
                        } else {
                            $com_data[0] = array(
                                "seller_id" => $this->input->post('edit_seller', true),
                                "category_id" => $commission_data['category_id'],
                                "commission" => $commission_data['commission'],
                            );
                        }
                    } else {
                        $com_data[0] = array(
                            "seller_id" => $this->input->post('edit_seller', true),
                            "category_id" => $commission_data['category_id'],
                            "commission" => $commission_data['commission'],
                        );
                    }
                }

                if ($this->Seller_model->add_seller($seller_data, $seller_profile, $com_data)) {
                    $this->response['error'] = false;
                    $this->response['csrfName'] = $this->security->get_csrf_token_name();
                    $this->response['csrfHash'] = $this->security->get_csrf_hash();
                    $message = 'Seller Update Successfully';
                    $this->response['message'] = $message;
                    print_r(json_encode($this->response));
                } else {
                    $this->response['error'] = true;
                    $this->response['csrfName'] = $this->security->get_csrf_token_name();
                    $this->response['csrfHash'] = $this->security->get_csrf_hash();
                    $this->response['message'] = "Seller data was not updated";
                    print_r(json_encode($this->response));
                }
            } else {

                if (!$this->form_validation->is_unique($_POST['mobile'], 'users.mobile') || !$this->form_validation->is_unique($_POST['email'], 'users.email')) {
                    $response["error"] = true;
                    $response["message"] = "Email or mobile already exists !";
                    $response['csrfName'] = $this->security->get_csrf_token_name();
                    $response['csrfHash'] = $this->security->get_csrf_hash();
                    $response["data"] = array();
                    echo json_encode($response);
                    return false;
                }

                $identity_column = $this->config->item('identity', 'ion_auth');
                $email = strtolower($this->input->post('email'));
                $mobile = $this->input->post('mobile');
                $identity = ($identity_column == 'mobile') ? $mobile : $email;
                $password = $this->input->post('password');

                if (isset($_POST['serviceable_zipcodes']) && !empty($_POST['serviceable_zipcodes'])) {
                    $serviceable_zipcodes = implode(",", $this->input->post('serviceable_zipcodes', true));
                } else {
                    $serviceable_zipcodes = NULL;
                }
                if (isset($_POST['serviceable_cities']) && !empty($_POST['serviceable_cities'])) {
                    $serviceable_cities = implode(",", $this->input->post('serviceable_cities', true));
                } else {
                    $serviceable_cities = NULL;
                }

                if (isset($_POST['seo_meta_keywords']) && $_POST['seo_meta_keywords'] != '') {
                    $_POST['seo_meta_keywords'] = json_decode($_POST['seo_meta_keywords'], 1);
                    $seo_meta_keywords = array_column($_POST['seo_meta_keywords'], 'value');
                    $_POST['seo_meta_keywords'] = implode(",", $seo_meta_keywords);
                }

                $additional_data = [
                    'username' => $this->input->post('name', true),
                    'address' => $this->input->post('address', true),
                    'latitude' => $this->input->post('latitude', true),
                    'longitude' => $this->input->post('longitude', true),
                    'type' => 'phone',
                ];

                $this->ion_auth->register($identity, $password, $email, $additional_data, ['4']);
                if (update_details(['active' => 1], [$identity_column => $identity], 'users')) {
                    $user_id = fetch_details('users', ['mobile' => $mobile], 'id');
                    $com_data = array();
                    if (isset($_POST['commission_data']) && !empty($_POST['commission_data'])) {

                        $commission_data = json_decode($this->input->post('commission_data'), true);

                        if (is_array($commission_data['category_id'])) {
                            if (count($commission_data['category_id']) >= 2) {
                                $cat_array = array_unique($commission_data['category_id']);
                                foreach ($commission_data['commission'] as $key => $val) {
                                    if (!array_key_exists($key, $cat_array)) unset($commission_data['commission'][$key]);
                                }
                                $cat_array = array_values($cat_array);
                                $com_array = array_values($commission_data['commission']);

                                for ($i = 0; $i < count($cat_array); $i++) {
                                    $tmp['seller_id'] = $user_id[0]['id'];
                                    $tmp['category_id'] = $cat_array[$i];
                                    $tmp['commission'] = $com_array[$i];
                                    $com_data[] = $tmp;
                                }
                            } else {
                                $com_data[0] = array(
                                    "seller_id" => $user_id[0]['id'],
                                    "category_id" => $commission_data['category_id'],
                                    "commission" => $commission_data['commission'],
                                );
                            }
                        } else {
                            $com_data[0] = array(
                                "seller_id" => $user_id[0]['id'],
                                "category_id" => $commission_data['category_id'],
                                "commission" => $commission_data['commission'],
                            );
                        }
                    } else {
                        $category_ids = fetch_details('categories', null, 'id');
                        $categories = implode(",", array_column($category_ids, "id"));
                    }

                    $data = array(
                        'user_id' => $user_id[0]['id'],
                        'address_proof' => (!empty($proof_doc)) ? $proof_doc : null,
                        'store_logo' => (!empty($store_logo_doc)) ? $store_logo_doc : null,
                        'authorized_signature' => (!empty($authorized_signature_doc)) ? $authorized_signature_doc : null,
                        'status' => $this->input->post('status', true),
                        'tax_number' => $this->input->post('tax_number', true),
                        'tax_name' => $this->input->post('tax_name', true),
                        'store_description' => $this->input->post('store_description', true),
                        'store_url' => $this->input->post('store_url', true),
                        'store_name' => $this->input->post('store_name', true),
                        'global_commission' => (isset($_POST['global_commission']) && !empty($_POST['global_commission'])) ? $this->input->post('global_commission', true) : 0,
                        'categories' => $categories,
                        'permissions' => $permmissions,
                        'categories' => $categories,
                        'deliverable_zipcode_type' => $this->input->post('deliverable_zipcode_type', true),
                        'deliverable_city_type' => $this->input->post('deliverable_city_type', true),
                        'serviceable_zipcodes' => $serviceable_zipcodes,
                        'serviceable_cities' => $serviceable_cities,
                        'seo_page_title' => $this->input->post('seo_page_title', true),
                        'seo_meta_keywords' => $this->input->post('seo_meta_keywords', true),
                        'seo_meta_description' => $this->input->post('seo_meta_description', true),
                        'seo_og_image' => $this->input->post('seo_og_image', true),
                        'slug' => create_unique_slug($this->input->post('store_name', true), 'seller_data')
                    );
                    $insert_id = $this->Seller_model->add_seller($data, [], $com_data);
                    if (!empty($insert_id)) {
                        $this->response['error'] = false;
                        $this->response['csrfName'] = $this->security->get_csrf_token_name();
                        $this->response['csrfHash'] = $this->security->get_csrf_hash();
                        $this->response['message'] = 'Seller Added Successfully';
                        print_r(json_encode($this->response));
                    } else {
                        $this->response['error'] = true;
                        $this->response['csrfName'] = $this->security->get_csrf_token_name();
                        $this->response['csrfHash'] = $this->security->get_csrf_hash();
                        $this->response['message'] = "Seller data was not added";
                        print_r(json_encode($this->response));
                    }
                } else {
                    $this->response['error'] = true;
                    $this->response['csrfName'] = $this->security->get_csrf_token_name();
                    $this->response['csrfHash'] = $this->security->get_csrf_hash();
                    $message = (isset($_POST['edit_seller'])) ? 'Seller not Updated' : 'Seller not Added.';
                    $this->response['message'] = $message;
                    print_r(json_encode($this->response));
                }
            }
        } else {
            redirect('admin/login', 'refresh');
        }
    }

    public function get_seller_commission_data()
    {
        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin()) {
            $result = array();
            if (isset($_POST['id']) && !empty($_POST['id'])) {
                $id = $this->input->post('id', true);
                $result = $this->Seller_model->get_seller_commission_data($id);
                if (empty($result)) {
                    $result = $this->category_model->get_categories();
                }
            } else {
                $result = fetch_details('categories', "", 'id,name');
            }
            if (empty($result)) {
                $this->response['error'] = true;
                $this->response['message'] = "No category & commission data found for seller.";
                $this->response['data'] = [];
                $this->response['csrfName'] = $this->security->get_csrf_token_name();
                $this->response['csrfHash'] = $this->security->get_csrf_hash();
                print_r(json_encode($this->response));
                return false;
            } else {
                $this->response['error'] = false;
                $this->response['data'] = $result;
                $this->response['csrfName'] = $this->security->get_csrf_token_name();
                $this->response['csrfHash'] = $this->security->get_csrf_hash();
                print_r(json_encode($this->response));
                return false;
            }
        } else {
            $this->response['error'] = true;
            $this->response['message'] = 'Unauthorized access is not allowed';
            $this->response['data'] = [];
            $this->response['csrfName'] = $this->security->get_csrf_token_name();
            $this->response['csrfHash'] = $this->security->get_csrf_hash();
            print_r(json_encode($this->response));
            return false;
        }
    }

    public function create_slug()
    {
        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin()) {
            $tmpRow = $update_batch = array();
            $sellers = fetch_details('seller_data', 'slug IS NULL', 'id,store_name');
            if (!empty($sellers)) {
                foreach ($sellers as $row) {
                    $tmpRow['id'] = $row['id'];
                    $tmpRow['slug'] = create_unique_slug($row['store_name'], 'seller_data');
                    $this->Seller_model->create_slug($tmpRow);
                }
                $this->response['error'] = false;
                $this->response['message'] = "Slug Created Successfully.";
                $this->response['data'] = [];
                $this->response['csrfName'] = $this->security->get_csrf_token_name();
                $this->response['csrfHash'] = $this->security->get_csrf_hash();
                print_r(json_encode($this->response));
                return false;
            } else {
                $this->response['error'] = true;
                $this->response['message'] = 'Already Created No need to create again.';
                $this->response['data'] = [];
                $this->response['csrfName'] = $this->security->get_csrf_token_name();
                $this->response['csrfHash'] = $this->security->get_csrf_hash();
                print_r(json_encode($this->response));
                return false;
            }
        } else {
            redirect('admin/login', 'refresh');
        }
    }

    public function top_seller()
    {
        $this->Seller_model->top_sellers();
    }

    public function approved_sellers()
    {
        $this->Seller_model->approved_sellers();
    }

    public function not_approved_sellers()
    {
        $this->Seller_model->not_approved_sellers();
    }

    public function deactive_sellers()
    {
        $this->Seller_model->deactive_sellers();
    }
}
