<?php
defined('BASEPATH') or exit('No direct script access allowed');


class About_us extends CI_Controller
{

    public function __construct()
    {
        parent::__construct();
        $this->load->database();
        $this->load->helper(['url', 'language', 'timezone_helper']);
        $this->load->model('Setting_model');

        if (!has_permissions('read', 'about_us')) {
            $this->session->set_flashdata('authorize_flag', PERMISSION_ERROR_MSG);
            redirect('admin/home', 'refresh');
        }
    }

    public function index()
    {
        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin()) {
            $this->data['main_page'] = FORMS . 'about-us';
            $settings = get_settings('system_settings', true);
            $this->data['title'] = 'About Us | ' . $settings['app_name'];
            $this->data['meta_description'] = 'About Us | ' . $settings['app_name'];
            $this->data['about_us'] = get_settings('about_us');
            $this->load->view('admin/template', $this->data);
        } else {
            redirect('admin/login', 'refresh');
        }
    }

    public function update_about_us_settings()
    {

        if ($this->ion_auth->logged_in() && $this->ion_auth->is_admin()) {
            if (print_msg(!has_permissions('update', 'about_us'), PERMISSION_ERROR_MSG, 'about_us')) {
                return false;
            }
            $this->form_validation->set_rules('about_us_input_description', 'About Us', 'trim|required|xss_clean');
            if (!$this->form_validation->run()) {
                $this->response['error'] = true;
                $this->response['csrfName'] = $this->security->get_csrf_token_name();
                $this->response['csrfHash'] = $this->security->get_csrf_hash();
                $this->response['message'] = validation_errors();
                print_r(json_encode($this->response));
            } else {
                $settings['about_us_input_description'] = $this->input->post('about_us_input_description', true);
                $this->Setting_model->update_about_us($settings);
                $this->response['error'] = false;
                $this->response['csrfName'] = $this->security->get_csrf_token_name();
                $this->response['csrfHash'] = $this->security->get_csrf_hash();
                $this->response['message'] = 'System Setting Updated Successfully';
                print_r(json_encode($this->response));
            }
        } else {
            redirect('admin/login', 'refresh');
        }
    }

    public function about_us_page()
    {
        $settings = get_settings('system_settings', true);
        $this->data['title'] = 'About Us | ' . $settings['app_name'];
        $this->data['meta_description'] = 'About Us | ' . $settings['app_name'];
        $this->data['about_us'] = get_settings('about_us');
        $this->load->view('admin/pages/view/about-us', $this->data);
    }
}
