!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.moment=e()}(this,function(){"use strict";var t;function m(){return t.apply(null,arguments)}function r(t){return t instanceof Array||"[object Array]"===Object.prototype.toString.call(t)}function o(t){return null!=t&&"[object Object]"===Object.prototype.toString.call(t)}function n(t){return void 0===t}function h(t){return"number"==typeof t||"[object Number]"===Object.prototype.toString.call(t)}function l(t){return t instanceof Date||"[object Date]"===Object.prototype.toString.call(t)}function d(t,e){for(var s=[],i=0;i<t.length;++i)s.push(e(t[i],i));return s}function f(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function c(t,e){for(var s in e)f(e,s)&&(t[s]=e[s]);return f(e,"toString")&&(t.toString=e.toString),f(e,"valueOf")&&(t.valueOf=e.valueOf),t}function u(t,e,s,i){return Se(t,e,s,i,!0).utc()}function p(t){return null==t._pf&&(t._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],meridiem:null,rfc2822:!1,weekdayMismatch:!1}),t._pf}function y(t){if(null==t._isValid){var e=p(t),s=i.call(e.parsedDateParts,function(t){return null!=t}),s=!isNaN(t._d.getTime())&&e.overflow<0&&!e.empty&&!e.invalidMonth&&!e.invalidWeekday&&!e.weekdayMismatch&&!e.nullInput&&!e.invalidFormat&&!e.userInvalidated&&(!e.meridiem||e.meridiem&&s);if(t._strict&&(s=s&&0===e.charsLeftOver&&0===e.unusedTokens.length&&void 0===e.bigHour),null!=Object.isFrozen&&Object.isFrozen(t))return s;t._isValid=s}return t._isValid}function g(t){var e=u(NaN);return null!=t?c(p(e),t):p(e).userInvalidated=!0,e}var i=Array.prototype.some||function(t){for(var e=Object(this),s=e.length>>>0,i=0;i<s;i++)if(i in e&&t.call(this,e[i],i,e))return!0;return!1},D=m.momentProperties=[];function _(t,e){var s,i,a;if(n(e._isAMomentObject)||(t._isAMomentObject=e._isAMomentObject),n(e._i)||(t._i=e._i),n(e._f)||(t._f=e._f),n(e._l)||(t._l=e._l),n(e._strict)||(t._strict=e._strict),n(e._tzm)||(t._tzm=e._tzm),n(e._isUTC)||(t._isUTC=e._isUTC),n(e._offset)||(t._offset=e._offset),n(e._pf)||(t._pf=p(e)),n(e._locale)||(t._locale=e._locale),0<D.length)for(s=0;s<D.length;s++)n(a=e[i=D[s]])||(t[i]=a);return t}var e=!1;function k(t){_(this,t),this._d=new Date(null!=t._d?t._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===e&&(e=!0,m.updateOffset(this),e=!1)}function w(t){return t instanceof k||null!=t&&null!=t._isAMomentObject}function v(t){return t<0?Math.ceil(t)||0:Math.floor(t)}function Y(t){var e=+t,t=0;return t=0!=e&&isFinite(e)?v(e):t}function M(t,e,s){for(var i=Math.min(t.length,e.length),a=Math.abs(t.length-e.length),n=0,r=0;r<i;r++)(s&&t[r]!==e[r]||!s&&Y(t[r])!==Y(e[r]))&&n++;return n+a}function S(t){!1===m.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+t)}function s(a,n){var r=!0;return c(function(){if(null!=m.deprecationHandler&&m.deprecationHandler(null,a),r){for(var t,e=[],s=0;s<arguments.length;s++){if(t="","object"==typeof arguments[s]){for(var i in t+="\n["+s+"] ",arguments[0])t+=i+": "+arguments[0][i]+", ";t=t.slice(0,-2)}else t=arguments[s];e.push(t)}S(a+"\nArguments: "+Array.prototype.slice.call(e).join("")+"\n"+(new Error).stack),r=!1}return n.apply(this,arguments)},n)}var a={};function b(t,e){null!=m.deprecationHandler&&m.deprecationHandler(t,e),a[t]||(S(e),a[t]=!0)}function C(t){return t instanceof Function||"[object Function]"===Object.prototype.toString.call(t)}function x(t,e){var s,i=c({},t);for(s in e)f(e,s)&&(o(t[s])&&o(e[s])?(i[s]={},c(i[s],t[s]),c(i[s],e[s])):null!=e[s]?i[s]=e[s]:delete i[s]);for(s in t)f(t,s)&&!f(e,s)&&o(t[s])&&(i[s]=c({},i[s]));return i}function P(t){null!=t&&this.set(t)}m.suppressDeprecationWarnings=!1,m.deprecationHandler=null;var O=Object.keys||function(t){var e,s=[];for(e in t)f(t,e)&&s.push(e);return s},T={};function W(t,e){var s=t.toLowerCase();T[s]=T[s+"s"]=T[e]=t}function L(t){return"string"==typeof t?T[t]||T[t.toLowerCase()]:void 0}function I(t){var e,s,i={};for(s in t)f(t,s)&&(e=L(s))&&(i[e]=t[s]);return i}var N={};function H(t,e){N[t]=e}function R(t,e,s){var i=""+Math.abs(t);return(0<=t?s?"+":"":"-")+Math.pow(10,Math.max(0,e-i.length)).toString().substr(1)+i}var A=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,E=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,U={},F={};function V(t,e,s,i){var a="string"==typeof i?function(){return this[i]()}:i;t&&(F[t]=a),e&&(F[e[0]]=function(){return R(a.apply(this,arguments),e[1],e[2])}),s&&(F[s]=function(){return this.localeData().ordinal(a.apply(this,arguments),t)})}function j(t,e){return t.isValid()?(e=G(e,t.localeData()),U[e]=U[e]||function(i){for(var t,a=i.match(A),e=0,n=a.length;e<n;e++)F[a[e]]?a[e]=F[a[e]]:a[e]=(t=a[e]).match(/\[[\s\S]/)?t.replace(/^\[|\]$/g,""):t.replace(/\\/g,"");return function(t){for(var e="",s=0;s<n;s++)e+=C(a[s])?a[s].call(t,i):a[s];return e}}(e),U[e](t)):t.localeData().invalidDate()}function G(t,e){var s=5;function i(t){return e.longDateFormat(t)||t}for(E.lastIndex=0;0<=s&&E.test(t);)t=t.replace(E,i),E.lastIndex=0,--s;return t}var B=/\d/,z=/\d\d/,Z=/\d{3}/,q=/\d{4}/,$=/[+-]?\d{6}/,J=/\d\d?/,Q=/\d\d\d\d?/,X=/\d\d\d\d\d\d?/,K=/\d{1,3}/,tt=/\d{1,4}/,et=/[+-]?\d{1,6}/,st=/\d+/,it=/[+-]?\d+/,at=/Z|[+-]\d\d:?\d\d/gi,nt=/Z|[+-]\d\d(?::?\d\d)?/gi,rt=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i,ot={};function ht(t,s,i){ot[t]=C(s)?s:function(t,e){return t&&i?i:s}}function lt(t){return t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}var dt={};function ct(t,s){var e,i=s;for("string"==typeof t&&(t=[t]),h(s)&&(i=function(t,e){e[s]=Y(t)}),e=0;e<t.length;e++)dt[t[e]]=i}function ut(t,a){ct(t,function(t,e,s,i){s._w=s._w||{},a(t,s._w,s,i)})}var ft=0,mt=1,pt=2,yt=3,gt=4,Dt=5,_t=6,kt=7,wt=8;function vt(t){return Yt(t)?366:365}function Yt(t){return t%4==0&&t%100!=0||t%400==0}V("Y",0,0,function(){var t=this.year();return t<=9999?""+t:"+"+t}),V(0,["YY",2],0,function(){return this.year()%100}),V(0,["YYYY",4],0,"year"),V(0,["YYYYY",5],0,"year"),V(0,["YYYYYY",6,!0],0,"year"),W("year","y"),H("year",1),ht("Y",it),ht("YY",J,z),ht("YYYY",tt,q),ht("YYYYY",et,$),ht("YYYYYY",et,$),ct(["YYYYY","YYYYYY"],ft),ct("YYYY",function(t,e){e[ft]=2===t.length?m.parseTwoDigitYear(t):Y(t)}),ct("YY",function(t,e){e[ft]=m.parseTwoDigitYear(t)}),ct("Y",function(t,e){e[ft]=parseInt(t,10)}),m.parseTwoDigitYear=function(t){return Y(t)+(68<Y(t)?1900:2e3)};var Mt,St=bt("FullYear",!0);function bt(e,s){return function(t){return null!=t?(xt(this,e,t),m.updateOffset(this,s),this):Ct(this,e)}}function Ct(t,e){return t.isValid()?t._d["get"+(t._isUTC?"UTC":"")+e]():NaN}function xt(t,e,s){t.isValid()&&!isNaN(s)&&("FullYear"===e&&Yt(t.year())&&1===t.month()&&29===t.date()?t._d["set"+(t._isUTC?"UTC":"")+e](s,t.month(),Pt(s,t.month())):t._d["set"+(t._isUTC?"UTC":"")+e](s))}function Pt(t,e){if(isNaN(t)||isNaN(e))return NaN;var s=(e%12+12)%12;return t+=(e-s)/12,1==s?Yt(t)?29:28:31-s%7%2}Mt=Array.prototype.indexOf||function(t){for(var e=0;e<this.length;++e)if(this[e]===t)return e;return-1},V("M",["MM",2],"Mo",function(){return this.month()+1}),V("MMM",0,0,function(t){return this.localeData().monthsShort(this,t)}),V("MMMM",0,0,function(t){return this.localeData().months(this,t)}),W("month","M"),H("month",8),ht("M",J),ht("MM",J,z),ht("MMM",function(t,e){return e.monthsShortRegex(t)}),ht("MMMM",function(t,e){return e.monthsRegex(t)}),ct(["M","MM"],function(t,e){e[mt]=Y(t)-1}),ct(["MMM","MMMM"],function(t,e,s,i){i=s._locale.monthsParse(t,i,s._strict);null!=i?e[mt]=i:p(s).invalidMonth=t});var Ot=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Tt="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),Wt="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_");function Lt(t,e){var s;if(!t.isValid())return t;if("string"==typeof e)if(/^\d+$/.test(e))e=Y(e);else if(!h(e=t.localeData().monthsParse(e)))return t;return s=Math.min(t.date(),Pt(t.year(),e)),t._d["set"+(t._isUTC?"UTC":"")+"Month"](e,s),t}function It(t){return null!=t?(Lt(this,t),m.updateOffset(this,!0),this):Ct(this,"Month")}var Nt=rt,Ht=rt;function Rt(){function t(t,e){return e.length-t.length}for(var e,s=[],i=[],a=[],n=0;n<12;n++)e=u([2e3,n]),s.push(this.monthsShort(e,"")),i.push(this.months(e,"")),a.push(this.months(e,"")),a.push(this.monthsShort(e,""));for(s.sort(t),i.sort(t),a.sort(t),n=0;n<12;n++)s[n]=lt(s[n]),i[n]=lt(i[n]);for(n=0;n<24;n++)a[n]=lt(a[n]);this._monthsRegex=new RegExp("^("+a.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+i.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+s.join("|")+")","i")}function At(t){var e;return t<100&&0<=t?((e=Array.prototype.slice.call(arguments))[0]=t+400,e=new Date(Date.UTC.apply(null,e)),isFinite(e.getUTCFullYear())&&e.setUTCFullYear(t)):e=new Date(Date.UTC.apply(null,arguments)),e}function Et(t,e,s){s=7+e-s;return-(7+At(t,0,s).getUTCDay()-e)%7+s-1}function Ut(t,e,s,i,a){var n,a=1+7*(e-1)+(7+s-i)%7+Et(t,i,a),a=a<=0?vt(n=t-1)+a:a>vt(t)?(n=t+1,a-vt(t)):(n=t,a);return{year:n,dayOfYear:a}}function Ft(t,e,s){var i,a,n=Et(t.year(),e,s),n=Math.floor((t.dayOfYear()-n-1)/7)+1;return n<1?i=n+Vt(a=t.year()-1,e,s):n>Vt(t.year(),e,s)?(i=n-Vt(t.year(),e,s),a=t.year()+1):(a=t.year(),i=n),{week:i,year:a}}function Vt(t,e,s){var i=Et(t,e,s),s=Et(t+1,e,s);return(vt(t)-i+s)/7}function jt(t,e){return t.slice(e,7).concat(t.slice(0,e))}V("w",["ww",2],"wo","week"),V("W",["WW",2],"Wo","isoWeek"),W("week","w"),W("isoWeek","W"),H("week",5),H("isoWeek",5),ht("w",J),ht("ww",J,z),ht("W",J),ht("WW",J,z),ut(["w","ww","W","WW"],function(t,e,s,i){e[i.substr(0,1)]=Y(t)}),V("d",0,"do","day"),V("dd",0,0,function(t){return this.localeData().weekdaysMin(this,t)}),V("ddd",0,0,function(t){return this.localeData().weekdaysShort(this,t)}),V("dddd",0,0,function(t){return this.localeData().weekdays(this,t)}),V("e",0,0,"weekday"),V("E",0,0,"isoWeekday"),W("day","d"),W("weekday","e"),W("isoWeekday","E"),H("day",11),H("weekday",11),H("isoWeekday",11),ht("d",J),ht("e",J),ht("E",J),ht("dd",function(t,e){return e.weekdaysMinRegex(t)}),ht("ddd",function(t,e){return e.weekdaysShortRegex(t)}),ht("dddd",function(t,e){return e.weekdaysRegex(t)}),ut(["dd","ddd","dddd"],function(t,e,s,i){i=s._locale.weekdaysParse(t,i,s._strict);null!=i?e.d=i:p(s).invalidWeekday=t}),ut(["d","e","E"],function(t,e,s,i){e[i]=Y(t)});var Gt="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Bt="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),zt="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),Zt=rt,qt=rt,$t=rt;function Jt(){function t(t,e){return e.length-t.length}for(var e,s,i,a=[],n=[],r=[],o=[],h=0;h<7;h++)i=u([2e3,1]).day(h),e=this.weekdaysMin(i,""),s=this.weekdaysShort(i,""),i=this.weekdays(i,""),a.push(e),n.push(s),r.push(i),o.push(e),o.push(s),o.push(i);for(a.sort(t),n.sort(t),r.sort(t),o.sort(t),h=0;h<7;h++)n[h]=lt(n[h]),r[h]=lt(r[h]),o[h]=lt(o[h]);this._weekdaysRegex=new RegExp("^("+o.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+r.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+n.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+a.join("|")+")","i")}function Qt(){return this.hours()%12||12}function Xt(t,e){V(t,0,0,function(){return this.localeData().meridiem(this.hours(),this.minutes(),e)})}function Kt(t,e){return e._meridiemParse}V("H",["HH",2],0,"hour"),V("h",["hh",2],0,Qt),V("k",["kk",2],0,function(){return this.hours()||24}),V("hmm",0,0,function(){return""+Qt.apply(this)+R(this.minutes(),2)}),V("hmmss",0,0,function(){return""+Qt.apply(this)+R(this.minutes(),2)+R(this.seconds(),2)}),V("Hmm",0,0,function(){return""+this.hours()+R(this.minutes(),2)}),V("Hmmss",0,0,function(){return""+this.hours()+R(this.minutes(),2)+R(this.seconds(),2)}),Xt("a",!0),Xt("A",!1),W("hour","h"),H("hour",13),ht("a",Kt),ht("A",Kt),ht("H",J),ht("h",J),ht("k",J),ht("HH",J,z),ht("hh",J,z),ht("kk",J,z),ht("hmm",Q),ht("hmmss",X),ht("Hmm",Q),ht("Hmmss",X),ct(["H","HH"],yt),ct(["k","kk"],function(t,e,s){t=Y(t);e[yt]=24===t?0:t}),ct(["a","A"],function(t,e,s){s._isPm=s._locale.isPM(t),s._meridiem=t}),ct(["h","hh"],function(t,e,s){e[yt]=Y(t),p(s).bigHour=!0}),ct("hmm",function(t,e,s){var i=t.length-2;e[yt]=Y(t.substr(0,i)),e[gt]=Y(t.substr(i)),p(s).bigHour=!0}),ct("hmmss",function(t,e,s){var i=t.length-4,a=t.length-2;e[yt]=Y(t.substr(0,i)),e[gt]=Y(t.substr(i,2)),e[Dt]=Y(t.substr(a)),p(s).bigHour=!0}),ct("Hmm",function(t,e,s){var i=t.length-2;e[yt]=Y(t.substr(0,i)),e[gt]=Y(t.substr(i))}),ct("Hmmss",function(t,e,s){var i=t.length-4,a=t.length-2;e[yt]=Y(t.substr(0,i)),e[gt]=Y(t.substr(i,2)),e[Dt]=Y(t.substr(a))});var te,ee=bt("Hours",!0),se={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:Tt,monthsShort:Wt,week:{dow:0,doy:6},weekdays:Gt,weekdaysMin:zt,weekdaysShort:Bt,meridiemParse:/[ap]\.?m?\.?/i},ie={},ae={};function ne(t){return t&&t.toLowerCase().replace("_","-")}function re(t){var e;if(!ie[t]&&"undefined"!=typeof module&&module&&module.exports)try{e=te._abbr,require("./locale/"+t),oe(e)}catch(t){}return ie[t]}function oe(t,e){return t&&((e=n(e)?le(t):he(t,e))?te=e:"undefined"!=typeof console&&console.warn&&console.warn("Locale "+t+" not found. Did you forget to load it?")),te._abbr}function he(t,e){if(null===e)return delete ie[t],null;var s,i=se;if(e.abbr=t,null!=ie[t])b("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),i=ie[t]._config;else if(null!=e.parentLocale)if(null!=ie[e.parentLocale])i=ie[e.parentLocale]._config;else{if(null==(s=re(e.parentLocale)))return ae[e.parentLocale]||(ae[e.parentLocale]=[]),ae[e.parentLocale].push({name:t,config:e}),null;i=s._config}return ie[t]=new P(x(i,e)),ae[t]&&ae[t].forEach(function(t){he(t.name,t.config)}),oe(t),ie[t]}function le(t){var e;if(!(t=t&&t._locale&&t._locale._abbr?t._locale._abbr:t))return te;if(!r(t)){if(e=re(t))return e;t=[t]}return function(t){for(var e,s,i,a,n=0;n<t.length;){for(e=(a=ne(t[n]).split("-")).length,s=(s=ne(t[n+1]))?s.split("-"):null;0<e;){if(i=re(a.slice(0,e).join("-")))return i;if(s&&s.length>=e&&M(a,s,!0)>=e-1)break;e--}n++}return te}(t)}function de(t){var e=t._a;return e&&-2===p(t).overflow&&(e=e[mt]<0||11<e[mt]?mt:e[pt]<1||e[pt]>Pt(e[ft],e[mt])?pt:e[yt]<0||24<e[yt]||24===e[yt]&&(0!==e[gt]||0!==e[Dt]||0!==e[_t])?yt:e[gt]<0||59<e[gt]?gt:e[Dt]<0||59<e[Dt]?Dt:e[_t]<0||999<e[_t]?_t:-1,p(t)._overflowDayOfYear&&(e<ft||pt<e)&&(e=pt),p(t)._overflowWeeks&&-1===e&&(e=kt),p(t)._overflowWeekday&&-1===e&&(e=wt),p(t).overflow=e),t}function ce(t,e,s){return null!=t?t:null!=e?e:s}function ue(t){var e,s,i,a,n,r,o,h,l,d=[];if(!t._d){var c=t,u=new Date(m.now()),f=c._useUTC?[u.getUTCFullYear(),u.getUTCMonth(),u.getUTCDate()]:[u.getFullYear(),u.getMonth(),u.getDate()];for(t._w&&null==t._a[pt]&&null==t._a[mt]&&(null!=(c=(i=t)._w).GG||null!=c.W||null!=c.E?(r=1,o=4,h=ce(c.GG,i._a[ft],Ft(be(),1,4).year),l=ce(c.W,1),((a=ce(c.E,1))<1||7<a)&&(n=!0)):(r=i._locale._week.dow,o=i._locale._week.doy,u=Ft(be(),r,o),h=ce(c.gg,i._a[ft],u.year),l=ce(c.w,u.week),null!=c.d?((a=c.d)<0||6<a)&&(n=!0):null!=c.e?(a=c.e+r,(c.e<0||6<c.e)&&(n=!0)):a=r),l<1||l>Vt(h,r,o)?p(i)._overflowWeeks=!0:null!=n?p(i)._overflowWeekday=!0:(o=Ut(h,l,a,r,o),i._a[ft]=o.year,i._dayOfYear=o.dayOfYear)),null!=t._dayOfYear&&(s=ce(t._a[ft],f[ft]),(t._dayOfYear>vt(s)||0===t._dayOfYear)&&(p(t)._overflowDayOfYear=!0),s=At(s,0,t._dayOfYear),t._a[mt]=s.getUTCMonth(),t._a[pt]=s.getUTCDate()),e=0;e<3&&null==t._a[e];++e)t._a[e]=d[e]=f[e];for(;e<7;e++)t._a[e]=d[e]=null==t._a[e]?2===e?1:0:t._a[e];24===t._a[yt]&&0===t._a[gt]&&0===t._a[Dt]&&0===t._a[_t]&&(t._nextDay=!0,t._a[yt]=0),t._d=(t._useUTC?At:function(t,e,s,i,a,n,r){var o;return t<100&&0<=t?(o=new Date(t+400,e,s,i,a,n,r),isFinite(o.getFullYear())&&o.setFullYear(t)):o=new Date(t,e,s,i,a,n,r),o}).apply(null,d),s=t._useUTC?t._d.getUTCDay():t._d.getDay(),null!=t._tzm&&t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),t._nextDay&&(t._a[yt]=24),t._w&&void 0!==t._w.d&&t._w.d!==s&&(p(t).weekdayMismatch=!0)}}var fe=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,me=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,pe=/Z|[+-]\d\d(?::?\d\d)?/,ye=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/]],ge=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],De=/^\/?Date\((\-?\d+)/i;function _e(t){var e,s,i,a,n,r,o=t._i,h=fe.exec(o)||me.exec(o);if(h){for(p(t).iso=!0,e=0,s=ye.length;e<s;e++)if(ye[e][1].exec(h[1])){a=ye[e][0],i=!1!==ye[e][2];break}if(null==a)return t._isValid=!1;if(h[3]){for(e=0,s=ge.length;e<s;e++)if(ge[e][1].exec(h[3])){n=(h[2]||" ")+ge[e][0];break}if(null==n)return t._isValid=!1}if(!i&&null!=n)return t._isValid=!1;if(h[4]){if(!pe.exec(h[4]))return t._isValid=!1;r="Z"}t._f=a+(n||"")+(r||""),Ye(t)}else t._isValid=!1}var ke=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/;var we={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function ve(t){var e,s,i,a,n,r,o=ke.exec(t._i.replace(/\([^)]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));o?(e=o[4],s=o[3],i=o[2],a=o[5],n=o[6],r=o[7],n=[(e=parseInt(e,10))<=49?2e3+e:e<=999?1900+e:e,Wt.indexOf(s),parseInt(i,10),parseInt(a,10),parseInt(n,10)],r&&n.push(parseInt(r,10)),a=i=n,r=t,(n=o[1])&&Bt.indexOf(n)!==new Date(a[0],a[1],a[2]).getDay()?(p(r).weekdayMismatch=!0,r._isValid=!1):(t._a=i,t._tzm=function(t,e,s){if(t)return we[t];if(e)return 0;e=parseInt(s,10),s=e%100;return(e-s)/100*60+s}(o[8],o[9],o[10]),t._d=At.apply(null,t._a),t._d.setUTCMinutes(t._d.getUTCMinutes()-t._tzm),p(t).rfc2822=!0)):t._isValid=!1}function Ye(t){if(t._f!==m.ISO_8601)if(t._f!==m.RFC_2822){t._a=[],p(t).empty=!0;for(var e,s,i,a=""+t._i,n=a.length,r=0,o=G(t._f,t._locale).match(A)||[],h=0;h<o.length;h++)s=o[h],(e=(a.match((u=t,f(ot,c=s)?ot[c](u._strict,u._locale):new RegExp(lt(c.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(t,e,s,i,a){return e||s||i||a})))))||[])[0])&&(0<(i=a.substr(0,a.indexOf(e))).length&&p(t).unusedInput.push(i),a=a.slice(a.indexOf(e)+e.length),r+=e.length),F[s]?(e?p(t).empty=!1:p(t).unusedTokens.push(s),u=s,c=t,null!=(i=e)&&f(dt,u)&&dt[u](i,c._a,c,u)):t._strict&&!e&&p(t).unusedTokens.push(s);p(t).charsLeftOver=n-r,0<a.length&&p(t).unusedInput.push(a),t._a[yt]<=12&&!0===p(t).bigHour&&0<t._a[yt]&&(p(t).bigHour=void 0),p(t).parsedDateParts=t._a.slice(0),p(t).meridiem=t._meridiem,t._a[yt]=(l=t._locale,d=t._a[yt],null==(n=t._meridiem)?d:null!=l.meridiemHour?l.meridiemHour(d,n):(null!=l.isPM&&((n=l.isPM(n))&&d<12&&(d+=12),n||12!==d||(d=0)),d)),ue(t),de(t)}else ve(t);else _e(t);var l,d,c,u}function Me(t){var e,s,i=t._i,a=t._f;return t._locale=t._locale||le(t._l),null===i||void 0===a&&""===i?g({nullInput:!0}):("string"==typeof i&&(t._i=i=t._locale.preparse(i)),w(i)?new k(de(i)):(l(i)?t._d=i:r(a)?function(t){var e,s,i,a,n;if(0===t._f.length)return p(t).invalidFormat=!0,t._d=new Date(NaN);for(a=0;a<t._f.length;a++)n=0,e=_({},t),null!=t._useUTC&&(e._useUTC=t._useUTC),e._f=t._f[a],Ye(e),y(e)&&(n+=p(e).charsLeftOver,n+=10*p(e).unusedTokens.length,p(e).score=n,(null==i||n<i)&&(i=n,s=e));c(t,s||e)}(t):a?Ye(t):n(a=(i=t)._i)?i._d=new Date(m.now()):l(a)?i._d=new Date(a.valueOf()):"string"==typeof a?(e=i,null===(s=De.exec(e._i))?(_e(e),!1===e._isValid&&(delete e._isValid,ve(e),!1===e._isValid&&(delete e._isValid,m.createFromInputFallback(e)))):e._d=new Date(+s[1])):r(a)?(i._a=d(a.slice(0),function(t){return parseInt(t,10)}),ue(i)):o(a)?(e=i)._d||(s=I(e._i),e._a=d([s.year,s.month,s.day||s.date,s.hour,s.minute,s.second,s.millisecond],function(t){return t&&parseInt(t,10)}),ue(e)):h(a)?i._d=new Date(a):m.createFromInputFallback(i),y(t)||(t._d=null),t))}function Se(t,e,s,i,a){var n={};return!0!==s&&!1!==s||(i=s,s=void 0),(o(t)&&function(t){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(t).length;for(var e in t)if(t.hasOwnProperty(e))return;return 1}(t)||r(t)&&0===t.length)&&(t=void 0),n._isAMomentObject=!0,n._useUTC=n._isUTC=a,n._l=s,n._i=t,n._f=e,n._strict=i,(n=new k(de(Me(n))))._nextDay&&(n.add(1,"d"),n._nextDay=void 0),n}function be(t,e,s,i){return Se(t,e,s,i,!1)}m.createFromInputFallback=s("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged and will be removed in an upcoming major release. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",function(t){t._d=new Date(t._i+(t._useUTC?" UTC":""))}),m.ISO_8601=function(){},m.RFC_2822=function(){};var Ce=s("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var t=be.apply(null,arguments);return this.isValid()&&t.isValid()?t<this?this:t:g()}),xe=s("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",function(){var t=be.apply(null,arguments);return this.isValid()&&t.isValid()?this<t?this:t:g()});function Pe(t,e){var s,i;if(!(e=1===e.length&&r(e[0])?e[0]:e).length)return be();for(s=e[0],i=1;i<e.length;++i)e[i].isValid()&&!e[i][t](s)||(s=e[i]);return s}var Oe=["year","quarter","month","week","day","hour","minute","second","millisecond"];function Te(t){var e=I(t),s=e.year||0,i=e.quarter||0,a=e.month||0,n=e.week||e.isoWeek||0,r=e.day||0,o=e.hour||0,h=e.minute||0,l=e.second||0,t=e.millisecond||0;this._isValid=function(t){for(var e in t)if(-1===Mt.call(Oe,e)||null!=t[e]&&isNaN(t[e]))return!1;for(var s=!1,i=0;i<Oe.length;++i)if(t[Oe[i]]){if(s)return!1;parseFloat(t[Oe[i]])!==Y(t[Oe[i]])&&(s=!0)}return!0}(e),this._milliseconds=+t+1e3*l+6e4*h+1e3*o*60*60,this._days=+r+7*n,this._months=+a+3*i+12*s,this._data={},this._locale=le(),this._bubble()}function We(t){return t instanceof Te}function Le(t){return t<0?-1*Math.round(-1*t):Math.round(t)}function Ie(t,s){V(t,0,0,function(){var t=this.utcOffset(),e="+";return t<0&&(t=-t,e="-"),e+R(~~(t/60),2)+s+R(~~t%60,2)})}Ie("Z",":"),Ie("ZZ",""),ht("Z",nt),ht("ZZ",nt),ct(["Z","ZZ"],function(t,e,s){s._useUTC=!0,s._tzm=He(nt,t)});var Ne=/([\+\-]|\d\d)/gi;function He(t,e){e=(e||"").match(t);if(null===e)return null;t=((e[e.length-1]||[])+"").match(Ne)||["-",0,0],e=60*t[1]+Y(t[2]);return 0===e?0:"+"===t[0]?e:-e}function Re(t,e){var s;return e._isUTC?(s=e.clone(),e=(w(t)||l(t)?t:be(t)).valueOf()-s.valueOf(),s._d.setTime(s._d.valueOf()+e),m.updateOffset(s,!1),s):be(t).local()}function Ae(t){return 15*-Math.round(t._d.getTimezoneOffset()/15)}function Ee(){return!!this.isValid()&&this._isUTC&&0===this._offset}m.updateOffset=function(){};var Ue=/^(\-|\+)?(?:(\d*)[. ])?(\d+)\:(\d+)(?:\:(\d+)(\.\d*)?)?$/,Fe=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function Ve(t,e){var s,i,a=t,n=null;return We(t)?a={ms:t._milliseconds,d:t._days,M:t._months}:h(t)?(a={},e?a[e]=t:a.milliseconds=t):(n=Ue.exec(t))?(s="-"===n[1]?-1:1,a={y:0,d:Y(n[pt])*s,h:Y(n[yt])*s,m:Y(n[gt])*s,s:Y(n[Dt])*s,ms:Y(Le(1e3*n[_t]))*s}):(n=Fe.exec(t))?(s="-"===n[1]?-1:1,a={y:je(n[2],s),M:je(n[3],s),w:je(n[4],s),d:je(n[5],s),h:je(n[6],s),m:je(n[7],s),s:je(n[8],s)}):null==a?a={}:"object"==typeof a&&("from"in a||"to"in a)&&(n=be(a.from),s=be(a.to),i=n.isValid()&&s.isValid()?(s=Re(s,n),n.isBefore(s)?i=Ge(n,s):((i=Ge(s,n)).milliseconds=-i.milliseconds,i.months=-i.months),i):{milliseconds:0,months:0},(a={}).ms=i.milliseconds,a.M=i.months),a=new Te(a),We(t)&&f(t,"_locale")&&(a._locale=t._locale),a}function je(t,e){t=t&&parseFloat(t.replace(",","."));return(isNaN(t)?0:t)*e}function Ge(t,e){var s={};return s.months=e.month()-t.month()+12*(e.year()-t.year()),t.clone().add(s.months,"M").isAfter(e)&&--s.months,s.milliseconds=+e-+t.clone().add(s.months,"M"),s}function Be(i,a){return function(t,e){var s;return null===e||isNaN(+e)||(b(a,"moment()."+a+"(period, number) is deprecated. Please use moment()."+a+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),s=t,t=e,e=s),ze(this,Ve(t="string"==typeof t?+t:t,e),i),this}}function ze(t,e,s,i){var a=e._milliseconds,n=Le(e._days),e=Le(e._months);t.isValid()&&(i=null==i||i,e&&Lt(t,Ct(t,"Month")+e*s),n&&xt(t,"Date",Ct(t,"Date")+n*s),a&&t._d.setTime(t._d.valueOf()+a*s),i&&m.updateOffset(t,n||e))}Ve.fn=Te.prototype,Ve.invalid=function(){return Ve(NaN)};var Ze=Be(1,"add"),rt=Be(-1,"subtract");function qe(t,e){var s=12*(e.year()-t.year())+(e.month()-t.month()),i=t.clone().add(s,"months");return-(s+(e-i<0?(e-i)/(i-t.clone().add(s-1,"months")):(e-i)/(t.clone().add(1+s,"months")-i)))||0}function $e(t){return void 0===t?this._locale._abbr:(null!=(t=le(t))&&(this._locale=t),this)}m.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",m.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";Q=s("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",function(t){return void 0===t?this.localeData():this.locale(t)});function Je(){return this._locale}var Qe=126227808e5;function Xe(t,e){return(t%e+e)%e}function Ke(t,e,s){return t<100&&0<=t?new Date(t+400,e,s)-Qe:new Date(t,e,s).valueOf()}function ts(t,e,s){return t<100&&0<=t?Date.UTC(t+400,e,s)-Qe:Date.UTC(t,e,s)}function es(t,e){V(0,[t,t.length],0,e)}function ss(t,e,s,i,a){var n;return null==t?Ft(this,i,a).year:((n=Vt(t,i,a))<e&&(e=n),function(t,e,s,i,a){a=Ut(t,e,s,i,a),a=At(a.year,0,a.dayOfYear);return this.year(a.getUTCFullYear()),this.month(a.getUTCMonth()),this.date(a.getUTCDate()),this}.call(this,t,e,s,i,a))}V(0,["gg",2],0,function(){return this.weekYear()%100}),V(0,["GG",2],0,function(){return this.isoWeekYear()%100}),es("gggg","weekYear"),es("ggggg","weekYear"),es("GGGG","isoWeekYear"),es("GGGGG","isoWeekYear"),W("weekYear","gg"),W("isoWeekYear","GG"),H("weekYear",1),H("isoWeekYear",1),ht("G",it),ht("g",it),ht("GG",J,z),ht("gg",J,z),ht("GGGG",tt,q),ht("gggg",tt,q),ht("GGGGG",et,$),ht("ggggg",et,$),ut(["gggg","ggggg","GGGG","GGGGG"],function(t,e,s,i){e[i.substr(0,2)]=Y(t)}),ut(["gg","GG"],function(t,e,s,i){e[i]=m.parseTwoDigitYear(t)}),V("Q",0,"Qo","quarter"),W("quarter","Q"),H("quarter",7),ht("Q",B),ct("Q",function(t,e){e[mt]=3*(Y(t)-1)}),V("D",["DD",2],"Do","date"),W("date","D"),H("date",9),ht("D",J),ht("DD",J,z),ht("Do",function(t,e){return t?e._dayOfMonthOrdinalParse||e._ordinalParse:e._dayOfMonthOrdinalParseLenient}),ct(["D","DD"],pt),ct("Do",function(t,e){e[pt]=Y(t.match(J)[0])});X=bt("Date",!0);V("DDD",["DDDD",3],"DDDo","dayOfYear"),W("dayOfYear","DDD"),H("dayOfYear",4),ht("DDD",K),ht("DDDD",Z),ct(["DDD","DDDD"],function(t,e,s){s._dayOfYear=Y(t)}),V("m",["mm",2],0,"minute"),W("minute","m"),H("minute",14),ht("m",J),ht("mm",J,z),ct(["m","mm"],gt);Tt=bt("Minutes",!1);V("s",["ss",2],0,"second"),W("second","s"),H("second",15),ht("s",J),ht("ss",J,z),ct(["s","ss"],Dt);var is,Gt=bt("Seconds",!1);for(V("S",0,0,function(){return~~(this.millisecond()/100)}),V(0,["SS",2],0,function(){return~~(this.millisecond()/10)}),V(0,["SSS",3],0,"millisecond"),V(0,["SSSS",4],0,function(){return 10*this.millisecond()}),V(0,["SSSSS",5],0,function(){return 100*this.millisecond()}),V(0,["SSSSSS",6],0,function(){return 1e3*this.millisecond()}),V(0,["SSSSSSS",7],0,function(){return 1e4*this.millisecond()}),V(0,["SSSSSSSS",8],0,function(){return 1e5*this.millisecond()}),V(0,["SSSSSSSSS",9],0,function(){return 1e6*this.millisecond()}),W("millisecond","ms"),H("millisecond",16),ht("S",K,B),ht("SS",K,z),ht("SSS",K,Z),is="SSSS";is.length<=9;is+="S")ht(is,st);function as(t,e){e[_t]=Y(1e3*("0."+t))}for(is="S";is.length<=9;is+="S")ct(is,as);zt=bt("Milliseconds",!1);V("z",0,0,"zoneAbbr"),V("zz",0,0,"zoneName");tt=k.prototype;function ns(t){return t}tt.add=Ze,tt.calendar=function(t,e){var s=t||be(),t=Re(s,this).startOf("day"),t=m.calendarFormat(this,t)||"sameElse",e=e&&(C(e[t])?e[t].call(this,s):e[t]);return this.format(e||this.localeData().calendar(t,this,be(s)))},tt.clone=function(){return new k(this)},tt.diff=function(t,e,s){var i,a,n;if(!this.isValid())return NaN;if(!(i=Re(t,this)).isValid())return NaN;switch(a=6e4*(i.utcOffset()-this.utcOffset()),e=L(e)){case"year":n=qe(this,i)/12;break;case"month":n=qe(this,i);break;case"quarter":n=qe(this,i)/3;break;case"second":n=(this-i)/1e3;break;case"minute":n=(this-i)/6e4;break;case"hour":n=(this-i)/36e5;break;case"day":n=(this-i-a)/864e5;break;case"week":n=(this-i-a)/6048e5;break;default:n=this-i}return s?n:v(n)},tt.endOf=function(t){var e;if(void 0===(t=L(t))||"millisecond"===t||!this.isValid())return this;var s=this._isUTC?ts:Ke;switch(t){case"year":e=s(this.year()+1,0,1)-1;break;case"quarter":e=s(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":e=s(this.year(),this.month()+1,1)-1;break;case"week":e=s(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":e=s(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":e=s(this.year(),this.month(),this.date()+1)-1;break;case"hour":e=this._d.valueOf(),e+=36e5-Xe(e+(this._isUTC?0:6e4*this.utcOffset()),36e5)-1;break;case"minute":e=this._d.valueOf(),e+=6e4-Xe(e,6e4)-1;break;case"second":e=this._d.valueOf(),e+=1e3-Xe(e,1e3)-1}return this._d.setTime(e),m.updateOffset(this,!0),this},tt.format=function(t){t=t||(this.isUtc()?m.defaultFormatUtc:m.defaultFormat);t=j(this,t);return this.localeData().postformat(t)},tt.from=function(t,e){return this.isValid()&&(w(t)&&t.isValid()||be(t).isValid())?Ve({to:this,from:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()},tt.fromNow=function(t){return this.from(be(),t)},tt.to=function(t,e){return this.isValid()&&(w(t)&&t.isValid()||be(t).isValid())?Ve({from:this,to:t}).locale(this.locale()).humanize(!e):this.localeData().invalidDate()},tt.toNow=function(t){return this.to(be(),t)},tt.get=function(t){return C(this[t=L(t)])?this[t]():this},tt.invalidAt=function(){return p(this).overflow},tt.isAfter=function(t,e){t=w(t)?t:be(t);return!(!this.isValid()||!t.isValid())&&("millisecond"===(e=L(e)||"millisecond")?this.valueOf()>t.valueOf():t.valueOf()<this.clone().startOf(e).valueOf())},tt.isBefore=function(t,e){t=w(t)?t:be(t);return!(!this.isValid()||!t.isValid())&&("millisecond"===(e=L(e)||"millisecond")?this.valueOf()<t.valueOf():this.clone().endOf(e).valueOf()<t.valueOf())},tt.isBetween=function(t,e,s,i){t=w(t)?t:be(t),e=w(e)?e:be(e);return!!(this.isValid()&&t.isValid()&&e.isValid())&&("("===(i=i||"()")[0]?this.isAfter(t,s):!this.isBefore(t,s))&&(")"===i[1]?this.isBefore(e,s):!this.isAfter(e,s))},tt.isSame=function(t,e){var t=w(t)?t:be(t);return!(!this.isValid()||!t.isValid())&&("millisecond"===(e=L(e)||"millisecond")?this.valueOf()===t.valueOf():(t=t.valueOf(),this.clone().startOf(e).valueOf()<=t&&t<=this.clone().endOf(e).valueOf()))},tt.isSameOrAfter=function(t,e){return this.isSame(t,e)||this.isAfter(t,e)},tt.isSameOrBefore=function(t,e){return this.isSame(t,e)||this.isBefore(t,e)},tt.isValid=function(){return y(this)},tt.lang=Q,tt.locale=$e,tt.localeData=Je,tt.max=xe,tt.min=Ce,tt.parsingFlags=function(){return c({},p(this))},tt.set=function(s,t){if("object"==typeof s)for(var e=function(){var t,e=[];for(t in s)e.push({unit:t,priority:N[t]});return e.sort(function(t,e){return t.priority-e.priority}),e}(s=I(s)),i=0;i<e.length;i++)this[e[i].unit](s[e[i].unit]);else if(C(this[s=L(s)]))return this[s](t);return this},tt.startOf=function(t){var e;if(void 0===(t=L(t))||"millisecond"===t||!this.isValid())return this;var s=this._isUTC?ts:Ke;switch(t){case"year":e=s(this.year(),0,1);break;case"quarter":e=s(this.year(),this.month()-this.month()%3,1);break;case"month":e=s(this.year(),this.month(),1);break;case"week":e=s(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":e=s(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":e=s(this.year(),this.month(),this.date());break;case"hour":e=this._d.valueOf(),e-=Xe(e+(this._isUTC?0:6e4*this.utcOffset()),36e5);break;case"minute":e=this._d.valueOf(),e-=Xe(e,6e4);break;case"second":e=this._d.valueOf(),e-=Xe(e,1e3)}return this._d.setTime(e),m.updateOffset(this,!0),this},tt.subtract=rt,tt.toArray=function(){var t=this;return[t.year(),t.month(),t.date(),t.hour(),t.minute(),t.second(),t.millisecond()]},tt.toObject=function(){var t=this;return{years:t.year(),months:t.month(),date:t.date(),hours:t.hours(),minutes:t.minutes(),seconds:t.seconds(),milliseconds:t.milliseconds()}},tt.toDate=function(){return new Date(this.valueOf())},tt.toISOString=function(t){if(!this.isValid())return null;var e=!0!==t,t=e?this.clone().utc():this;return t.year()<0||9999<t.year()?j(t,e?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):C(Date.prototype.toISOString)?e?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",j(t,"Z")):j(t,e?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")},tt.inspect=function(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var t="moment",e="";this.isLocal()||(t=0===this.utcOffset()?"moment.utc":"moment.parseZone",e="Z");var s="["+t+'("]',t=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY";return this.format(s+t+"-MM-DD[T]HH:mm:ss.SSS"+(e+'[")]'))},tt.toJSON=function(){return this.isValid()?this.toISOString():null},tt.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},tt.unix=function(){return Math.floor(this.valueOf()/1e3)},tt.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},tt.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},tt.year=St,tt.isLeapYear=function(){return Yt(this.year())},tt.weekYear=function(t){return ss.call(this,t,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)},tt.isoWeekYear=function(t){return ss.call(this,t,this.isoWeek(),this.isoWeekday(),1,4)},tt.quarter=tt.quarters=function(t){return null==t?Math.ceil((this.month()+1)/3):this.month(3*(t-1)+this.month()%3)},tt.month=It,tt.daysInMonth=function(){return Pt(this.year(),this.month())},tt.week=tt.weeks=function(t){var e=this.localeData().week(this);return null==t?e:this.add(7*(t-e),"d")},tt.isoWeek=tt.isoWeeks=function(t){var e=Ft(this,1,4).week;return null==t?e:this.add(7*(t-e),"d")},tt.weeksInYear=function(){var t=this.localeData()._week;return Vt(this.year(),t.dow,t.doy)},tt.isoWeeksInYear=function(){return Vt(this.year(),1,4)},tt.date=X,tt.day=tt.days=function(t){if(!this.isValid())return null!=t?this:NaN;var e,s,i=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=t?(e=t,s=this.localeData(),t="string"!=typeof e?e:isNaN(e)?"number"==typeof(e=s.weekdaysParse(e))?e:null:parseInt(e,10),this.add(t-i,"d")):i},tt.weekday=function(t){if(!this.isValid())return null!=t?this:NaN;var e=(this.day()+7-this.localeData()._week.dow)%7;return null==t?e:this.add(t-e,"d")},tt.isoWeekday=function(t){if(!this.isValid())return null!=t?this:NaN;if(null==t)return this.day()||7;var e=(e=t,t=this.localeData(),"string"==typeof e?t.weekdaysParse(e)%7||7:isNaN(e)?null:e);return this.day(this.day()%7?e:e-7)},tt.dayOfYear=function(t){var e=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==t?e:this.add(t-e,"d")},tt.hour=tt.hours=ee,tt.minute=tt.minutes=Tt,tt.second=tt.seconds=Gt,tt.millisecond=tt.milliseconds=zt,tt.utcOffset=function(t,e,s){var i,a=this._offset||0;if(!this.isValid())return null!=t?this:NaN;if(null==t)return this._isUTC?a:Ae(this);if("string"==typeof t){if(null===(t=He(nt,t)))return this}else Math.abs(t)<16&&!s&&(t*=60);return!this._isUTC&&e&&(i=Ae(this)),this._offset=t,this._isUTC=!0,null!=i&&this.add(i,"m"),a!==t&&(!e||this._changeInProgress?ze(this,Ve(t-a,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,m.updateOffset(this,!0),this._changeInProgress=null)),this},tt.utc=function(t){return this.utcOffset(0,t)},tt.local=function(t){return this._isUTC&&(this.utcOffset(0,t),this._isUTC=!1,t&&this.subtract(Ae(this),"m")),this},tt.parseZone=function(){var t;return null!=this._tzm?this.utcOffset(this._tzm,!1,!0):"string"==typeof this._i&&(null!=(t=He(at,this._i))?this.utcOffset(t):this.utcOffset(0,!0)),this},tt.hasAlignedHourOffset=function(t){return!!this.isValid()&&(t=t?be(t).utcOffset():0,(this.utcOffset()-t)%60==0)},tt.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},tt.isLocal=function(){return!!this.isValid()&&!this._isUTC},tt.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},tt.isUtc=Ee,tt.isUTC=Ee,tt.zoneAbbr=function(){return this._isUTC?"UTC":""},tt.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},tt.dates=s("dates accessor is deprecated. Use date instead.",X),tt.months=s("months accessor is deprecated. Use month instead",It),tt.years=s("years accessor is deprecated. Use year instead",St),tt.zone=s("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",function(t,e){return null!=t?(this.utcOffset(t="string"!=typeof t?-t:t,e),this):-this.utcOffset()}),tt.isDSTShifted=s("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",function(){if(!n(this._isDSTShifted))return this._isDSTShifted;var t,e={};return _(e,this),(e=Me(e))._a?(t=(e._isUTC?u:be)(e._a),this._isDSTShifted=this.isValid()&&0<M(e._a,t.toArray())):this._isDSTShifted=!1,this._isDSTShifted});q=P.prototype;function rs(t,e,s,i){var a=le(),e=u().set(i,e);return a[s](e,t)}function os(t,e,s){if(h(t)&&(e=t,t=void 0),t=t||"",null!=e)return rs(t,e,s,"month");for(var i=[],a=0;a<12;a++)i[a]=rs(t,a,s,"month");return i}function hs(t,e,s,i){"boolean"==typeof t?h(e)&&(s=e,e=void 0):(e=t,t=!1,h(s=e)&&(s=e,e=void 0)),e=e||"";var a=le(),n=t?a._week.dow:0;if(null!=s)return rs(e,(s+n)%7,i,"day");for(var r=[],o=0;o<7;o++)r[o]=rs(e,(o+n)%7,i,"day");return r}q.calendar=function(t,e,s){t=this._calendar[t]||this._calendar.sameElse;return C(t)?t.call(e,s):t},q.longDateFormat=function(t){var e=this._longDateFormat[t],s=this._longDateFormat[t.toUpperCase()];return e||!s?e:(this._longDateFormat[t]=s.replace(/MMMM|MM|DD|dddd/g,function(t){return t.slice(1)}),this._longDateFormat[t])},q.invalidDate=function(){return this._invalidDate},q.ordinal=function(t){return this._ordinal.replace("%d",t)},q.preparse=ns,q.postformat=ns,q.relativeTime=function(t,e,s,i){var a=this._relativeTime[s];return C(a)?a(t,e,s,i):a.replace(/%d/i,t)},q.pastFuture=function(t,e){t=this._relativeTime[0<t?"future":"past"];return C(t)?t(e):t.replace(/%s/i,e)},q.set=function(t){var e,s;for(s in t)C(e=t[s])?this[s]=e:this["_"+s]=e;this._config=t,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},q.months=function(t,e){return t?(r(this._months)?this._months:this._months[(this._months.isFormat||Ot).test(e)?"format":"standalone"])[t.month()]:r(this._months)?this._months:this._months.standalone},q.monthsShort=function(t,e){return t?(r(this._monthsShort)?this._monthsShort:this._monthsShort[Ot.test(e)?"format":"standalone"])[t.month()]:r(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},q.monthsParse=function(t,e,s){var i,a;if(this._monthsParseExact)return function(t,e,s){var i,a,n,t=t.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],i=0;i<12;++i)n=u([2e3,i]),this._shortMonthsParse[i]=this.monthsShort(n,"").toLocaleLowerCase(),this._longMonthsParse[i]=this.months(n,"").toLocaleLowerCase();return s?"MMM"===e?-1!==(a=Mt.call(this._shortMonthsParse,t))?a:null:-1!==(a=Mt.call(this._longMonthsParse,t))?a:null:"MMM"===e?-1!==(a=Mt.call(this._shortMonthsParse,t))||-1!==(a=Mt.call(this._longMonthsParse,t))?a:null:-1!==(a=Mt.call(this._longMonthsParse,t))||-1!==(a=Mt.call(this._shortMonthsParse,t))?a:null}.call(this,t,e,s);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),i=0;i<12;i++){if(a=u([2e3,i]),s&&!this._longMonthsParse[i]&&(this._longMonthsParse[i]=new RegExp("^"+this.months(a,"").replace(".","")+"$","i"),this._shortMonthsParse[i]=new RegExp("^"+this.monthsShort(a,"").replace(".","")+"$","i")),s||this._monthsParse[i]||(a="^"+this.months(a,"")+"|^"+this.monthsShort(a,""),this._monthsParse[i]=new RegExp(a.replace(".",""),"i")),s&&"MMMM"===e&&this._longMonthsParse[i].test(t))return i;if(s&&"MMM"===e&&this._shortMonthsParse[i].test(t))return i;if(!s&&this._monthsParse[i].test(t))return i}},q.monthsRegex=function(t){return this._monthsParseExact?(f(this,"_monthsRegex")||Rt.call(this),t?this._monthsStrictRegex:this._monthsRegex):(f(this,"_monthsRegex")||(this._monthsRegex=Ht),this._monthsStrictRegex&&t?this._monthsStrictRegex:this._monthsRegex)},q.monthsShortRegex=function(t){return this._monthsParseExact?(f(this,"_monthsRegex")||Rt.call(this),t?this._monthsShortStrictRegex:this._monthsShortRegex):(f(this,"_monthsShortRegex")||(this._monthsShortRegex=Nt),this._monthsShortStrictRegex&&t?this._monthsShortStrictRegex:this._monthsShortRegex)},q.week=function(t){return Ft(t,this._week.dow,this._week.doy).week},q.firstDayOfYear=function(){return this._week.doy},q.firstDayOfWeek=function(){return this._week.dow},q.weekdays=function(t,e){e=r(this._weekdays)?this._weekdays:this._weekdays[t&&!0!==t&&this._weekdays.isFormat.test(e)?"format":"standalone"];return!0===t?jt(e,this._week.dow):t?e[t.day()]:e},q.weekdaysMin=function(t){return!0===t?jt(this._weekdaysMin,this._week.dow):t?this._weekdaysMin[t.day()]:this._weekdaysMin},q.weekdaysShort=function(t){return!0===t?jt(this._weekdaysShort,this._week.dow):t?this._weekdaysShort[t.day()]:this._weekdaysShort},q.weekdaysParse=function(t,e,s){var i,a;if(this._weekdaysParseExact)return function(t,e,s){var i,a,n,t=t.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],i=0;i<7;++i)n=u([2e3,1]).day(i),this._minWeekdaysParse[i]=this.weekdaysMin(n,"").toLocaleLowerCase(),this._shortWeekdaysParse[i]=this.weekdaysShort(n,"").toLocaleLowerCase(),this._weekdaysParse[i]=this.weekdays(n,"").toLocaleLowerCase();return s?"dddd"===e?-1!==(a=Mt.call(this._weekdaysParse,t))?a:null:"ddd"===e?-1!==(a=Mt.call(this._shortWeekdaysParse,t))?a:null:-1!==(a=Mt.call(this._minWeekdaysParse,t))?a:null:"dddd"===e?-1!==(a=Mt.call(this._weekdaysParse,t))||-1!==(a=Mt.call(this._shortWeekdaysParse,t))||-1!==(a=Mt.call(this._minWeekdaysParse,t))?a:null:"ddd"===e?-1!==(a=Mt.call(this._shortWeekdaysParse,t))||-1!==(a=Mt.call(this._weekdaysParse,t))||-1!==(a=Mt.call(this._minWeekdaysParse,t))?a:null:-1!==(a=Mt.call(this._minWeekdaysParse,t))||-1!==(a=Mt.call(this._weekdaysParse,t))||-1!==(a=Mt.call(this._shortWeekdaysParse,t))?a:null}.call(this,t,e,s);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),i=0;i<7;i++){if(a=u([2e3,1]).day(i),s&&!this._fullWeekdaysParse[i]&&(this._fullWeekdaysParse[i]=new RegExp("^"+this.weekdays(a,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[i]=new RegExp("^"+this.weekdaysShort(a,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[i]=new RegExp("^"+this.weekdaysMin(a,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[i]||(a="^"+this.weekdays(a,"")+"|^"+this.weekdaysShort(a,"")+"|^"+this.weekdaysMin(a,""),this._weekdaysParse[i]=new RegExp(a.replace(".",""),"i")),s&&"dddd"===e&&this._fullWeekdaysParse[i].test(t))return i;if(s&&"ddd"===e&&this._shortWeekdaysParse[i].test(t))return i;if(s&&"dd"===e&&this._minWeekdaysParse[i].test(t))return i;if(!s&&this._weekdaysParse[i].test(t))return i}},q.weekdaysRegex=function(t){return this._weekdaysParseExact?(f(this,"_weekdaysRegex")||Jt.call(this),t?this._weekdaysStrictRegex:this._weekdaysRegex):(f(this,"_weekdaysRegex")||(this._weekdaysRegex=Zt),this._weekdaysStrictRegex&&t?this._weekdaysStrictRegex:this._weekdaysRegex)},q.weekdaysShortRegex=function(t){return this._weekdaysParseExact?(f(this,"_weekdaysRegex")||Jt.call(this),t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(f(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=qt),this._weekdaysShortStrictRegex&&t?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},q.weekdaysMinRegex=function(t){return this._weekdaysParseExact?(f(this,"_weekdaysRegex")||Jt.call(this),t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(f(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=$t),this._weekdaysMinStrictRegex&&t?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},q.isPM=function(t){return"p"===(t+"").toLowerCase().charAt(0)},q.meridiem=function(t,e,s){return 11<t?s?"pm":"PM":s?"am":"AM"},oe("en",{dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(t){var e=t%10;return t+(1===Y(t%100/10)?"th":1==e?"st":2==e?"nd":3==e?"rd":"th")}}),m.lang=s("moment.lang is deprecated. Use moment.locale instead.",oe),m.langData=s("moment.langData is deprecated. Use moment.localeData instead.",le);var ls=Math.abs;function ds(t,e,s,i){s=Ve(e,s);return t._milliseconds+=i*s._milliseconds,t._days+=i*s._days,t._months+=i*s._months,t._bubble()}function cs(t){return t<0?Math.floor(t):Math.ceil(t)}function us(t){return 4800*t/146097}function fs(t){return 146097*t/4800}function ms(t){return function(){return this.as(t)}}et=ms("ms"),$=ms("s"),B=ms("m"),z=ms("h"),K=ms("d"),Z=ms("w"),Ze=ms("M"),xe=ms("Q"),Ce=ms("y");function ps(t){return function(){return this.isValid()?this._data[t]:NaN}}var rt=ps("milliseconds"),ee=ps("seconds"),Tt=ps("minutes"),Gt=ps("hours"),zt=ps("days"),X=ps("months"),St=ps("years"),ys=Math.round,gs={ss:44,s:45,m:45,h:22,d:26,M:11},Ds=Math.abs;function _s(t){return(0<t)-(t<0)||+t}function ks(){if(!this.isValid())return this.localeData().invalidDate();var t=Ds(this._milliseconds)/1e3,e=Ds(this._days),s=Ds(this._months),i=v((l=v(t/60))/60);t%=60,l%=60;var a=v(s/12),n=s%=12,r=e,o=i,h=l,s=t?t.toFixed(3).replace(/\.?0+$/,""):"",e=this.asSeconds();if(!e)return"P0D";var i=_s(this._months)!==_s(e)?"-":"",l=_s(this._days)!==_s(e)?"-":"",t=_s(this._milliseconds)!==_s(e)?"-":"";return(e<0?"-":"")+"P"+(a?i+a+"Y":"")+(n?i+n+"M":"")+(r?l+r+"D":"")+(o||h||s?"T":"")+(o?t+o+"H":"")+(h?t+h+"M":"")+(s?t+s+"S":"")}q=Te.prototype;return q.isValid=function(){return this._isValid},q.abs=function(){var t=this._data;return this._milliseconds=ls(this._milliseconds),this._days=ls(this._days),this._months=ls(this._months),t.milliseconds=ls(t.milliseconds),t.seconds=ls(t.seconds),t.minutes=ls(t.minutes),t.hours=ls(t.hours),t.months=ls(t.months),t.years=ls(t.years),this},q.add=function(t,e){return ds(this,t,e,1)},q.subtract=function(t,e){return ds(this,t,e,-1)},q.as=function(t){if(!this.isValid())return NaN;var e,s,i=this._milliseconds;if("month"===(t=L(t))||"quarter"===t||"year"===t)switch(e=this._days+i/864e5,s=this._months+us(e),t){case"month":return s;case"quarter":return s/3;case"year":return s/12}else switch(e=this._days+Math.round(fs(this._months)),t){case"week":return e/7+i/6048e5;case"day":return e+i/864e5;case"hour":return 24*e+i/36e5;case"minute":return 1440*e+i/6e4;case"second":return 86400*e+i/1e3;case"millisecond":return Math.floor(864e5*e)+i;default:throw new Error("Unknown unit "+t)}},q.asMilliseconds=et,q.asSeconds=$,q.asMinutes=B,q.asHours=z,q.asDays=K,q.asWeeks=Z,q.asMonths=Ze,q.asQuarters=xe,q.asYears=Ce,q.valueOf=function(){return this.isValid()?this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*Y(this._months/12):NaN},q._bubble=function(){var t=this._milliseconds,e=this._days,s=this._months,i=this._data;return 0<=t&&0<=e&&0<=s||t<=0&&e<=0&&s<=0||(t+=864e5*cs(fs(s)+e),s=e=0),i.milliseconds=t%1e3,t=v(t/1e3),i.seconds=t%60,t=v(t/60),i.minutes=t%60,t=v(t/60),i.hours=t%24,s+=t=v(us(e+=v(t/24))),e-=cs(fs(t)),t=v(s/12),s%=12,i.days=e,i.months=s,i.years=t,this},q.clone=function(){return Ve(this)},q.get=function(t){return t=L(t),this.isValid()?this[t+"s"]():NaN},q.milliseconds=rt,q.seconds=ee,q.minutes=Tt,q.hours=Gt,q.days=zt,q.weeks=function(){return v(this.days()/7)},q.months=X,q.years=St,q.humanize=function(t){if(!this.isValid())return this.localeData().invalidDate();var e,s,i,a,n,r,o,h=this.localeData(),l=(e=!t,s=h,l=Ve(this).abs(),i=ys(l.as("s")),a=ys(l.as("m")),n=ys(l.as("h")),r=ys(l.as("d")),o=ys(l.as("M")),l=ys(l.as("y")),(l=(i<=gs.ss?["s",i]:i<gs.s&&["ss",i])||a<=1&&["m"]||a<gs.m&&["mm",a]||n<=1&&["h"]||n<gs.h&&["hh",n]||r<=1&&["d"]||r<gs.d&&["dd",r]||o<=1&&["M"]||o<gs.M&&["MM",o]||l<=1&&["y"]||["yy",l])[2]=e,l[3]=0<+this,l[4]=s,function(t,e,s,i,a){return a.relativeTime(e||1,!!s,t,i)}.apply(null,l));return t&&(l=h.pastFuture(+this,l)),h.postformat(l)},q.toISOString=ks,q.toString=ks,q.toJSON=ks,q.locale=$e,q.localeData=Je,q.toIsoString=s("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",ks),q.lang=Q,V("X",0,0,"unix"),V("x",0,0,"valueOf"),ht("x",it),ht("X",/[+-]?\d+(\.\d{1,3})?/),ct("X",function(t,e,s){s._d=new Date(1e3*parseFloat(t,10))}),ct("x",function(t,e,s){s._d=new Date(Y(t))}),m.version="2.24.0",t=be,m.fn=tt,m.min=function(){return Pe("isBefore",[].slice.call(arguments,0))},m.max=function(){return Pe("isAfter",[].slice.call(arguments,0))},m.now=function(){return Date.now?Date.now():+new Date},m.utc=u,m.unix=function(t){return be(1e3*t)},m.months=function(t,e){return os(t,e,"months")},m.isDate=l,m.locale=oe,m.invalid=g,m.duration=Ve,m.isMoment=w,m.weekdays=function(t,e,s){return hs(t,e,s,"weekdays")},m.parseZone=function(){return be.apply(null,arguments).parseZone()},m.localeData=le,m.isDuration=We,m.monthsShort=function(t,e){return os(t,e,"monthsShort")},m.weekdaysMin=function(t,e,s){return hs(t,e,s,"weekdaysMin")},m.defineLocale=he,m.updateLocale=function(t,e){var s,i;return null!=e?(i=se,(e=new P(e=x(i=null!=(s=re(t))?s._config:i,e))).parentLocale=ie[t],ie[t]=e,oe(t)):null!=ie[t]&&(null!=ie[t].parentLocale?ie[t]=ie[t].parentLocale:null!=ie[t]&&delete ie[t]),ie[t]},m.locales=function(){return O(ie)},m.weekdaysShort=function(t,e,s){return hs(t,e,s,"weekdaysShort")},m.normalizeUnits=L,m.relativeTimeRounding=function(t){return void 0===t?ys:"function"==typeof t&&(ys=t,!0)},m.relativeTimeThreshold=function(t,e){return void 0!==gs[t]&&(void 0===e?gs[t]:(gs[t]=e,"s"===t&&(gs.ss=e-1),!0))},m.calendarFormat=function(t,e){e=t.diff(e,"days",!0);return e<-6?"sameElse":e<-1?"lastWeek":e<0?"lastDay":e<1?"sameDay":e<2?"nextDay":e<7?"nextWeek":"sameElse"},m.prototype=tt,m.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},m}),function(t,s){var e,i;"function"==typeof define&&define.amd?define(["moment","jquery"],function(t,e){return e.fn||(e.fn={}),"function"!=typeof t&&t.default&&(t=t.default),s(t,e)}):"object"==typeof module&&module.exports?((e="undefined"!=typeof window?window.jQuery:void 0)||(e=require("jquery")).fn||(e.fn={}),i="undefined"!=typeof window&&void 0!==window.moment?window.moment:require("moment"),module.exports=s(i,e)):t.daterangepicker=s(t.moment,t.jQuery)}(this,function(T,W){function i(t,e,s){var i,a,n,r;if(this.parentEl="body",this.element=W(t),this.startDate=T().startOf("day"),this.endDate=T().endOf("day"),this.minDate=!1,this.maxDate=!1,this.maxSpan=!1,this.autoApply=!1,this.singleDatePicker=!1,this.showDropdowns=!1,this.minYear=T().subtract(100,"year").format("YYYY"),this.maxYear=T().add(100,"year").format("YYYY"),this.showWeekNumbers=!1,this.showISOWeekNumbers=!1,this.showCustomRangeLabel=!0,this.timePicker=!1,this.timePicker24Hour=!1,this.timePickerIncrement=1,this.timePickerSeconds=!1,this.linkedCalendars=!0,this.autoUpdateInput=!0,this.alwaysShowCalendars=!1,this.ranges={},this.opens="right",this.element.hasClass("pull-right")&&(this.opens="left"),this.drops="down",this.element.hasClass("dropup")&&(this.drops="up"),this.buttonClasses="btn btn-sm",this.applyButtonClasses="btn-primary",this.cancelButtonClasses="btn-default",this.locale={direction:"ltr",format:T.localeData().longDateFormat("L"),separator:" - ",applyLabel:"Apply",cancelLabel:"Cancel",weekLabel:"W",customRangeLabel:"Custom Range",daysOfWeek:T.weekdaysMin(),monthNames:T.monthsShort(),firstDay:T.localeData().firstDayOfWeek()},this.callback=function(){},this.isShowing=!1,this.leftCalendar={},this.rightCalendar={},"object"==typeof e&&null!==e||(e={}),"string"==typeof(e=W.extend(this.element.data(),e)).template||e.template instanceof W||(e.template='<div class="daterangepicker"><div class="ranges"></div><div class="drp-calendar left"><div class="calendar-table"></div><div class="calendar-time"></div></div><div class="drp-calendar right"><div class="calendar-table"></div><div class="calendar-time"></div></div><div class="drp-buttons"><span class="drp-selected"></span><button class="cancelBtn" type="button"></button><button class="applyBtn" disabled="disabled" type="button"></button> </div></div>'),this.parentEl=e.parentEl&&W(e.parentEl).length?W(e.parentEl):W(this.parentEl),this.container=W(e.template).appendTo(this.parentEl),"object"==typeof e.locale&&("string"==typeof e.locale.direction&&(this.locale.direction=e.locale.direction),"string"==typeof e.locale.format&&(this.locale.format=e.locale.format),"string"==typeof e.locale.separator&&(this.locale.separator=e.locale.separator),"object"==typeof e.locale.daysOfWeek&&(this.locale.daysOfWeek=e.locale.daysOfWeek.slice()),"object"==typeof e.locale.monthNames&&(this.locale.monthNames=e.locale.monthNames.slice()),"number"==typeof e.locale.firstDay&&(this.locale.firstDay=e.locale.firstDay),"string"==typeof e.locale.applyLabel&&(this.locale.applyLabel=e.locale.applyLabel),"string"==typeof e.locale.cancelLabel&&(this.locale.cancelLabel=e.locale.cancelLabel),"string"==typeof e.locale.weekLabel&&(this.locale.weekLabel=e.locale.weekLabel),"string"==typeof e.locale.customRangeLabel&&((h=document.createElement("textarea")).innerHTML=e.locale.customRangeLabel,l=h.value,this.locale.customRangeLabel=l)),this.container.addClass(this.locale.direction),"string"==typeof e.startDate&&(this.startDate=T(e.startDate,this.locale.format)),"string"==typeof e.endDate&&(this.endDate=T(e.endDate,this.locale.format)),"string"==typeof e.minDate&&(this.minDate=T(e.minDate,this.locale.format)),"string"==typeof e.maxDate&&(this.maxDate=T(e.maxDate,this.locale.format)),"object"==typeof e.startDate&&(this.startDate=T(e.startDate)),"object"==typeof e.endDate&&(this.endDate=T(e.endDate)),"object"==typeof e.minDate&&(this.minDate=T(e.minDate)),"object"==typeof e.maxDate&&(this.maxDate=T(e.maxDate)),this.minDate&&this.startDate.isBefore(this.minDate)&&(this.startDate=this.minDate.clone()),this.maxDate&&this.endDate.isAfter(this.maxDate)&&(this.endDate=this.maxDate.clone()),"string"==typeof e.applyButtonClasses&&(this.applyButtonClasses=e.applyButtonClasses),"string"==typeof e.applyClass&&(this.applyButtonClasses=e.applyClass),"string"==typeof e.cancelButtonClasses&&(this.cancelButtonClasses=e.cancelButtonClasses),"string"==typeof e.cancelClass&&(this.cancelButtonClasses=e.cancelClass),"object"==typeof e.maxSpan&&(this.maxSpan=e.maxSpan),"object"==typeof e.dateLimit&&(this.maxSpan=e.dateLimit),"string"==typeof e.opens&&(this.opens=e.opens),"string"==typeof e.drops&&(this.drops=e.drops),"boolean"==typeof e.showWeekNumbers&&(this.showWeekNumbers=e.showWeekNumbers),"boolean"==typeof e.showISOWeekNumbers&&(this.showISOWeekNumbers=e.showISOWeekNumbers),"string"==typeof e.buttonClasses&&(this.buttonClasses=e.buttonClasses),"object"==typeof e.buttonClasses&&(this.buttonClasses=e.buttonClasses.join(" ")),"boolean"==typeof e.showDropdowns&&(this.showDropdowns=e.showDropdowns),"number"==typeof e.minYear&&(this.minYear=e.minYear),"number"==typeof e.maxYear&&(this.maxYear=e.maxYear),"boolean"==typeof e.showCustomRangeLabel&&(this.showCustomRangeLabel=e.showCustomRangeLabel),"boolean"==typeof e.singleDatePicker&&(this.singleDatePicker=e.singleDatePicker,this.singleDatePicker&&(this.endDate=this.startDate.clone())),"boolean"==typeof e.timePicker&&(this.timePicker=e.timePicker),"boolean"==typeof e.timePickerSeconds&&(this.timePickerSeconds=e.timePickerSeconds),"number"==typeof e.timePickerIncrement&&(this.timePickerIncrement=e.timePickerIncrement),"boolean"==typeof e.timePicker24Hour&&(this.timePicker24Hour=e.timePicker24Hour),"boolean"==typeof e.autoApply&&(this.autoApply=e.autoApply),"boolean"==typeof e.autoUpdateInput&&(this.autoUpdateInput=e.autoUpdateInput),"boolean"==typeof e.linkedCalendars&&(this.linkedCalendars=e.linkedCalendars),"function"==typeof e.isInvalidDate&&(this.isInvalidDate=e.isInvalidDate),"function"==typeof e.isCustomDate&&(this.isCustomDate=e.isCustomDate),"boolean"==typeof e.alwaysShowCalendars&&(this.alwaysShowCalendars=e.alwaysShowCalendars),0!=this.locale.firstDay)for(var o=this.locale.firstDay;0<o;)this.locale.daysOfWeek.push(this.locale.daysOfWeek.shift()),o--;if(void 0===e.startDate&&void 0===e.endDate&&W(this.element).is(":text")&&(r=i=null,2==(t=(n=W(this.element).val()).split(this.locale.separator)).length?(r=T(t[0],this.locale.format),i=T(t[1],this.locale.format)):this.singleDatePicker&&""!==n&&(r=T(n,this.locale.format),i=T(n,this.locale.format)),null!==r&&null!==i&&(this.setStartDate(r),this.setEndDate(i))),"object"==typeof e.ranges){for(a in e.ranges){r="string"==typeof e.ranges[a][0]?T(e.ranges[a][0],this.locale.format):T(e.ranges[a][0]),i="string"==typeof e.ranges[a][1]?T(e.ranges[a][1],this.locale.format):T(e.ranges[a][1]),this.minDate&&r.isBefore(this.minDate)&&(r=this.minDate.clone());var h,l,d=this.maxDate;(d=this.maxSpan&&d&&r.clone().add(this.maxSpan).isAfter(d)?r.clone().add(this.maxSpan):d)&&i.isAfter(d)&&(i=d.clone()),this.minDate&&i.isBefore(this.minDate,this.timepicker?"minute":"day")||d&&r.isAfter(d,this.timepicker?"minute":"day")||((h=document.createElement("textarea")).innerHTML=a,l=h.value,this.ranges[l]=[r,i])}var c="<ul>";for(a in this.ranges)c+='<li data-range-key="'+a+'">'+a+"</li>";this.showCustomRangeLabel&&(c+='<li data-range-key="'+this.locale.customRangeLabel+'">'+this.locale.customRangeLabel+"</li>"),c+="</ul>",this.container.find(".ranges").prepend(c)}"function"==typeof s&&(this.callback=s),this.timePicker||(this.startDate=this.startDate.startOf("day"),this.endDate=this.endDate.endOf("day"),this.container.find(".calendar-time").hide()),this.timePicker&&this.autoApply&&(this.autoApply=!1),this.autoApply&&this.container.addClass("auto-apply"),"object"==typeof e.ranges&&this.container.addClass("show-ranges"),this.singleDatePicker&&(this.container.addClass("single"),this.container.find(".drp-calendar.left").addClass("single"),this.container.find(".drp-calendar.left").show(),this.container.find(".drp-calendar.right").hide(),this.timePicker||this.container.addClass("auto-apply")),(void 0===e.ranges&&!this.singleDatePicker||this.alwaysShowCalendars)&&this.container.addClass("show-calendar"),this.container.addClass("opens"+this.opens),this.container.find(".applyBtn, .cancelBtn").addClass(this.buttonClasses),this.applyButtonClasses.length&&this.container.find(".applyBtn").addClass(this.applyButtonClasses),this.cancelButtonClasses.length&&this.container.find(".cancelBtn").addClass(this.cancelButtonClasses),this.container.find(".applyBtn").html(this.locale.applyLabel),this.container.find(".cancelBtn").html(this.locale.cancelLabel),this.container.find(".drp-calendar").on("click.daterangepicker",".prev",W.proxy(this.clickPrev,this)).on("click.daterangepicker",".next",W.proxy(this.clickNext,this)).on("mousedown.daterangepicker","td.available",W.proxy(this.clickDate,this)).on("mouseenter.daterangepicker","td.available",W.proxy(this.hoverDate,this)).on("change.daterangepicker","select.yearselect",W.proxy(this.monthOrYearChanged,this)).on("change.daterangepicker","select.monthselect",W.proxy(this.monthOrYearChanged,this)).on("change.daterangepicker","select.hourselect,select.minuteselect,select.secondselect,select.ampmselect",W.proxy(this.timeChanged,this)),this.container.find(".ranges").on("click.daterangepicker","li",W.proxy(this.clickRange,this)),this.container.find(".drp-buttons").on("click.daterangepicker","button.applyBtn",W.proxy(this.clickApply,this)).on("click.daterangepicker","button.cancelBtn",W.proxy(this.clickCancel,this)),this.element.is("input")||this.element.is("button")?this.element.on({"click.daterangepicker":W.proxy(this.show,this),"focus.daterangepicker":W.proxy(this.show,this),"keyup.daterangepicker":W.proxy(this.elementChanged,this),"keydown.daterangepicker":W.proxy(this.keydown,this)}):(this.element.on("click.daterangepicker",W.proxy(this.toggle,this)),this.element.on("keydown.daterangepicker",W.proxy(this.toggle,this))),this.updateElement()}return i.prototype={constructor:i,setStartDate:function(t){"string"==typeof t&&(this.startDate=T(t,this.locale.format)),"object"==typeof t&&(this.startDate=T(t)),this.timePicker||(this.startDate=this.startDate.startOf("day")),this.timePicker&&this.timePickerIncrement&&this.startDate.minute(Math.round(this.startDate.minute()/this.timePickerIncrement)*this.timePickerIncrement),this.minDate&&this.startDate.isBefore(this.minDate)&&(this.startDate=this.minDate.clone(),this.timePicker&&this.timePickerIncrement&&this.startDate.minute(Math.round(this.startDate.minute()/this.timePickerIncrement)*this.timePickerIncrement)),this.maxDate&&this.startDate.isAfter(this.maxDate)&&(this.startDate=this.maxDate.clone(),this.timePicker&&this.timePickerIncrement&&this.startDate.minute(Math.floor(this.startDate.minute()/this.timePickerIncrement)*this.timePickerIncrement)),this.isShowing||this.updateElement(),this.updateMonthsInView()},setEndDate:function(t){"string"==typeof t&&(this.endDate=T(t,this.locale.format)),"object"==typeof t&&(this.endDate=T(t)),this.timePicker||(this.endDate=this.endDate.endOf("day")),this.timePicker&&this.timePickerIncrement&&this.endDate.minute(Math.round(this.endDate.minute()/this.timePickerIncrement)*this.timePickerIncrement),this.endDate.isBefore(this.startDate)&&(this.endDate=this.startDate.clone()),this.maxDate&&this.endDate.isAfter(this.maxDate)&&(this.endDate=this.maxDate.clone()),this.maxSpan&&this.startDate.clone().add(this.maxSpan).isBefore(this.endDate)&&(this.endDate=this.startDate.clone().add(this.maxSpan)),this.previousRightTime=this.endDate.clone(),this.container.find(".drp-selected").html(this.startDate.format(this.locale.format)+this.locale.separator+this.endDate.format(this.locale.format)),this.isShowing||this.updateElement(),this.updateMonthsInView()},isInvalidDate:function(){return!1},isCustomDate:function(){return!1},updateView:function(){this.timePicker&&(this.renderTimePicker("left"),this.renderTimePicker("right"),this.endDate?this.container.find(".right .calendar-time select").removeAttr("disabled").removeClass("disabled"):this.container.find(".right .calendar-time select").attr("disabled","disabled").addClass("disabled")),this.endDate&&this.container.find(".drp-selected").html(this.startDate.format(this.locale.format)+this.locale.separator+this.endDate.format(this.locale.format)),this.updateMonthsInView(),this.updateCalendars(),this.updateFormInputs()},updateMonthsInView:function(){if(this.endDate){if(!this.singleDatePicker&&this.leftCalendar.month&&this.rightCalendar.month&&(this.startDate.format("YYYY-MM")==this.leftCalendar.month.format("YYYY-MM")||this.startDate.format("YYYY-MM")==this.rightCalendar.month.format("YYYY-MM"))&&(this.endDate.format("YYYY-MM")==this.leftCalendar.month.format("YYYY-MM")||this.endDate.format("YYYY-MM")==this.rightCalendar.month.format("YYYY-MM")))return;this.leftCalendar.month=this.startDate.clone().date(2),this.linkedCalendars||this.endDate.month()==this.startDate.month()&&this.endDate.year()==this.startDate.year()?this.rightCalendar.month=this.startDate.clone().date(2).add(1,"month"):this.rightCalendar.month=this.endDate.clone().date(2)}else this.leftCalendar.month.format("YYYY-MM")!=this.startDate.format("YYYY-MM")&&this.rightCalendar.month.format("YYYY-MM")!=this.startDate.format("YYYY-MM")&&(this.leftCalendar.month=this.startDate.clone().date(2),this.rightCalendar.month=this.startDate.clone().date(2).add(1,"month"));this.maxDate&&this.linkedCalendars&&!this.singleDatePicker&&this.rightCalendar.month>this.maxDate&&(this.rightCalendar.month=this.maxDate.clone().date(2),this.leftCalendar.month=this.maxDate.clone().date(2).subtract(1,"month"))},updateCalendars:function(){var t,e,s,i;this.timePicker&&(this.endDate?(e=parseInt(this.container.find(".left .hourselect").val(),10),s=parseInt(this.container.find(".left .minuteselect").val(),10),isNaN(s)&&(s=parseInt(this.container.find(".left .minuteselect option:last").val(),10)),t=this.timePickerSeconds?parseInt(this.container.find(".left .secondselect").val(),10):0,this.timePicker24Hour||("PM"===(i=this.container.find(".left .ampmselect").val())&&e<12&&(e+=12),"AM"===i&&12===e&&(e=0))):(e=parseInt(this.container.find(".right .hourselect").val(),10),s=parseInt(this.container.find(".right .minuteselect").val(),10),isNaN(s)&&(s=parseInt(this.container.find(".right .minuteselect option:last").val(),10)),t=this.timePickerSeconds?parseInt(this.container.find(".right .secondselect").val(),10):0,this.timePicker24Hour||("PM"===(i=this.container.find(".right .ampmselect").val())&&e<12&&(e+=12),"AM"===i&&12===e&&(e=0))),this.leftCalendar.month.hour(e).minute(s).second(t),this.rightCalendar.month.hour(e).minute(s).second(t)),this.renderCalendar("left"),this.renderCalendar("right"),this.container.find(".ranges li").removeClass("active"),null!=this.endDate&&this.calculateChosenLabel()},renderCalendar:function(t){var e="left"==t?this.leftCalendar:this.rightCalendar,s=e.month.month(),i=e.month.year(),a=e.month.hour(),n=e.month.minute(),r=e.month.second(),o=T([i,s]).daysInMonth(),h=T([i,s,1]),l=T([i,s,o]),d=T(h).subtract(1,"month").month(),i=T(h).subtract(1,"month").year(),s=T([i,d]).daysInMonth(),o=h.day();(e=[]).firstDay=h,e.lastDay=l;for(var c=0;c<6;c++)e[c]=[];l=s-o+this.locale.firstDay+1;s<l&&(l-=7),o==this.locale.firstDay&&(l=s-6);for(var u=T([i,d,l,12,n,r]),c=0,f=0,m=0;c<42;c++,f++,u=T(u).add(24,"hour"))0<c&&f%7==0&&(f=0,m++),e[m][f]=u.clone().hour(a).minute(n).second(r),u.hour(12),this.minDate&&e[m][f].format("YYYY-MM-DD")==this.minDate.format("YYYY-MM-DD")&&e[m][f].isBefore(this.minDate)&&"left"==t&&(e[m][f]=this.minDate.clone()),this.maxDate&&e[m][f].format("YYYY-MM-DD")==this.maxDate.format("YYYY-MM-DD")&&e[m][f].isAfter(this.maxDate)&&"right"==t&&(e[m][f]=this.maxDate.clone());"left"==t?this.leftCalendar.calendar=e:this.rightCalendar.calendar=e;var p="left"==t?this.minDate:this.startDate,y=this.maxDate,g=("left"==t?this.startDate:this.endDate,this.locale.direction,'<table class="table-condensed">');g+="<thead>",g+="<tr>",(this.showWeekNumbers||this.showISOWeekNumbers)&&(g+="<th></th>"),p&&!p.isBefore(e.firstDay)||this.linkedCalendars&&"left"!=t?g+="<th></th>":g+='<th class="prev available"><span></span></th>';var d=this.locale.monthNames[e[1][1].month()]+e[1][1].format(" YYYY");if(this.showDropdowns){for(var D=e[1][1].month(),_=e[1][1].year(),k=y&&y.year()||this.maxYear,l=p&&p.year()||this.minYear,w=_==l,v=_==k,Y='<select class="monthselect">',M=0;M<12;M++)(!w||p&&M>=p.month())&&(!v||y&&M<=y.month())?Y+="<option value='"+M+"'"+(M===D?" selected='selected'":"")+">"+this.locale.monthNames[M]+"</option>":Y+="<option value='"+M+"'"+(M===D?" selected='selected'":"")+" disabled='disabled'>"+this.locale.monthNames[M]+"</option>";Y+="</select>";for(var S='<select class="yearselect">',b=l;b<=k;b++)S+='<option value="'+b+'"'+(b===_?' selected="selected"':"")+">"+b+"</option>";d=Y+(S+="</select>")}g+='<th colspan="5" class="month">'+d+"</th>",y&&!y.isAfter(e.lastDay)||this.linkedCalendars&&"right"!=t&&!this.singleDatePicker?g+="<th></th>":g+='<th class="next available"><span></span></th>',g+="</tr>",g+="<tr>",(this.showWeekNumbers||this.showISOWeekNumbers)&&(g+='<th class="week">'+this.locale.weekLabel+"</th>"),W.each(this.locale.daysOfWeek,function(t,e){g+="<th>"+e+"</th>"}),g+="</tr>",g+="</thead>",g+="<tbody>",null==this.endDate&&this.maxSpan&&(d=this.startDate.clone().add(this.maxSpan).endOf("day"),y&&!d.isBefore(y)||(y=d));for(m=0;m<6;m++){g+="<tr>",this.showWeekNumbers?g+='<td class="week">'+e[m][0].week()+"</td>":this.showISOWeekNumbers&&(g+='<td class="week">'+e[m][0].isoWeek()+"</td>");for(f=0;f<7;f++){var C=[];e[m][f].isSame(new Date,"day")&&C.push("today"),5<e[m][f].isoWeekday()&&C.push("weekend"),e[m][f].month()!=e[1][1].month()&&C.push("off","ends"),this.minDate&&e[m][f].isBefore(this.minDate,"day")&&C.push("off","disabled"),y&&e[m][f].isAfter(y,"day")&&C.push("off","disabled"),this.isInvalidDate(e[m][f])&&C.push("off","disabled"),e[m][f].format("YYYY-MM-DD")==this.startDate.format("YYYY-MM-DD")&&C.push("active","start-date"),null!=this.endDate&&e[m][f].format("YYYY-MM-DD")==this.endDate.format("YYYY-MM-DD")&&C.push("active","end-date"),null!=this.endDate&&e[m][f]>this.startDate&&e[m][f]<this.endDate&&C.push("in-range");var x=this.isCustomDate(e[m][f]);!1!==x&&("string"==typeof x?C.push(x):Array.prototype.push.apply(C,x));for(var P="",O=!1,c=0;c<C.length;c++)P+=C[c]+" ","disabled"==C[c]&&(O=!0);O||(P+="available"),g+='<td class="'+P.replace(/^\s+|\s+$/g,"")+'" data-title="r'+m+"c"+f+'">'+e[m][f].date()+"</td>"}g+="</tr>"}g+="</tbody>",g+="</table>",this.container.find(".drp-calendar."+t+" .calendar-table").html(g)},renderTimePicker:function(t){if("right"!=t||this.endDate){var e,s,i=this.maxDate;!this.maxSpan||this.maxDate&&!this.startDate.clone().add(this.maxSpan).isBefore(this.maxDate)||(i=this.startDate.clone().add(this.maxSpan)),"left"==t?(e=this.startDate.clone(),s=this.minDate):"right"==t&&(e=this.endDate.clone(),s=this.startDate,""!=(n=this.container.find(".drp-calendar.right .calendar-time")).html()&&(e.hour(isNaN(e.hour())?n.find(".hourselect option:selected").val():e.hour()),e.minute(isNaN(e.minute())?n.find(".minuteselect option:selected").val():e.minute()),e.second(isNaN(e.second())?n.find(".secondselect option:selected").val():e.second()),this.timePicker24Hour||("PM"===(c=n.find(".ampmselect option:selected").val())&&e.hour()<12&&e.hour(e.hour()+12),"AM"===c&&12===e.hour()&&e.hour(0))),e.isBefore(this.startDate)&&(e=this.startDate.clone()),i&&e.isAfter(i)&&(e=i.clone()));for(var a='<select class="hourselect">',n=this.timePicker24Hour?0:1,r=this.timePicker24Hour?23:12,o=n;o<=r;o++){var h=o;this.timePicker24Hour||(h=12<=e.hour()?12==o?12:o+12:12==o?0:o);var l=e.clone().hour(h),d=!1;s&&l.minute(59).isBefore(s)&&(d=!0),i&&l.minute(0).isAfter(i)&&(d=!0),h!=e.hour()||d?a+=d?'<option value="'+o+'" disabled="disabled" class="disabled">'+o+"</option>":'<option value="'+o+'">'+o+"</option>":a+='<option value="'+o+'" selected="selected">'+o+"</option>"}a+="</select> ",a+=': <select class="minuteselect">';for(var c,o=0;o<60;o+=this.timePickerIncrement){var u=o<10?"0"+o:o,l=e.clone().minute(o),d=!1;s&&l.second(59).isBefore(s)&&(d=!0),i&&l.second(0).isAfter(i)&&(d=!0),e.minute()!=o||d?a+=d?'<option value="'+o+'" disabled="disabled" class="disabled">'+u+"</option>":'<option value="'+o+'">'+u+"</option>":a+='<option value="'+o+'" selected="selected">'+u+"</option>"}if(a+="</select> ",this.timePickerSeconds){a+=': <select class="secondselect">';for(o=0;o<60;o++){u=o<10?"0"+o:o,l=e.clone().second(o),d=!1;s&&l.isBefore(s)&&(d=!0),i&&l.isAfter(i)&&(d=!0),e.second()!=o||d?a+=d?'<option value="'+o+'" disabled="disabled" class="disabled">'+u+"</option>":'<option value="'+o+'">'+u+"</option>":a+='<option value="'+o+'" selected="selected">'+u+"</option>"}a+="</select> "}this.timePicker24Hour||(a+='<select class="ampmselect">',n=c="",s&&e.clone().hour(12).minute(0).second(0).isBefore(s)&&(c=' disabled="disabled" class="disabled"'),i&&e.clone().hour(0).minute(0).second(0).isAfter(i)&&(n=' disabled="disabled" class="disabled"'),12<=e.hour()?a+='<option value="AM"'+c+'>AM</option><option value="PM" selected="selected"'+n+">PM</option>":a+='<option value="AM" selected="selected"'+c+'>AM</option><option value="PM"'+n+">PM</option>",a+="</select>"),this.container.find(".drp-calendar."+t+" .calendar-time").html(a)}},updateFormInputs:function(){this.singleDatePicker||this.endDate&&(this.startDate.isBefore(this.endDate)||this.startDate.isSame(this.endDate))?this.container.find("button.applyBtn").removeAttr("disabled"):this.container.find("button.applyBtn").attr("disabled","disabled")},move:function(){var t,e={top:0,left:0},s=W(window).width();this.parentEl.is("body")||(e={top:this.parentEl.offset().top-this.parentEl.scrollTop(),left:this.parentEl.offset().left-this.parentEl.scrollLeft()},s=this.parentEl[0].clientWidth+this.parentEl.offset().left),t="up"==this.drops?this.element.offset().top-this.container.outerHeight()-e.top:this.element.offset().top+this.element.outerHeight()-e.top,this.container.css({top:0,left:0,right:"auto"});var i,a=this.container.outerWidth();this.container["up"==this.drops?"addClass":"removeClass"]("drop-up"),"left"==this.opens?a+(s=s-this.element.offset().left-this.element.outerWidth())>W(window).width()?this.container.css({top:t,right:"auto",left:9}):this.container.css({top:t,right:s,left:"auto"}):"center"==this.opens?(i=this.element.offset().left-e.left+this.element.outerWidth()/2-a/2)<0?this.container.css({top:t,right:"auto",left:9}):i+a>W(window).width()?this.container.css({top:t,left:"auto",right:0}):this.container.css({top:t,left:i,right:"auto"}):(i=this.element.offset().left-e.left)+a>W(window).width()?this.container.css({top:t,left:"auto",right:0}):this.container.css({top:t,left:i,right:"auto"})},show:function(t){this.isShowing||(this._outsideClickProxy=W.proxy(function(t){this.outsideClick(t)},this),W(document).on("mousedown.daterangepicker",this._outsideClickProxy).on("touchend.daterangepicker",this._outsideClickProxy).on("click.daterangepicker","[data-toggle=dropdown]",this._outsideClickProxy).on("focusin.daterangepicker",this._outsideClickProxy),W(window).on("resize.daterangepicker",W.proxy(function(t){this.move(t)},this)),this.oldStartDate=this.startDate.clone(),this.oldEndDate=this.endDate.clone(),this.previousRightTime=this.endDate.clone(),this.updateView(),this.container.show(),this.move(),this.element.trigger("show.daterangepicker",this),this.isShowing=!0)},hide:function(t){this.isShowing&&(this.endDate||(this.startDate=this.oldStartDate.clone(),this.endDate=this.oldEndDate.clone()),this.startDate.isSame(this.oldStartDate)&&this.endDate.isSame(this.oldEndDate)||this.callback(this.startDate.clone(),this.endDate.clone(),this.chosenLabel),this.updateElement(),W(document).off(".daterangepicker"),W(window).off(".daterangepicker"),this.container.hide(),this.element.trigger("hide.daterangepicker",this),this.isShowing=!1)},toggle:function(t){this.isShowing?this.hide():this.show()},outsideClick:function(t){var e=W(t.target);"focusin"==t.type||e.closest(this.element).length||e.closest(this.container).length||e.closest(".calendar-table").length||(this.hide(),this.element.trigger("outsideClick.daterangepicker",this))},showCalendars:function(){this.container.addClass("show-calendar"),this.move(),this.element.trigger("showCalendar.daterangepicker",this)},hideCalendars:function(){this.container.removeClass("show-calendar"),this.element.trigger("hideCalendar.daterangepicker",this)},clickRange:function(t){var t=t.target.getAttribute("data-range-key");(this.chosenLabel=t)==this.locale.customRangeLabel?this.showCalendars():(t=this.ranges[t],this.startDate=t[0],this.endDate=t[1],this.timePicker||(this.startDate.startOf("day"),this.endDate.endOf("day")),this.alwaysShowCalendars||this.hideCalendars(),this.clickApply())},clickPrev:function(t){W(t.target).parents(".drp-calendar").hasClass("left")?(this.leftCalendar.month.subtract(1,"month"),this.linkedCalendars&&this.rightCalendar.month.subtract(1,"month")):this.rightCalendar.month.subtract(1,"month"),this.updateCalendars()},clickNext:function(t){W(t.target).parents(".drp-calendar").hasClass("left")?this.leftCalendar.month.add(1,"month"):(this.rightCalendar.month.add(1,"month"),this.linkedCalendars&&this.leftCalendar.month.add(1,"month")),this.updateCalendars()},hoverDate:function(t){var e,s,a,n,r,o;W(t.target).hasClass("available")&&(e=(s=W(t.target).attr("data-title")).substr(1,1),s=s.substr(3,1),a=(W(t.target).parents(".drp-calendar").hasClass("left")?this.leftCalendar:this.rightCalendar).calendar[e][s],n=this.leftCalendar,r=this.rightCalendar,o=this.startDate,this.endDate||this.container.find(".drp-calendar tbody td").each(function(t,e){var s,i;W(e).hasClass("week")||(s=(i=W(e).attr("data-title")).substr(1,1),i=i.substr(3,1),(i=(W(e).parents(".drp-calendar").hasClass("left")?n:r).calendar[s][i]).isAfter(o)&&i.isBefore(a)||i.isSame(a,"day")?W(e).addClass("in-range"):W(e).removeClass("in-range"))}))},clickDate:function(t){var e,s,i,a,n,r;W(t.target).hasClass("available")&&(e=(s=W(t.target).attr("data-title")).substr(1,1),s=s.substr(3,1),s=(W(t.target).parents(".drp-calendar").hasClass("left")?this.leftCalendar:this.rightCalendar).calendar[e][s],this.endDate||s.isBefore(this.startDate,"day")?(this.timePicker&&(i=parseInt(this.container.find(".left .hourselect").val(),10),this.timePicker24Hour||("PM"===(a=this.container.find(".left .ampmselect").val())&&i<12&&(i+=12),"AM"===a&&12===i&&(i=0)),n=parseInt(this.container.find(".left .minuteselect").val(),10),isNaN(n)&&(n=parseInt(this.container.find(".left .minuteselect option:last").val(),10)),r=this.timePickerSeconds?parseInt(this.container.find(".left .secondselect").val(),10):0,s=s.clone().hour(i).minute(n).second(r)),this.endDate=null,this.setStartDate(s.clone())):!this.endDate&&s.isBefore(this.startDate)?this.setEndDate(this.startDate.clone()):(this.timePicker&&(i=parseInt(this.container.find(".right .hourselect").val(),10),this.timePicker24Hour||("PM"===(a=this.container.find(".right .ampmselect").val())&&i<12&&(i+=12),"AM"===a&&12===i&&(i=0)),n=parseInt(this.container.find(".right .minuteselect").val(),10),isNaN(n)&&(n=parseInt(this.container.find(".right .minuteselect option:last").val(),10)),r=this.timePickerSeconds?parseInt(this.container.find(".right .secondselect").val(),10):0,s=s.clone().hour(i).minute(n).second(r)),this.setEndDate(s.clone()),this.autoApply&&(this.calculateChosenLabel(),this.clickApply())),this.singleDatePicker&&(this.setEndDate(this.startDate),this.timePicker||this.clickApply()),this.updateView(),t.stopPropagation())},calculateChosenLabel:function(){var t,e=!0,s=0;for(t in this.ranges){if(this.timePicker){var i=this.timePickerSeconds?"YYYY-MM-DD HH:mm:ss":"YYYY-MM-DD HH:mm";if(this.startDate.format(i)==this.ranges[t][0].format(i)&&this.endDate.format(i)==this.ranges[t][1].format(i)){e=!1,this.chosenLabel=this.container.find(".ranges li:eq("+s+")").addClass("active").attr("data-range-key");break}}else if(this.startDate.format("YYYY-MM-DD")==this.ranges[t][0].format("YYYY-MM-DD")&&this.endDate.format("YYYY-MM-DD")==this.ranges[t][1].format("YYYY-MM-DD")){e=!1,this.chosenLabel=this.container.find(".ranges li:eq("+s+")").addClass("active").attr("data-range-key");break}s++}e&&(this.showCustomRangeLabel?this.chosenLabel=this.container.find(".ranges li:last").addClass("active").attr("data-range-key"):this.chosenLabel=null,this.showCalendars())},clickApply:function(t){this.hide(),this.element.trigger("apply.daterangepicker",this)},clickCancel:function(t){this.startDate=this.oldStartDate,this.endDate=this.oldEndDate,this.hide(),this.element.trigger("cancel.daterangepicker",this)},monthOrYearChanged:function(t){var e=W(t.target).closest(".drp-calendar").hasClass("left"),s=this.container.find(".drp-calendar."+(e?"left":"right")),t=parseInt(s.find(".monthselect").val(),10),s=s.find(".yearselect").val();e||(s<this.startDate.year()||s==this.startDate.year()&&t<this.startDate.month())&&(t=this.startDate.month(),s=this.startDate.year()),this.minDate&&(s<this.minDate.year()||s==this.minDate.year()&&t<this.minDate.month())&&(t=this.minDate.month(),s=this.minDate.year()),this.maxDate&&(s>this.maxDate.year()||s==this.maxDate.year()&&t>this.maxDate.month())&&(t=this.maxDate.month(),s=this.maxDate.year()),e?(this.leftCalendar.month.month(t).year(s),this.linkedCalendars&&(this.rightCalendar.month=this.leftCalendar.month.clone().add(1,"month"))):(this.rightCalendar.month.month(t).year(s),this.linkedCalendars&&(this.leftCalendar.month=this.rightCalendar.month.clone().subtract(1,"month"))),this.updateCalendars()},timeChanged:function(t){var e=W(t.target).closest(".drp-calendar"),s=e.hasClass("left"),i=parseInt(e.find(".hourselect").val(),10),a=parseInt(e.find(".minuteselect").val(),10);isNaN(a)&&(a=parseInt(e.find(".minuteselect option:last").val(),10));var n,t=this.timePickerSeconds?parseInt(e.find(".secondselect").val(),10):0;this.timePicker24Hour||("PM"===(e=e.find(".ampmselect").val())&&i<12&&(i+=12),"AM"===e&&12===i&&(i=0)),s?((n=this.startDate.clone()).hour(i),n.minute(a),n.second(t),this.setStartDate(n),this.singleDatePicker?this.endDate=this.startDate.clone():this.endDate&&this.endDate.format("YYYY-MM-DD")==n.format("YYYY-MM-DD")&&this.endDate.isBefore(n)&&this.setEndDate(n.clone())):this.endDate&&((n=this.endDate.clone()).hour(i),n.minute(a),n.second(t),this.setEndDate(n)),this.updateCalendars(),this.updateFormInputs(),this.renderTimePicker("left"),this.renderTimePicker("right")},elementChanged:function(){var t,e,s;this.element.is("input")&&this.element.val().length&&(s=e=null,2===(t=this.element.val().split(this.locale.separator)).length&&(e=T(t[0],this.locale.format),s=T(t[1],this.locale.format)),!this.singleDatePicker&&null!==e&&null!==s||(s=e=T(this.element.val(),this.locale.format)),e.isValid()&&s.isValid()&&(this.setStartDate(e),this.setEndDate(s),this.updateView()))},keydown:function(t){9!==t.keyCode&&13!==t.keyCode||this.hide(),27===t.keyCode&&(t.preventDefault(),t.stopPropagation(),this.hide())},updateElement:function(){var t;this.element.is("input")&&this.autoUpdateInput&&(t=this.startDate.format(this.locale.format),this.singleDatePicker||(t+=this.locale.separator+this.endDate.format(this.locale.format)),t!==this.element.val()&&this.element.val(t).trigger("change"))},remove:function(){this.container.remove(),this.element.off(".daterangepicker"),this.element.removeData()}},W.fn.daterangepicker=function(t,e){var s=W.extend(!0,{},W.fn.daterangepicker.defaultOptions,t);return this.each(function(){var t=W(this);t.data("daterangepicker")&&t.data("daterangepicker").remove(),t.data("daterangepicker",new i(t,s,e))}),this},i});