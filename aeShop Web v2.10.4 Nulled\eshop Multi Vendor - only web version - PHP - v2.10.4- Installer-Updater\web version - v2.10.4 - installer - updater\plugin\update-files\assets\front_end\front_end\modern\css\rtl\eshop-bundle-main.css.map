{"version": 3, "sources": ["star-rating.min.css", "custom.css", "products.css"], "names": [], "mappings": "AAAA;;;;;;;;;EASA,CACA,gBAKA,gDAAA,CACA,WAAA,CAFA,UAAA,CADA,WAAA,CADA,WAAA,CADA,UAMA,CAEA,gCAEA,cAAA,CAEA,oBAAA,CACA,eAAA,CAJA,iBAAA,CAEA,qBAAA,CAGA,kBACA,CAEA,sCACA,kBACA,CAEA,cASA,cAAA,CADA,WAAA,CAHA,QAAA,CAHA,cAAA,CADA,gBAAA,CAMA,aAAA,CAHA,UAAA,CAEA,MAAA,CAMA,QAAA,CAFA,SAAA,CACA,SAAA,CARA,UAUA,CAEA,iDAIA,oBAAA,CAFA,aAAA,CACA,iBAEA,CAEA,gDACA,cACA,CAEA,+BACA,kBACA,CAEA,wBACA,oBAAA,CACA,YAAA,CACA,iBACA,CAEA,+BACA,UACA,CAEA,gCAQA,4BAAA,CAHA,aAAA,CAHA,MAAA,CAEA,WAAA,CAGA,eAAA,CANA,iBAAA,CAQA,wBAAA,CANA,KAAA,CAGA,kBAIA,CAEA,YACA,WACA,CAEA,8BACA,0BACA,CAEA,0BACA,SAAA,CACA,OAAA,CAGA,8BAAA,CAFA,eAGA,CAEA,kCACA,WACA,CAEA,kCACA,kBACA,CAEA,gCACA,UAAA,CACA,kBAAA,CACA,oBAAA,CAEA,aAAA,CACA,iBAAA,CAFA,qBAGA,CAEA,qBACA,wBACA,CAEA,2BACA,aACA,CAEA,kCACA,oBAAA,CACA,kBAAA,CACA,uBACA,CAEA,2BACA,UAAA,CACA,oBAAA,CAEA,aAAA,CACA,eAAA,CACA,cAAA,CAHA,qBAIA,CAEA,qBAEA,aAAA,CADA,gBAEA,CAEA,aACA,gCACA,YACA,CACA,CAEA,WACA,cACA,CAEA,WACA,cACA,CAEA,WACA,cACA,CAEA,WACA,cACA,CAEA,WACA,cACA,CAEA,oBACA,cACA,CAEA,oBACA,cACA,CAEA,oBACA,cACA,CAEA,oBACA,cACA,CAEA,oBACA,cACA,CAEA,eAMA,UAAA,CAJA,oBAAA,CADA,sCAAA,CAGA,eAAA,CACA,eAAA,CAFA,mBAAA,CAKA,uBAAA,CADA,kBAEA,CAEA,mBACA,wBACA,CAEA,gBACA,wBACA,CAEA,iBACA,wBAAA,CACA,aACA,CAEA,cACA,wBAAA,CACA,aACA,CAEA,iBACA,wBACA,CAEA,iBACA,wBACA,CC3NA,KAKA,QAAA,CACA,SACA,CAYA,YAEA,kBAAA,CAMA,eAAA,CADA,QAAA,CANA,mBAAA,CAEA,sBAAA,CAEA,sBAAA,CACA,iBAAA,CAFA,uBAKA,CAEA,iCAEA,eAAA,CADA,cAEA,CAEA,uBACA,yBACA,CAEA,+JAIA,+CAAA,CACA,2CAAA,CAFA,UAGA,CAEA,mGAOA,2BAAA,CAEA,sCAAA,CAJA,gCAKA,CAEA,uIAIA,+CAAA,CACA,2CAAA,CAFA,UAGA,CAEA,sCAEA,yBACA,CAEA,cAGA,eAAA,CADA,cAAA,CADA,iBAIA,CAMA,qDACA,YACA,CAEA,2BAEA,iBAAA,CADA,YAEA,CAEA,gBACA,mBACA,CAGA,8BAEA,iBAAA,CADA,eAAA,CAEA,cACA,CAEA,0BACA,sBAAA,CACA,qBACA,CAEA,qBACA,wBACA,CAEA,8BAEA,qBACA,CAEA,kCAGA,iBAAA,CAFA,eAAA,CACA,cAEA,CAEA,iBAEA,sBAAA,CADA,qBAEA,CAEA,qBAGA,eAAA,CADA,cAAA,CADA,mBAAA,CAAA,gBAGA,CAEA,+BACA,mBACA,CAMA,yCACA,2BACA,CAMA,gBACA,4BACA,CAEA,WACA,gBAAA,CACA,gBAEA,CAEA,eAEA,WAAA,CADA,UAEA,CAgBA,gCAEA,eAAA,CADA,cAEA,CAgBA,yCAGA,oBAAA,CADA,YAAA,CADA,OAGA,CAEA,WACA,sBAAA,CACA,wBAAA,CACA,oBACA,CAMA,iCACA,YACA,CAGA,QACA,eAAA,CACA,+BAAA,CAIA,gBAAA,CAFA,WAGA,CAEA,qBAJA,UAAA,CAFA,WAYA,CANA,aAEA,aAAA,CAGA,iBAAA,CAJA,SAKA,CAEA,uCAIA,wBAAA,CAFA,UAAA,CAGA,UAAA,CAGA,QAAA,CAEA,gBAAA,CADA,eAAA,CANA,iBAAA,CAIA,OAAA,CADA,UAKA,CAEA,wBACA,uBACA,CAEA,cAIA,WAAA,CAGA,aAAA,CANA,UAAA,CAKA,eAAA,CAHA,WAAA,CAEA,iBAAA,CAHA,SAMA,CAEA,oBACA,UAAA,CACA,YACA,CAGA,2CAEA,UAAA,CADA,WAAA,CAGA,iBAAA,CADA,UAEA,CAEA,2CAEA,UAAA,CADA,WAAA,CAGA,iBAAA,CADA,UAEA,CAEA,2CAEA,UAAA,CADA,WAAA,CAGA,iBAAA,CADA,UAEA,CAEA,2CAEA,UAAA,CADA,WAAA,CAGA,iBAAA,CADA,UAEA,CAEA,2CAEA,UAAA,CADA,WAAA,CAGA,iBAAA,CADA,UAEA,CAEA,2CAEA,UAAA,CADA,WAAA,CAGA,iBAAA,CADA,UAEA,CAEA,2CAEA,UAAA,CADA,WAAA,CAGA,iBAAA,CADA,UAEA,CAEA,sCACA,+BACA,CAEA,0BACA,2DAKA,eAAA,CACA,yCAAA,CAHA,UAAA,CACA,UAAA,CAFA,iBAAA,CAKA,UAAA,CANA,UAAA,CAOA,SACA,CAEA,CAEA,cAEA,iBACA,CAEA,cAEA,gBACA,CAMA,cACA,cACA,CAEA,wBACA,mBACA,CAEA,8CACA,OACA,CAGA,kCACA,oBAEA,CAEA,UAEA,YACA,CAEA,oBAEA,eAAA,CADA,cAAA,CAEA,mBAAA,CAAA,gBACA,CAEA,YACA,qBAAA,CACA,kBACA,CAEA,cACA,eACA,CAEA,YAMA,kBAAA,CALA,wBAAA,CACA,mBAAA,CAGA,YAAA,CADA,sBAAA,CADA,mBAIA,CAEA,gBACA,oBACA,CAEA,kBAEA,0BAAA,CADA,wBAEA,CAGA,sCACA,SACA,CAGA,4CACA,iBACA,CAGA,4CACA,kBAAA,CACA,qBAAA,CACA,iBACA,CAEA,yCACA,YACA,CAEA,eAEA,eAAA,CADA,cAEA,CAEA,WACA,YACA,CAOA,WACA,YAEA,CAEA,eAEA,eAAA,CADA,cAEA,CAEA,eAIA,oBAAA,CADA,YAAA,CADA,YAAA,CAGA,sBAAA,CACA,eAAA,CALA,oBAMA,CAOA,cACA,QACA,CAGA,gBAEA,YAAA,CACA,qBAAA,CAFA,iBAGA,CAEA,UAEA,sBAAA,CADA,qBAEA,CAEA,cAEA,yBAAA,CADA,wBAEA,CAEA,cAEA,qBAAA,CADA,mBAEA,CAEA,kBAEA,yBAAA,CADA,wBAEA,CAEA,4CAEA,4BACA,CAEA,aAEA,sBACA,CAEA,iBAEA,yBAAA,CADA,wBAEA,CAEA,qBAGA,kBAAA,CAFA,wBAAA,CACA,YAEA,CAEA,YAEA,qBAAA,CADA,oBAEA,CAEA,gBAEA,yBAAA,CADA,wBAEA,CAMA,eACA,YAEA,CAEA,mBAEA,yBAAA,CADA,wBAEA,CAEA,oBAEA,WAAA,CADA,WAEA,CAEA,wBAEA,eAAA,CADA,cAEA,CAEA,0BACA,oBACA,CAEA,aACA,cACA,CAEA,gBAIA,kBAAA,CADA,YAAA,CAFA,YAAA,CAIA,sBAAA,CAHA,WAIA,CAEA,oBACA,eAAA,CACA,cACA,CAEA,iBAKA,kBAAA,CAHA,kBAAA,CADA,iBAAA,CAKA,iBAAA,CACA,cAAA,CAHA,YAAA,CAIA,6BAAA,CALA,cAMA,CAMA,wBACA,cACA,CAEA,gBACA,YACA,CAGA,mBACA,gBAAA,CACA,iBACA,CAEA,sCACA,SACA,CAEA,4CACA,iBACA,CAEA,4CACA,kBAAA,CACA,qBAAA,CACA,iBACA,CAGA,cACA,gBAAA,CACA,iBACA,CAEA,YACA,0BAAA,CAEA,yBAAA,CADA,gCAEA,CAEA,iCACA,SACA,CAEA,uCACA,iBACA,CAEA,uCACA,kBAAA,CACA,qBAAA,CACA,iBACA,CAGA,wBAIA,QAAA,CAFA,SAAA,CADA,iBAAA,CAEA,OAEA,CAEA,WACA,qBAAA,CACA,oBACA,CAGA,4BACA,cAAA,CACA,eAAA,CACA,cAAA,CACA,qBAAA,CAAA,kBACA,CAOA,oDACA,sCACA,CAEA,mBACA,qCAAA,CACA,gLACA,CAGA,iBACA,gBAAA,CACA,iBACA,CAEA,oCACA,SACA,CAEA,0CACA,iBACA,CAEA,0CACA,kBAAA,CACA,qBAAA,CACA,iBACA,CAIA,mCAGA,eAAA,CADA,cAAA,CAEA,aAAA,CAHA,cAMA,CAEA,0BACA,gBAAA,CACA,aACA,CAEA,2BAEA,kBAAA,CADA,eAEA,CAEA,mCACA,UAAA,CAEA,WAAA,CACA,iBAAA,CACA,2BAAA,CAHA,UAIA,CAEA,iCACA,UAAA,CAEA,WAAA,CACA,iBAAA,CACA,2BAAA,CAHA,UAIA,CAEA,uCAGA,iBAAA,CADA,eAAA,CADA,cAGA,CAEA,iCACA,gBACA,CAEA,sCACA,aACA,CAEA,kCACA,oBAAA,CACA,eAAA,CACA,iBACA,CAEA,yEAEA,gBACA,CAEA,8GAIA,UAAA,CADA,oBAAA,CAEA,cACA,CAEA,wCAEA,UAAA,CADA,cAAA,CAEA,cACA,CAEA,sCACA,mBACA,CAEA,wEACA,aAAA,CACA,UAAA,CACA,eACA,CAEA,8SAIA,aACA,CAOA,6BACA,8BAAA,CACA,8BAAA,CAEA,4BACA,CAEA,gCACA,aACA,CAEA,8BACA,oBACA,CAQA,kCACA,kCACA,CAEA,0BACA,eACA,CAEA,kBAEA,kCAAA,CACA,eAAA,CAFA,YAGA,CAEA,iDACA,iBAAA,CACA,WACA,CAEA,yBACA,UAAA,CACA,eACA,CAEA,yCACA,kCAEA,CAIA,8CACA,oBACA,CAMA,iGACA,oCACA,CAIA,0BACA,2BACA,WACA,CAEA,0CACA,WACA,CAEA,UAEA,qBACA,CAMA,qCACA,YACA,CACA,CAGA,4BAEA,mDAEA,sCAAA,CACA,uCAAA,CACA,qBACA,CAMA,CAGA,mDACA,2BACA,WACA,CAEA,0CACA,WACA,CAEA,UAEA,qBACA,CAMA,qCACA,YACA,CACA,CAEA,yBACA,0CACA,WACA,CAMA,oCACA,YACA,CAEA,+CACA,qBACA,CACA,CAGA,oCASA,uDACA,YACA,CACA,CAEA,oCACA,aACA,mBACA,CACA,CAEA,UAEA,SAAA,CADA,QAEA,CAIA,yCACA,0CACA,YACA,CAEA,WACA,sBACA,CAEA,MACA,sBACA,CAEA,OACA,yBACA,CAMA,+BACA,wBAAA,CACA,yBACA,CAMA,0DACA,wBACA,CAEA,+CACA,qBACA,CAEA,yBACA,gBACA,CAEA,4CACA,yBACA,CAEA,UAEA,oBAAA,CADA,mBAEA,CAEA,6BACA,YACA,CAMA,oBACA,cACA,CAEA,oBACA,wBACA,CAEA,wBAEA,sBACA,CAEA,0CACA,qBAEA,CAMA,6CACA,YACA,CAEA,UAEA,qBACA,CAOA,2BAEA,iBAAA,CADA,WAEA,CACA,CAEA,yCACA,kCACA,YACA,CAEA,CAEA,yBACA,yBACA,sBACA,CACA,CAKA,yCASA,+BACA,YACA,CACA,CAIA,yCAKA,+BACA,YACA,CAKA,CAIA,yCAKA,0DACA,YACA,CAKA,UAEA,qBACA,CAEA,CAEA,0CAEA,uBACA,YACA,CAEA,WACA,YACA,CAEA,2BAEA,iBAAA,CADA,YAEA,CAMA,CAEA,6BACA,oCACA,CAEA,aAEA,WAAA,CACA,SAAA,CAFA,cAAA,CAGA,YACA,CAIA,cACA,0BAAA,CACA,cACA,CAEA,iBAGA,qCAAA,CACA,WAAA,CAKA,gBAAA,CAJA,cAAA,CACA,OAAA,CACA,WAAA,CACA,YAEA,CAEA,gDAXA,sCAaA,CAEA,0CACA,sCAAA,CAOA,2BAAA,CANA,WAAA,CAKA,WAAA,CAJA,iBAAA,CACA,WAAA,CACA,QAAA,CACA,UAGA,CAEA,gCAIA,eAAA,CAHA,4BAAA,CACA,eAAA,CACA,SAEA,CAEA,kBACA,oBACA,CAEA,qEAEA,eAAA,CADA,cAEA,CAEA,qCAKA,iBAAA,CAJA,cAAA,CACA,aAAA,CACA,WAAA,CAGA,UAAA,CAFA,UAGA,CAEA,sCACA,wBACA,CAEA,sCACA,wBACA,CAEA,yCACA,wBACA,CAEA,uCACA,wBACA,CAEA,uCACA,wBACA,CAGA,sCACA,wBACA,CAEA,sCACA,wBACA,CAEA,wCACA,wBACA,CAEA,sCACA,wBACA,CAEA,wCACA,wBACA,CAEA,qCACA,wBACA,CAEA,qCACA,wBACA,CAEA,wCACA,wBACA,CAUA,yLACA,YACA,CAEA,eAGA,WAAA,CAGA,WAAA,CAJA,SAAA,CADA,cAAA,CAIA,UAAA,CADA,YAIA,CAEA,oBACA,mBACA,CAEA,wBAEA,eAAA,CADA,cAEA,CAIA,gBACA,sBACA,CAEA,UAEA,qBAAA,CACA,gBACA,CAEA,eAGA,oBAAA,CAFA,YAAA,CAIA,YAAA,CAHA,sBAAA,CAEA,WAEA,CAEA,mBAEA,eAAA,CADA,cAEA,CAEA,6BACA,oBACA,CAEA,UACA,qBACA,CAEA,iBACA,kCAAA,CACA,sCACA,CAEA,kBACA,kCAAA,CACA,sCACA,CAEA,gDAOA,MAEA,CAEA,iGAVA,kBAAA,CAEA,iBAAA,CAEA,aAAA,CAGA,cAAA,CANA,eAAA,CAEA,iBAAA,CAEA,SAcA,CATA,iDAOA,OAEA,CAEA,mBAKA,kBAAA,CADA,WAAA,CAOA,iBAAA,CARA,UASA,CAEA,oCAZA,WAAA,CADA,iBAAA,CASA,UAAA,CAFA,cAAA,CACA,eAAA,CAFA,gBAAA,CADA,iBAmBA,CAXA,iBAKA,kBAAA,CADA,WAAA,CADA,UAQA,CAEA,+CAYA,gCAAA,CAXA,UAYA,CAEA,0DAYA,sBAAA,CAXA,WAYA,CASA,2DAEA,UAAA,CADA,iBAAA,CAGA,SAAA,CADA,KAEA,CASA,+DALA,UAAA,CAEA,QAAA,CAHA,iBAAA,CAEA,KASA,CAEA,gBACA,aACA,CAEA,iBAEA,aAAA,CADA,cAEA,CAEA,iBACA,cACA,CAgBA,2DAKA,aAAA,CAFA,kDAQA,CAEA,8EAPA,qBAAA,CACA,iBAAA,CAEA,UAAA,CAPA,YAAA,CAQA,UAAA,CAFA,YAAA,CAPA,iBAuBA,CAXA,mBAKA,aAAA,CAFA,yDAQA,CAEA,mCAEA,qBAAA,CACA,iBAAA,CAEA,UAAA,CACA,UAAA,CAFA,WAAA,CAHA,iBAMA,CAGA,kCACA,YACA,CAEA,kCAEA,qBAAA,CACA,iBAAA,CAEA,UAAA,CACA,UAAA,CAFA,WAAA,CAHA,iBAMA,CAEA,iCACA,YACA,CAWA,+EACA,kBAAA,CAGA,iBAAA,CAFA,UAAA,CAGA,YAAA,CACA,kBAAA,CAHA,WAIA,CAMA,qFACA,aACA,CAqBA,uEAOA,WAAA,CAFA,iBAAA,CAQA,kCAAA,CAPA,oBAAA,CALA,cAAA,CAaA,YAAA,CAVA,WAAA,CAFA,WAAA,CAMA,iBAAA,CAEA,OAAA,CADA,QAAA,CAGA,8BAAA,CATA,UAAA,CAYA,SACA,CAEA,mCAIA,uBAAA,CAHA,iBAAA,CAEA,UAAA,CADA,OAGA,CAEA,oCAIA,uBAAA,CAHA,iBAAA,CAEA,UAAA,CADA,OAGA,CAEA,yCACA,uBACA,CAEA,0CACA,uBACA,CAEA,4BACA,uBACA,CAEA,+BAEA,uBAAA,CADA,0BAEA,CAEA,8BAEA,oBAAA,CADA,0BAEA,CAEA,2BACA,qDACA,CAEA,iCACA,uBACA,CAEA,0BACA,kCACA,CAEA,+BACA,uBACA,CAEA,kCACA,kCAAA,CACA,kCAAA,CACA,8BAAA,CAEA,qBAAA,CADA,0BAAA,CAEA,4BACA,CAOA,gFAJA,kCAAA,CACA,kCAOA,CAJA,wCAGA,uBACA,CAEA,mCACA,8BAAA,CAEA,qBAAA,CADA,0BAAA,CAEA,4BACA,CAEA,yCACA,wBAAA,CACA,oBACA,CAEA,kCACA,kCAAA,CACA,8BACA,CAEA,aAEA,sBAAA,CADA,yBAEA,CAGA,gCACA,SACA,CAGA,sCACA,iBACA,CAGA,sCACA,kBAAA,CACA,qBAAA,CACA,iBACA,CAEA,yBAEA,kBAAA,CAEA,uBAAA,CAHA,yBAAA,CAEA,gBAEA,CAEA,+BACA,4BAAA,CACA,uBACA,CAEA,0BAEA,kBAAA,CAEA,uBAAA,CAHA,yBAAA,CAEA,gBAEA,CAEA,gCACA,4BAAA,CACA,uBACA,CAEA,8BAEA,aAAA,CADA,eAAA,CAEA,gBACA,CAQA,6CACA,kBAAA,CAEA,iBAAA,CADA,eAEA,CAEA,oCACA,eAAA,CACA,cACA,CAEA,+BAEA,aAAA,CADA,eAAA,CAEA,gBACA,CAEA,qCACA,eAAA,CACA,cACA,CAEA,6BACA,uBAEA,CAEA,8BACA,uBACA,CAEA,gCACA,uBACA,CAEA,iCACA,uBACA,CAEA,+BACA,kCAAA,CACA,kCAAA,CACA,2BAAA,CACA,uBAAA,CACA,qBAAA,CACA,8BACA,CAEA,gCACA,kCAAA,CACA,2BAAA,CACA,qBAAA,CACA,8BACA,CAEA,kBAIA,yBAAA,CAHA,yBAAA,CACA,sBAAA,CACA,yBAEA,CAEA,qBACA,wBAAA,CACA,eACA,CAEA,8BACA,yBAAA,CAEA,yBAAA,CADA,0BAEA,CAEA,gBAGA,gBAAA,CADA,0BAAA,CADA,uBAGA,CAEA,iBAGA,sBAAA,CAFA,gBAAA,CACA,qBAAA,CAEA,wBAAA,CACA,oBACA,CAGA,cAOA,kBAAA,CAHA,WAAA,CAEA,YAAA,CAHA,gBAAA,CAEA,eAAA,CAJA,gBAAA,CAOA,iBACA,CAEA,yCAIA,+BAAA,CAFA,UAAA,CACA,MAEA,CAEA,qBACA,kBACA,CAEA,oBACA,iBACA,CAGA,aAOA,kBAAA,CAHA,WAAA,CAEA,YAAA,CAGA,YAAA,CANA,gBAAA,CAEA,eAAA,CAJA,gBAAA,CAOA,iBAEA,CAEA,uCAIA,+BAAA,CAFA,UAAA,CACA,MAEA,CAEA,oBACA,kBACA,CAEA,mBACA,iBACA,CAGA,4BACA,mCAAA,CAGA,qCAAA,CACA,2BAAA,CAFA,WAAA,CAGA,WAAA,CAJA,iBAKA,CAEA,YAIA,wBAAA,CAFA,iBAAA,CAGA,UAAA,CAFA,WAAA,CAKA,aAAA,CACA,eAAA,CACA,aAAA,CAGA,cAAA,CANA,gBAAA,CAIA,iBAAA,CAVA,qBAAA,CAWA,kBAEA,CAEA,wCAGA,oCAAA,CADA,+BAEA,CAEA,aACA,kBACA,CAEA,aAOA,WAAA,CAJA,WAAA,CAFA,YAAA,CAKA,YAAA,CAJA,cAAA,CAEA,UAAA,CACA,WAAA,CAGA,WACA,CAEA,YAGA,WAAA,CAFA,cAAA,CACA,UAAA,CAEA,aACA,CAEA,eACA,yBACA,CAEA,aAMA,wBAAA,CAGA,iBAAA,CAFA,UAAA,CAIA,cAAA,CAGA,YAAA,CANA,cAAA,CAOA,sBAAA,CAVA,gBAAA,CAJA,+BAAA,CASA,WAMA,CAGA,WACA,qBACA,CAGA,oBACA,WACA,CAEA,qBACA,iBAAA,CAGA,YAAA,CAFA,WAIA,CAEA,yBAIA,iBAAA,CAFA,yBAAA,CADA,wBAAA,CAEA,6BAAA,CAAA,0BAEA,CAEA,0BACA,qCAEA,CAEA,8CACA,YACA,CAEA,2BAEA,0BAAA,CACA,yBAAA,CACA,yBAAA,CACA,6BACA,CAEA,QACA,wBAAA,CACA,qBAAA,CACA,oBACA,CAEA,eAGA,gBAAA,CAFA,2BAAA,CACA,eAEA,CAEA,gBAOA,yBAAA,CACA,uBAAA,CACA,yBAAA,CACA,6BAAA,CAMA,sBAAA,CACA,6BAAA,CACA,yBAAA,CACA,yBAAA,CACA,mCAAA,CACA,oCAAA,CACA,0BAAA,CACA,0BAAA,CACA,oCAAA,CACA,8DAAA,CACA,+BAAA,CACA,gCAAA,CACA,sCAAA,CACA,kBAAA,CAEA,sBAAA,CACA,wBAAA,CACA,wBAAA,CAEA,uBAAA,CACA,yBAAA,CAGA,yBAAA,CACA,8BAAA,CACA,+EAAA,CAvCA,kBAAA,CAyDA,iCAAA,CAFA,kEAAA,CACA,yCAAA,CAEA,mCAAA,CAZA,yBAAA,CA/CA,mBAAA,CA4CA,iCAAA,CACA,qCAAA,CA3CA,sBAAA,CAEA,sBAAA,CA0CA,qCAAA,CAJA,uDAAA,CArCA,iBAAA,CA2CA,iBAAA,CA7CA,uBAAA,CAyDA,8BAAA,CAVA,qBAAA,CADA,kBAYA,CAEA,sBAEA,uCAAA,CACA,6CAAA,CAFA,+BAGA,CAEA,kCACA,oBACA,CAEA,eACA,YAAA,CACA,WACA,CAEA,mBAEA,eAAA,CADA,cAEA,CAEA,mBACA,YAAA,CACA,WACA,CAEA,uBAEA,eAAA,CADA,cAEA,CAEA,mBACA,oBACA,CAEA,kCACA,4BACA,CAEA,kCAEA,4BAAA,CADA,yBAEA,CAEA,YAEA,SAAA,CADA,iBAEA,CAEA,uBACA,sBAAA,CAEA,mBAAA,CAAA,gBAAA,CADA,qBAEA,CAEA,wFAGA,sBAAA,CADA,qBAEA,CAEA,kDACA,o4BACA,CAEA,iDACA,w2BACA,CAEA,cAKA,qBAAA,CACA,2BAAA,CACA,4BAAA,CALA,QAAA,CAMA,qCAAA,CALA,MAAA,CAFA,cAAA,CAGA,OAAA,CAKA,0BAAA,CACA,iCAAA,CACA,YACA,CAEA,mBACA,uBACA,CAEA,UACA,qBAAA,CAAA,kBACA,CAEA,WACA,mBAAA,CAAA,gBACA,CAEA,wBACA,WACA,CAEA,mBACA,eACA,CAEA,YACA,WACA,CAEA,sBAEA,sBAAA,CADA,qBAAA,CAAA,kBAAA,CAEA,sBAAA,CAAA,iBACA,CAEA,oBAEA,iBAAA,CADA,WAEA,CAEA,iBACA,YACA,CAEA,aAEA,iBAAA,CADA,WAEA,CAEA,gBACA,sBACA,CAEA,cACA,eAAA,CAEA,QAAA,CAEA,gBAAA,CAEA,iBAAA,CAHA,SAKA,CAEA,iCACA,SACA,CAEA,uCACA,iBACA,CAEA,uCACA,kBAAA,CACA,qBAAA,CACA,iBACA,CAEA,iBACA,aACA,CAEA,MACA,mBACA,CAEA,MACA,WAEA,CAEA,kBACA,UAAA,CAEA,oBACA,CAMA,OACA,UAAA,CACA,iBACA,CAEA,6BAEA,eAAA,CACA,sBAAA,CAFA,kBAGA,CAKA,kBAEA,kBAAA,CACA,wBAAA,CAFA,mBAAA,CAGA,UAAA,CACA,mBAAA,CACA,gBAAA,CACA,iBAAA,CAIA,sBAAA,CAAA,iBAHA,CAMA,yBACA,WACA,CAEA,uBACA,aACA,CAEA,kBACA,mBACA,CAEA,yBACA,iBACA,CAEA,sBACA,YACA,CAEA,8CACA,kBAAA,CACA,oBAAA,CACA,UACA,CAEA,kBACA,eACA,CAEA,uBACA,aACA,CAEA,oBACA,qBAAA,CACA,kBAAA,CACA,YAAA,CACA,cACA,CACA,0BACA,kCACA,CACA,wBAEA,WAAA,CACA,qBAAA,CAAA,kBAAA,CAFA,UAGA,CAEA,gBACA,cACA,CCzyEA,kCAEA,aAAA,CADA,cAAA,CAEA,eAAA,CACA,aACA,CAEA,4BACA,WAAA,CACA,cACA,CAEA,qCACA,oBACA,CAEA,wBAEA,YAAA,CACA,cAAA,CAEA,iBAAA,CACA,qBAEA,CACA,4BAEA,eAAA,CADA,cAEA,CACA,yBAEA,YAAA,CAIA,iBAAA,CAHA,WAAA,CACA,iBAAA,CACA,qBAAA,CAJA,WAMA,CAEA,cAIA,+BAAA,CAHA,8BAAA,CAOA,gBAAA,CACA,iBAAA,CAJA,eAAA,CAFA,gBAAA,CAGA,iBAAA,CAJA,iBAAA,CAKA,SAGA,CAEA,6BAIA,WAAA,CAHA,iBAAA,CACA,0BAAA,CACA,UAEA,CAEA,yBACA,wBACA,CAEA,iCAEA,eAAA,CADA,cAEA,CAEA,gCACA,UACA,CAEA,4BAaA,kBAAA,CARA,YAAA,CAIA,sBAKA,CAEA,qBACA,SAAA,CACA,8BACA,CAEA,2BACA,SACA,CAEA,qBAIA,MAAA,CAHA,SAAA,CACA,iBAAA,CACA,KAAA,CAEA,8BACA,CAEA,2BACA,SACA,CAEA,sBASA,QAAA,CALA,eAAA,CADA,QAAA,CAEA,SAAA,CAHA,SAAA,CAKA,iBAAA,CACA,OAAA,CAFA,2CAAA,CAKA,0BAAA,CAVA,WAAA,CASA,SAEA,CAEA,4BACA,SAAA,CACA,OACA,CAEA,yBACA,oBACA,CAEA,2BAEA,qBAAA,CADA,UAAA,CAQA,aAAA,CANA,cAAA,CAGA,WAAA,CAFA,gBAAA,CAIA,YAAA,CAEA,iBAAA,CALA,iBAAA,CAMA,8BAAA,CAJA,UAKA,CAEA,iCAEA,uCAAA,CADA,iCAEA,CAEA,mEAIA,qBAAA,CADA,UAAA,CADA,sBAAA,CAGA,cAAA,CAQA,QAAA,CAPA,kBAAA,CACA,gBAAA,CAGA,SAAA,CAFA,eAAA,CAIA,iBAAA,CAEA,SAAA,CAHA,0BAAA,CAFA,kBAMA,CAEA,iCAIA,eAAA,CAHA,UAAA,CACA,WAAA,CAIA,SAAA,CADA,wCAAA,CAFA,UAAA,CAIA,UACA,CAEA,+EAEA,SACA,CAEA,uEAGA,wBAAA,CADA,UAAA,CAKA,aAAA,CAHA,cAAA,CAMA,MAAA,CAJA,eAAA,CAEA,iBAAA,CAHA,wBAAA,CAIA,QAEA,CAEA,sCACA,qBAAA,CACA,SAAA,CACA,MAAA,CACA,QACA,CAEA,kBAGA,iBAAA,CAFA,OAAA,CACA,QAEA,CAEA,sBACA,aAAA,CACA,cAAA,CAGA,eAAA,CADA,QAAA,CADA,mBAAA,CAGA,iBAAA,CACA,UACA,CAEA,iCACA,oBACA,CAEA,+BACA,qBAAA,CAOA,UAAA,CAFA,MAAA,CAFA,aAAA,CADA,eAAA,CAEA,iBAAA,CAEA,OAAA,CALA,iBAAA,CAQA,kBAAA,CADA,SAEA,CAEA,qCACA,UACA,CAEA,qBACA,cAAA,CACA,eAAA,CACA,mBAAA,CAOA,oBAAA,CAHA,eAAA,CAEA,gBAAA,CADA,sBAAA,CAJA,yBAAA,CAEA,0BAKA,CAEA,SACA,aACA,CAMA,0DACA,aACA,CAEA,qBAIA,eAAA,CAEA,iBAAA,CAIA,kBAAA,CADA,eAAA,CAFA,iBAIA,CAEA,sCAZA,UAAA,CAEA,iCAAA,CADA,cAAA,CAGA,mBAAA,CAGA,kBAaA,CARA,iBAIA,eAAA,CAEA,eAEA,CAEA,yCAEA,SAAA,CAKA,oBAAA,CAJA,cAAA,CACA,eAAA,CAEA,eAAA,CADA,4BAGA,CAEA,aAKA,kBAAA,CAJA,iCAAA,CACA,cAAA,CACA,eAAA,CACA,gBAEA,CAKA,mFAGA,qBAAA,CAMA,mCAAA,CADA,eAAA,CADA,iBAAA,CADA,OAAA,CADA,KAAA,CADA,SAMA,CAEA,wGAOA,eAAA,CAJA,aAAA,CACA,eAAA,CACA,iBAAA,CACA,iBAEA,CAKA,sCACA,YACA,CAEA,+DACA,YACA,CAKA,iBACA,WAAA,CACA,6BACA,CAEA,+BACA,wBACA,CAEA,wBACA,SACA,CAEA,gCACA,YACA,CAEA,0CAEA,YAAA,CAGA,iBAAA,CAFA,WAAA,CAFA,eAAA,CAGA,iBAEA,CAEA,iCACA,cACA,CAEA,wCAGA,wBAAA,CAFA,WAAA,CACA,QAEA,CAEA,uDACA,UACA,CAEA,iEACA,WAAA,CACA,cACA,CAEA,uDACA,UACA,CAKA,iBACA,sCAAA,CACA,eACA,CAEA,+BACA,+BAAA,CACA,UACA,CAEA,iCACA,SACA,CAEA,iCACA,cACA,CAEA,gCAGA,gBAAA,CACA,iBAAA,CAFA,mBAAA,CADA,gBAIA,CAEA,gCACA,YACA,CAEA,0CACA,YACA,CAaA,wEAHA,kBAAA,CAFA,gBAAA,CACA,iBAWA,CAPA,6CAKA,oBAAA,CAHA,mBAAA,CADA,gBAMA,CAEA,yCACA,iBACA,CAEA,mCACA,cACA,CAEA,yCACA,cACA,kBACA,CACA,+BACA,yBACA,CACA,8CACA,gBACA,CACA,CAEA,yCACA,cACA,kBACA,CACA,CAKA,4BACA,UACA,CAEA,mCAEA,kCAAA,CADA,wBAEA,CAEA,mEAGA,kBAAA,CADA,aAEA,CAEA,+EAEA,wBACA,CAEA,+BACA,aACA", "file": "eshop-bundle-main.css", "sourcesContent": ["/*!\r\n * bootstrap-star-rating v4.1.2\r\n * http://plugins.krajee.com/star-rating\r\n *\r\n * Author: <PERSON><PERSON><PERSON>\r\n * Copyright: 2013 - 2021, <PERSON><PERSON><PERSON>, Krajee.com\r\n *\r\n * Licensed under the BSD 3-Clause\r\n * https://github.com/kartik-v/bootstrap-star-rating/blob/master/LICENSE.md\r\n */\r\n.rating-loading {\r\n    width: 25px;\r\n    height: 25px;\r\n    font-size: 0;\r\n    color: #fff;\r\n    background: url(../img/loading.gif) top left no-repeat;\r\n    border: none\r\n}\r\n\r\n.rating-container .rating-stars {\r\n    position: relative;\r\n    cursor: pointer;\r\n    vertical-align: middle;\r\n    display: inline-block;\r\n    overflow: hidden;\r\n    white-space: nowrap\r\n}\r\n\r\n.rating-container .rating-stars:focus {\r\n    outline: dotted 1px\r\n}\r\n\r\n.rating-input {\r\n    display: absolute;\r\n    cursor: pointer;\r\n    width: 100%;\r\n    height: 1px;\r\n    bottom: 0;\r\n    left: 0;\r\n    font-size: 1px;\r\n    border: none;\r\n    background: 0 0;\r\n    opacity: 0;\r\n    padding: 0;\r\n    margin: 0\r\n}\r\n\r\n.caption-badge,\r\n.rating-container .caption .label {\r\n    line-height: 1;\r\n    text-align: center;\r\n    border-radius: .25rem\r\n}\r\n\r\n.rating-container.is-display-only .rating-stars {\r\n    cursor: default\r\n}\r\n\r\n.rating-disabled .rating-stars {\r\n    cursor: not-allowed\r\n}\r\n\r\n.rating-container .star {\r\n    display: inline-block;\r\n    margin: 0 2px;\r\n    text-align: center\r\n}\r\n\r\n.rating-container .empty-stars {\r\n    color: #aaa\r\n}\r\n\r\n.rating-container .filled-stars {\r\n    position: absolute;\r\n    left: 0;\r\n    top: 0;\r\n    margin: auto;\r\n    color: #fde16d;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    -webkit-text-stroke: 1px #777;\r\n    text-shadow: 1px 1px #999\r\n}\r\n\r\n.rating-rtl {\r\n    float: right\r\n}\r\n\r\n.rating-animate .filled-stars {\r\n    transition: width .25s ease\r\n}\r\n\r\n.rating-rtl .filled-stars {\r\n    left: auto;\r\n    right: 0;\r\n    transition: none;\r\n    -webkit-transform: matrix(-1, 0, 0, 1, 0, 0);\r\n    transform: matrix(-1, 0, 0, 1, 0, 0)\r\n}\r\n\r\n.rating-rtl.is-star .filled-stars {\r\n    right: .06em\r\n}\r\n\r\n.rating-rtl.is-heart .empty-stars {\r\n    margin-right: .07em\r\n}\r\n\r\n.rating-container .clear-rating {\r\n    color: #aaa;\r\n    cursor: not-allowed;\r\n    display: inline-block;\r\n    vertical-align: middle;\r\n    font-size: 60%;\r\n    padding-right: 5px\r\n}\r\n\r\n.clear-rating-active {\r\n    cursor: pointer !important\r\n}\r\n\r\n.clear-rating-active:hover {\r\n    color: #843534\r\n}\r\n\r\n.rating-container .caption .label {\r\n    display: inline-block;\r\n    padding: .25em .4em;\r\n    vertical-align: baseline\r\n}\r\n\r\n.rating-container .caption {\r\n    color: #999;\r\n    display: inline-block;\r\n    vertical-align: middle;\r\n    line-height: 1;\r\n    margin-left: 5px;\r\n    margin-right: 0\r\n}\r\n\r\n.rating-rtl .caption {\r\n    margin-right: 5px;\r\n    margin-left: 0\r\n}\r\n\r\n@media print {\r\n    .rating-container .clear-rating {\r\n        display: none\r\n    }\r\n}\r\n\r\n.rating-xl {\r\n    font-size: 48px\r\n}\r\n\r\n.rating-lg {\r\n    font-size: 40px\r\n}\r\n\r\n.rating-md {\r\n    font-size: 32px\r\n}\r\n\r\n.rating-sm {\r\n    font-size: 24px\r\n}\r\n\r\n.rating-xs {\r\n    font-size: 16px\r\n}\r\n\r\n.rating-xl .caption {\r\n    font-size: 20px\r\n}\r\n\r\n.rating-lg .caption {\r\n    font-size: 18px\r\n}\r\n\r\n.rating-md .caption {\r\n    font-size: 16px\r\n}\r\n\r\n.rating-sm .caption {\r\n    font-size: 14px\r\n}\r\n\r\n.rating-xs .caption {\r\n    font-size: 12px\r\n}\r\n\r\n.caption-badge {\r\n    font-family: Arial, Helvetica, sans-serif;\r\n    display: inline-block;\r\n    padding: .35em .65em;\r\n    font-size: .75em;\r\n    font-weight: 700;\r\n    color: #fff;\r\n    white-space: nowrap;\r\n    vertical-align: baseline\r\n}\r\n\r\n.caption-secondary {\r\n    background-color: #6c757d\r\n}\r\n\r\n.caption-danger {\r\n    background-color: #dc3545\r\n}\r\n\r\n.caption-warning {\r\n    background-color: #ffc107;\r\n    color: #212529\r\n}\r\n\r\n.caption-info {\r\n    background-color: #0dcaf0;\r\n    color: #212529\r\n}\r\n\r\n.caption-primary {\r\n    background-color: #0d6efd\r\n}\r\n\r\n.caption-success {\r\n    background-color: #198754\r\n}", "/* html, */\r\n/* body {\r\n    position: relative;\r\n    height: 100%;\r\n    padding: 0;\r\n  } */\r\n\r\nbody {\r\n    /* background: #f7f7f7; */\r\n    /* font-family: Helvetica Neue, Helvetica, Arial, sans-serif; */\r\n    /* font-size: 14px; */\r\n    /* color: #000; */\r\n    margin: 0;\r\n    padding: 0;\r\n}\r\n\r\n/* .nav {\r\n    --bs-nav-link-hover-color: var(--primary-color) !important;\r\n} */\r\n\r\n/* .nav-tabs.nav-tabs-basic .nav-link.active,\r\n.nav-tabs.nav-tabs-basic .nav-item.show .nav-link {\r\n    border-color: var(--primary-color) !important;\r\n} */\r\n\r\n\r\n.search_btn {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    transform: translateY(0);\r\n    letter-spacing: -0.01rem;\r\n    position: relative;\r\n    border: 0px;\r\n    background: white;\r\n}\r\n\r\n.search_btn:not(.btn-link):hover {\r\n    transform: none;\r\n    box-shadow: none;\r\n}\r\n\r\n.refer_and_earn_border {\r\n    border: 2px dashed #343f52;\r\n}\r\n\r\n.btn-outline-primary:not(:disabled):not(.disabled).active,\r\n.btn-outline-primary:not(:disabled):not(.disabled):active,\r\n.show>.btn-outline-primary.dropdown-toggle {\r\n    color: #fff;\r\n    background-color: var(--primary-color) !important;\r\n    border-color: var(--primary-color) !important;\r\n}\r\n\r\n.btn-check:checked+.btn,\r\n:not(.btn-check)+.btn:active,\r\n.btn:first-child:active,\r\n.btn.active,\r\n.btn.show {\r\n    color: var(--bs-btn-active-color);\r\n    /* background-color: var(--primary-color) !important;; */\r\n    border-color: black !important;\r\n    ;\r\n    box-shadow: var(--bs-btn-active-shadow);\r\n}\r\n\r\n.btn-primary:not(:disabled):not(.disabled).active,\r\n.btn-primary:not(:disabled):not(.disabled):active,\r\n.show>.btn-primary.dropdown-toggle {\r\n    color: #fff;\r\n    background-color: var(--primary-color) !important;\r\n    border-color: var(--primary-color) !important;\r\n}\r\n\r\n.btn-primary.focus,\r\n.btn-primary:focus {\r\n    box-shadow: none !important;\r\n}\r\n\r\n.swiper-slide {\r\n    text-align: center;\r\n    font-size: 16px;\r\n    background: #fff;\r\n    /* background-color: #f7f7f7; */\r\n}\r\n\r\n/* .swiper-slide-category {\r\n    width: 160px !important;\r\n} */\r\n\r\n.category-swiper>.swiper-controls>.swiper-navigation {\r\n    display: none;\r\n}\r\n\r\n.swiper-slide-category img {\r\n    height: 110px;\r\n    border-radius: 8px;\r\n}\r\n\r\n.swiper-wrapper {\r\n    padding: 0 10px 35px 10px;\r\n}\r\n\r\n\r\n.category-image-container img {\r\n    max-height: 100%;\r\n    border-radius: 8px;\r\n    max-width: 100%;\r\n}\r\n\r\n.category-image-container {\r\n    height: 125px !important;\r\n    width: 125px !important;\r\n}\r\n\r\n.description_img img {\r\n    max-width: 100% !important;\r\n}\r\n\r\n.sub_category-image-container {\r\n    /* height: 100px !important; */\r\n    width: 100px !important;\r\n}\r\n\r\n.sub_category-image-container img {\r\n    max-height: 100%;\r\n    max-width: 100%;\r\n    border-radius: 8px;\r\n}\r\n\r\n.brand_image_div {\r\n    width: 110px !important;\r\n    height: 130px !important;\r\n}\r\n\r\n.brand_image_div img {\r\n    object-fit: cover;\r\n    max-width: 100%;\r\n    max-height: 100%;\r\n}\r\n\r\n.progress-wrap.active-progress {\r\n    opacity: 1 !important;\r\n}\r\n\r\n/* .category-swiper .swiper-controls .swiper-navigation {\r\n    display: none;\r\n} */\r\n\r\nsection.slider>.pb-md-1>.swiper-controls {\r\n    position: relative !important;\r\n}\r\n\r\n/* .swiper-controls {\r\n    margin-top: 20px;\r\n} */\r\n\r\n.swiper-wrapper {\r\n    padding: 0 10px 0px 0px !important;\r\n}\r\n\r\n.slide-img {\r\n    max-height: 500px;\r\n    max-width: 1315px;\r\n    /* max-width: 1290px; */\r\n}\r\n\r\n.slide-img img {\r\n    width: 100%;\r\n    height: 100%;\r\n}\r\n\r\n.swiper-slide img {\r\n    max-width: 100%;\r\n    max-height: 100%;\r\n}\r\n\r\n/* .slide-img {\r\n    height: 500px;\r\n} */\r\n\r\n/* .swiper-slide-style4 {\r\n    width: 310px !important;\r\n    margin-right: 20px !important;\r\n} */\r\n\r\n.logo-img img {\r\n    max-width: 100%;\r\n    max-height: 100%;\r\n}\r\n\r\n/* .navbar-brand img {\r\n    width: 50%;\r\n} */\r\n\r\n.product-grid .price span,\r\n.striped-price {\r\n    color: red;\r\n    font-size: 13px;\r\n    font-weight: 400;\r\n    text-decoration: line-through;\r\n    margin-left: 3px;\r\n    display: inline-block;\r\n}\r\n\r\nbutton.btn.btn-secondary.dropdown-toggle {\r\n    gap: 2px;\r\n    display: flex;\r\n    align-items: baseline;\r\n}\r\n\r\nfigure img {\r\n    height: 260px !important;\r\n    max-width: 100% !important;\r\n    width: 100% !important;\r\n}\r\n\r\n/* .fig_image{\r\n    height: 150px !important;\r\n} */\r\n\r\n.gallery-thumbs .swiper-controls {\r\n    display: none;\r\n}\r\n\r\n/* num in counting  */\r\n.num-in {\r\n    background: #FFFFFF;\r\n    border: 2px solid rgba(0, 0, 0, 0.1);\r\n    height: 40px;\r\n    width: 110px;\r\n    float: left;\r\n    margin-top: -22px;\r\n}\r\n\r\n.num-in span {\r\n    width: 30%;\r\n    display: block;\r\n    height: 40px;\r\n    float: left;\r\n    position: relative;\r\n}\r\n\r\n.num-in span:before,\r\n.num-in span:after {\r\n    content: '';\r\n    position: absolute;\r\n    background-color: #667780;\r\n    height: 2px;\r\n    width: 10px;\r\n    top: 50%;\r\n    left: 50%;\r\n    margin-top: -1px;\r\n    margin-left: -5px;\r\n}\r\n\r\n.num-in span.plus:after {\r\n    transform: rotate(90deg);\r\n}\r\n\r\n.num-in input {\r\n    float: left;\r\n    width: 60%;\r\n    height: 36px;\r\n    border: none;\r\n    text-align: center;\r\n    font-weight: 900;\r\n    color: #b0b0b0;\r\n}\r\n\r\n.num-in input:focus {\r\n    color: black;\r\n    outline: none;\r\n}\r\n\r\n/* order details progressbar */\r\n.orders-section #progressbar #step1:before {\r\n    content: \"1\";\r\n    color: #fff;\r\n    width: 29px;\r\n    text-align: center;\r\n}\r\n\r\n.orders-section #progressbar #step2:before {\r\n    content: \"2\";\r\n    color: #fff;\r\n    width: 29px;\r\n    text-align: center;\r\n}\r\n\r\n.orders-section #progressbar #step3:before {\r\n    content: \"3\";\r\n    color: #fff;\r\n    width: 29px;\r\n    text-align: center;\r\n}\r\n\r\n.orders-section #progressbar #step4:before {\r\n    content: \"4\";\r\n    color: #fff;\r\n    width: 29px;\r\n    text-align: center;\r\n}\r\n\r\n.orders-section #progressbar #step5:before {\r\n    content: \"5\";\r\n    color: #fff;\r\n    width: 29px;\r\n    text-align: center;\r\n}\r\n\r\n.orders-section #progressbar #step6:before {\r\n    content: \"6\";\r\n    color: #fff;\r\n    width: 29px;\r\n    text-align: center;\r\n}\r\n\r\n.orders-section #progressbar #step7:before {\r\n    content: \"7\";\r\n    color: #fff;\r\n    width: 29px;\r\n    text-align: center;\r\n}\r\n\r\nsection #progressbar div.active:after {\r\n    background: var(--primary-color);\r\n}\r\n\r\n@media (max-width: 1024px) {\r\n    .process-wrapper.line [class*=col-]:not(:last-child):after {\r\n        width: 100%;\r\n        position: absolute;\r\n        content: \"\";\r\n        height: 1px;\r\n        background: none;\r\n        border-top: 1px solid rgba(164, 174, 198, 0.2);\r\n        top: 1.2rem;\r\n        z-index: 1;\r\n    }\r\n\r\n}\r\n\r\n#step2,\r\n#step3 {\r\n    text-align: center;\r\n}\r\n\r\n#step4,\r\n#step5 {\r\n    text-align: right;\r\n}\r\n\r\n#step6 {\r\n    text-align: end;\r\n}\r\n\r\n#step7 {\r\n    text-align: end;\r\n}\r\n\r\n#progressbar div.cancel {\r\n    color: red !important;\r\n}\r\n\r\n.orders-section #progressbar div.cancel:after {\r\n    width: 0;\r\n}\r\n\r\n\r\n.blog_category .select2-container {\r\n    width: 100% !important;\r\n    /* padding-left: 109px; */\r\n}\r\n\r\n.blog-img {\r\n    /* width: 300px; */\r\n    height: 230px;\r\n}\r\n\r\n.product-list-image {\r\n    max-width: 100%;\r\n    max-height: 100%;\r\n    object-fit: cover;\r\n}\r\n\r\n.product-bg {\r\n    background-color: rgb(255, 255, 255);\r\n    border-radius: 10px;\r\n}\r\n\r\n.item-compare {\r\n    margin-top: 54px;\r\n}\r\n\r\n.swiper-img {\r\n    border: 1px solid #c2c2c2;\r\n    border-radius: 1.4mm;\r\n    width: 88% !important;\r\n    height: 110px !important;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.swiper-img img {\r\n    width: 100% !important;\r\n}\r\n\r\n.swiper-image img {\r\n    max-width: 100% !important;\r\n    max-height: 310px !important;\r\n}\r\n\r\n/* width */\r\n.product-thumb-img::-webkit-scrollbar {\r\n    width: 7px;\r\n}\r\n\r\n/* Track */\r\n.product-thumb-img::-webkit-scrollbar-track {\r\n    border-radius: 7px;\r\n}\r\n\r\n/* Handle */\r\n.product-thumb-img::-webkit-scrollbar-thumb {\r\n    background: rgb(178, 174, 174);\r\n    border: 1px solid rgb(255, 255, 255);\r\n    border-radius: 5px;\r\n}\r\n\r\n.swiper-slide-container>.swiper-controls {\r\n    display: none;\r\n}\r\n\r\n.offer-img img {\r\n    max-width: 100%;\r\n    max-height: 100%;\r\n}\r\n\r\n.offer-img {\r\n    height: 270px;\r\n}\r\n\r\n/* .offer-image {\r\n    height: 330px;\r\n    width: 100%;\r\n} */\r\n\r\n.faq_image {\r\n    height: 350px;\r\n    /* width: 430px; */\r\n}\r\n\r\n.faq_image img {\r\n    max-width: 100%;\r\n    max-height: 100%;\r\n}\r\n\r\n.blog-card-img {\r\n    width: 100% !important;\r\n    height: 300px;\r\n    display: flex;\r\n    align-content: center;\r\n    justify-content: center;\r\n    overflow: hidden;\r\n}\r\n\r\n/* .blog-card-img img {\r\n    max-width: 100% !important;\r\n    max-height: 100%;\r\n} */\r\n\r\n.fav-row .row {\r\n    margin: 0;\r\n}\r\n\r\n\r\n.accordion-card {\r\n    position: relative;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.cart-img {\r\n    width: 110px !important;\r\n    height: 100px !important;\r\n}\r\n\r\n.cart-img img {\r\n    max-width: 100% !important;\r\n    max-height: 100% !important;\r\n}\r\n\r\n.checkout-img {\r\n    width: 40% !important;\r\n    height: 90px !important;\r\n}\r\n\r\n.checkout-img img {\r\n    max-width: 100% !important;\r\n    max-height: 100% !important;\r\n}\r\n\r\n.iziModal.isAttached,\r\n.iziModal.isFullscreen {\r\n    border-radius: 12px !important;\r\n}\r\n\r\n.compare-img {\r\n    /* width: 100px !important; */\r\n    height: 270px !important;\r\n}\r\n\r\n.compare-img img {\r\n    max-width: 100% !important;\r\n    max-height: 100% !important;\r\n}\r\n\r\n.reviews .review-box {\r\n    background-color: #e8e8e8;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.review-img {\r\n    width: 100% !important;\r\n    height: 90px !important;\r\n}\r\n\r\n.review-img img {\r\n    max-width: 100% !important;\r\n    max-height: 100% !important;\r\n}\r\n\r\n/* .btn-outline-primary:hover {\r\n    color: white !important;\r\n} */\r\n\r\n.refer-img-box {\r\n    height: 250px;\r\n    /* width: 400px; */\r\n}\r\n\r\n.refer-img-box img {\r\n    max-width: 100% !important;\r\n    max-height: 100% !important;\r\n}\r\n\r\n.footer-logo-footer {\r\n    width: 240px;\r\n    height: 80px;\r\n}\r\n\r\n.footer-logo-footer img {\r\n    max-width: 100%;\r\n    max-height: 100%;\r\n}\r\n\r\n.list-unstyled li a:hover {\r\n    color: white !important;\r\n}\r\n\r\n.link_cursor {\r\n    cursor: pointer;\r\n}\r\n\r\n.promo-code-img {\r\n    height: 100px;\r\n    width: 100px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.promo-code-img img {\r\n    max-height: 100%;\r\n    max-width: 100%;\r\n}\r\n\r\n.copy-promo-code {\r\n    border: 1px dashed;\r\n    background: #ebebeb;\r\n    max-width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    border-radius: 8px;\r\n    cursor: pointer;\r\n    justify-content: space-between;\r\n}\r\n\r\n/* ---------------------------------------------------------------------------------------------- \r\n      Product-listing \r\n  */\r\n\r\n.product-listing .title {\r\n    font-size: 16px;\r\n}\r\n\r\n.sidebar-filter {\r\n    display: none;\r\n}\r\n\r\n/* filter sidebar scroll */\r\n.filter_attributes {\r\n    max-height: 300px;\r\n    overflow-y: scroll;\r\n}\r\n\r\n.filter_attributes::-webkit-scrollbar {\r\n    width: 7px;\r\n}\r\n\r\n.filter_attributes::-webkit-scrollbar-track {\r\n    border-radius: 7px;\r\n}\r\n\r\n.filter_attributes::-webkit-scrollbar-thumb {\r\n    background: rgb(66, 66, 66);\r\n    border: 1px solid rgb(255, 255, 255);\r\n    border-radius: 5px;\r\n}\r\n\r\n/* brand filter sidebar scroll */\r\n.brand_filter {\r\n    max-height: 160px;\r\n    overflow-y: scroll;\r\n}\r\n\r\n.title_wrap {\r\n    text-wrap: nowrap !important;\r\n    text-overflow: ellipsis !important;\r\n    overflow: hidden !important;\r\n}\r\n\r\n.brand_filter::-webkit-scrollbar {\r\n    width: 7px;\r\n}\r\n\r\n.brand_filter::-webkit-scrollbar-track {\r\n    border-radius: 7px;\r\n}\r\n\r\n.brand_filter::-webkit-scrollbar-thumb {\r\n    background: rgb(66, 66, 66);\r\n    border: 1px solid rgb(255, 255, 255);\r\n    border-radius: 5px;\r\n}\r\n\r\n/* HIDE RADIO */\r\n.brand_div [type=radio] {\r\n    position: absolute;\r\n    opacity: 0;\r\n    width: 0;\r\n    height: 0;\r\n}\r\n\r\n.brand_div {\r\n    height: 35px !important;\r\n    width: 35px !important;\r\n}\r\n\r\n/* IMAGE STYLES */\r\n.brand_div [type=radio]+img {\r\n    cursor: pointer;\r\n    max-height: 35px;\r\n    max-width: 35px;\r\n    object-fit: contain;\r\n}\r\n\r\n/* CHECKED STYLES */\r\n.brand_div [type=radio]:checked+img {\r\n    outline: 2px solid var(--primary-color);\r\n}\r\n\r\n.selected-brand {\r\n    outline: 2px solid var(--primary-color);\r\n}\r\n\r\n.selected-category {\r\n    background-color: var(--primary-color);\r\n    background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9Jy00IC00IDggOCc+PGNpcmNsZSByPScxLjUnIGZpbGw9JyNmZmYnLz48L3N2Zz4=);\r\n}\r\n\r\n/* category filter scroll sidebar */\r\n.category_filter {\r\n    max-height: 210px;\r\n    overflow-y: scroll;\r\n}\r\n\r\n.category_filter::-webkit-scrollbar {\r\n    width: 7px;\r\n}\r\n\r\n.category_filter::-webkit-scrollbar-track {\r\n    border-radius: 7px;\r\n}\r\n\r\n.category_filter::-webkit-scrollbar-thumb {\r\n    background: rgb(66, 66, 66);\r\n    border: 1px solid rgb(255, 255, 255);\r\n    border-radius: 5px;\r\n}\r\n\r\n/* select2 */\r\n\r\n#offcanvas-search .select2-results {\r\n    z-index: 999999;\r\n    position: fixed;\r\n    background: white;\r\n    width: inherit;\r\n    /* overflow: scroll;\r\n    height: 50%; */\r\n}\r\n\r\n.select2-results__options {\r\n    max-height: 300px;\r\n    overflow: auto;\r\n}\r\n\r\n.select2-result-repository {\r\n    padding-top: 4px;\r\n    padding-bottom: 3px;\r\n}\r\n\r\n.select2-result-repository__avatar {\r\n    float: left;\r\n    width: 60px;\r\n    height: 60px;\r\n    margin-right: 10px;\r\n    text-align: center !important;\r\n}\r\n\r\n.select2-result-repository__icon {\r\n    float: left;\r\n    width: 60px;\r\n    height: 25px;\r\n    margin-right: 10px;\r\n    text-align: center !important;\r\n}\r\n\r\n.select2-result-repository__avatar img {\r\n    max-width: 100%;\r\n    max-height: 100%;\r\n    border-radius: 2px;\r\n}\r\n\r\n.select2-result-repository__meta {\r\n    margin-left: 70px;\r\n}\r\n\r\n.select2-result-repository__meta_icon {\r\n    margin-left: 0px;\r\n}\r\n\r\n.select2-result-repository__title {\r\n    word-wrap: break-word;\r\n    line-height: 1.1;\r\n    margin-bottom: 4px;\r\n}\r\n\r\n.select2-result-repository__forks,\r\n.select2-result-repository__stargazers {\r\n    margin-right: 1em;\r\n}\r\n\r\n.select2-result-repository__forks,\r\n.select2-result-repository__stargazers,\r\n.select2-result-repository__watchers {\r\n    display: inline-block;\r\n    color: #aaa;\r\n    font-size: 11px;\r\n}\r\n\r\n.select2-result-repository__description {\r\n    font-size: 13px;\r\n    color: #777;\r\n    margin-top: 4px;\r\n}\r\n\r\n.select2-results__option--highlighted {\r\n    opacity: 1 !important;\r\n}\r\n\r\n.select2-results__option--highlighted .select2-result-repository__title {\r\n    color: rgb(31, 30, 30);\r\n    color: black;\r\n    font-weight: bold;\r\n}\r\n\r\n.select2-results__option--highlighted .select2-result-repository__forks,\r\n.select2-results__option--highlighted .select2-result-repository__stargazers,\r\n.select2-results__option--highlighted .select2-result-repository__description,\r\n.select2-results__option--highlighted .select2-result-repository__watchers {\r\n    color: rgb(31, 30, 30);\r\n}\r\n\r\n/* .select2-container--adwitt .select2-results>.select2-results__options {\r\n    max-height: 300px;\r\n    overflow: auto;\r\n} */\r\n\r\n.select2-selection__rendered {\r\n    word-wrap: break-word !important;\r\n    text-overflow: hidden !important;\r\n    overflow: hidden;\r\n    white-space: normal !important;\r\n}\r\n\r\n.select2-selection__placeholder {\r\n    color: #bcbbbb;\r\n}\r\n\r\n.edit_city .select2-container {\r\n    width: 100% !important;\r\n}\r\n\r\n/* .select2-container .select2-selection--single { */\r\n/* margin-top: 8px; */\r\n/* padding: 8px;\r\n    height: 100% !important; */\r\n/* } */\r\n\r\n.mobile-search .select2-container {\r\n    border: 2px solid rgba(185, 185, 185, .2);\r\n}\r\n\r\n.select2-results__options {\r\n    overflow-y: auto;\r\n}\r\n\r\n.select2-dropdown {\r\n    z-index: 9999;\r\n    border: 0px solid var(--border-color);\r\n    border-radius: 0px;\r\n}\r\n\r\n.select2-search--dropdown .select2-search__field {\r\n    border-radius: 6px;\r\n    padding: 2px;\r\n}\r\n\r\n.select2-results__option {\r\n    opacity: 0.8;\r\n    transition: 150ms;\r\n}\r\n\r\n.product-page-details .btn-group>.active {\r\n    background-color: #343f52 !important;\r\n    /* background-color: var(--primary-color) !important;  */\r\n}\r\n\r\n\r\n/* accordion in filter */\r\n.accordion-wrapper .card-header button:before {\r\n    color: black !important;\r\n}\r\n\r\n.accordion-wrapper .card-header button:hover::before {\r\n    color: var(--primary-color) !important;\r\n}\r\n\r\n.accordion-wrapper .card-header button:hover {\r\n    color: var(--primary-color) !important;\r\n}\r\n\r\n\r\n\r\n@media (min-width: 1024px) {\r\n    .swiper-auto .swiper-slide {\r\n        width: 850px;\r\n    }\r\n\r\n    .swiper-auto.swiper-auto-xs .swiper-slide {\r\n        width: 600px;\r\n    }\r\n\r\n    .logo-img {\r\n        /* width: 187px !important; */\r\n        height: 50px !important;\r\n    }\r\n\r\n    .mobile-searchbar {\r\n        display: none;\r\n    }\r\n\r\n    .mobile_quick_view {\r\n        display: none;\r\n    }\r\n}\r\n\r\n\r\n@media (max-width: 991.98px) {\r\n\r\n    .map.rounded-top iframe,\r\n    .image-wrapper.rounded-top {\r\n        border-top-left-radius: 0.4rem !important;\r\n        border-top-right-radius: 0.4rem !important;\r\n        width: 350px !important;\r\n    }\r\n\r\n    /* .image-wrapper.rounded-4-top {\r\n      border-top-left-radius: 0.8rem !important;\r\n      border-top-right-radius: 0.8rem !important;\r\n    } */\r\n}\r\n\r\n\r\n@media (min-width: 768px) and (max-width: 1023.98px) {\r\n    .swiper-auto .swiper-slide {\r\n        width: 600px;\r\n    }\r\n\r\n    .swiper-auto.swiper-auto-xs .swiper-slide {\r\n        width: 400px;\r\n    }\r\n\r\n    .logo-img {\r\n        /* width: 187px !important; */\r\n        height: 40px !important;\r\n    }\r\n\r\n    .mobile-searchbar {\r\n        display: none;\r\n    }\r\n\r\n    .mobile_quick_view {\r\n        display: none;\r\n    }\r\n}\r\n\r\n@media (max-width: 767px) {\r\n    .swiper-auto.swiper-auto-xs .swiper-slide {\r\n        width: 300px;\r\n    }\r\n\r\n    .desktop-search {\r\n        display: none;\r\n    }\r\n\r\n    .desktop_quick_view {\r\n        display: none;\r\n    }\r\n\r\n    .mySwiper .swiper-wrapper .swiper-mobile-slide {\r\n        width: 190px !important;\r\n    }\r\n}\r\n\r\n\r\n@media screen and (max-width: 450px) {\r\n    .sidebar-filter-sm {\r\n        display: none;\r\n    }\r\n\r\n    .desktop-search {\r\n        display: none;\r\n    }\r\n\r\n    .desktop_quick_view {\r\n        display: none;\r\n    }\r\n}\r\n\r\n@media screen and (max-width:500px) {\r\n    #chat-iframe {\r\n        width: 90% !important;\r\n    }\r\n}\r\n\r\n.sale_tag {\r\n    top: 1rem;\r\n    left: 1rem;\r\n}\r\n\r\n/* Extra small devices (phones, 600px and down) */\r\n\r\n@media only screen and (max-width: 600px) {\r\n    .swiper-container.swiper-thumbs-container {\r\n        display: none;\r\n    }\r\n\r\n    .fig_image {\r\n        height: 150px !important;\r\n    }\r\n\r\n    .mt-3 {\r\n        margin-top: 0px !important;\r\n    }\r\n\r\n    .mb-13 {\r\n        margin-bottom: 0rem !important;\r\n    }\r\n\r\n    /* .isotope{\r\n        height: 0px !important;\r\n    } */\r\n\r\n    .default-style .post-header h4 {\r\n        font-size: 14px !important;\r\n        margin-bottom: 0px !important;\r\n    }\r\n\r\n    .style_3 .product-content h4 {\r\n        font-size: 14px !important;\r\n    }\r\n\r\n    .style_4 .product-content h4 {\r\n        font-size: 14px !important;\r\n    }\r\n\r\n    .mySwiper .swiper-wrapper .swiper-mobile-slide {\r\n        width: 190px !important;\r\n    }\r\n\r\n    .product_listing_list h4 {\r\n        font-size: 0.75rem;\r\n    }\r\n\r\n    .product_listing_list .product-rating-small {\r\n        margin-bottom: 0px !important;\r\n    }\r\n\r\n    .sale_tag {\r\n        top: 0.1rem !important;\r\n        left: 0.1rem !important;\r\n    }\r\n\r\n    .product_listing_list>.mt-n2 {\r\n        display: none;\r\n    }\r\n\r\n    /* .swiper-slide-style4 {\r\n        width: 150px !important;\r\n    } */\r\n\r\n    .default_heading h3 {\r\n        font-size: 18px;\r\n    }\r\n\r\n    .default_heading h6 {\r\n        font-size: 16px !important;\r\n    }\r\n\r\n    .seller-image-container {\r\n        /* width: 150px !important; */\r\n        height: 200px !important;\r\n    }\r\n\r\n    .seller-image-container .fig_seller_image {\r\n        width: 150px !important;\r\n        /* height: 150px !important; */\r\n    }\r\n\r\n    .sidebar-filter-sm {\r\n        display: none;\r\n    }\r\n\r\n    .listing-page .filter-nav {\r\n        display: none;\r\n    }\r\n\r\n    .logo-img {\r\n        /* width: 187px !important; */\r\n        height: 75px !important;\r\n    }\r\n\r\n    /* .swiper-slide-category {\r\n        width: 100px !important;\r\n        margin-right: 10px !important;\r\n    } */\r\n\r\n    .swiper-slide-category img {\r\n        height: 75px;\r\n        border-radius: 8px;\r\n    }\r\n}\r\n\r\n@media only screen and (min-width: 800px) {\r\n    .product-preview-image-section-sm {\r\n        display: none;\r\n    }\r\n\r\n}\r\n\r\n@media (max-width:799px) {\r\n    .swiper-thumbs-container {\r\n        display: none !important;\r\n    }\r\n}\r\n\r\n\r\n/* Small devices (portrait tablets and large phones, 600px and up) */\r\n\r\n@media only screen and (max-width: 650px) {\r\n\r\n    /* .swiper-container.swiper-thumbs-container {\r\n        display: none;\r\n    } */\r\n    .filter-nav {\r\n        display: none;\r\n    }\r\n\r\n    .sidebar-filter-sm {\r\n        display: none;\r\n    }\r\n}\r\n\r\n/* Medium devices (landscape tablets, 768px and up) */\r\n\r\n@media only screen and (max-width: 768px) {\r\n    .filter-nav {\r\n        display: none;\r\n    }\r\n\r\n    .sidebar-filter-sm {\r\n        display: none;\r\n    }\r\n\r\n    /* .swiper-slide-category img {\r\n        height: 110px;\r\n    } */\r\n}\r\n\r\n/* Extra small devices (phones, 990px and down) */\r\n\r\n@media only screen and (max-width: 991px) {\r\n    .product-page-preview-image-section-md {\r\n        display: none;\r\n    }\r\n\r\n    .sidebar-filter-sm {\r\n        display: none;\r\n    }\r\n\r\n    /* .swiper-slide-category img {\r\n        height: 110px;\r\n    } */\r\n    .logo-img {\r\n        /* width: 187px !important; */\r\n        height: 75px !important;\r\n    }\r\n\r\n}\r\n\r\n@media only screen and (min-width: 1000px) {\r\n\r\n    .filter-sidebar-mobile {\r\n        display: none;\r\n    }\r\n\r\n    .slide-img {\r\n        height: 500px;\r\n    }\r\n\r\n    .swiper-slide-category img {\r\n        height: 110px;\r\n        border-radius: 8px;\r\n    }\r\n\r\n    /* .swiper-slide-category {\r\n        width: 150px !important;\r\n        margin-right: 10px !important;\r\n    } */\r\n}\r\n\r\n#pills-tab>.nav-item a:hover {\r\n    color: var(--primary-color) !important;\r\n}\r\n\r\n.buy-now-btn {\r\n    position: fixed;\r\n    bottom: 18px;\r\n    left: 18px;\r\n    z-index: 9999;\r\n}\r\n\r\n/* color-switcher */\r\n\r\n.setting-icon {\r\n    font-size: 1.7rem !important;\r\n    margin-top: 6px;\r\n}\r\n\r\n#colors-switcher {\r\n    background: none repeat scroll 0 0 #fff;\r\n    -webkit-box-shadow: 0 0 3px rgba(60, 72, 88, 0.15);\r\n    box-shadow: 0 0 3px rgba(60, 72, 88, 0.15);\r\n    left: -189px;\r\n    position: fixed;\r\n    top: 24%;\r\n    width: 189px;\r\n    z-index: 9999;\r\n    padding: 10px 5px\r\n}\r\n\r\n#colors-switcher .color-bottom {\r\n    background: none repeat scroll 0 0 #fff\r\n}\r\n\r\n#colors-switcher .color-bottom a.settings {\r\n    background: none repeat scroll 0 0 #fff;\r\n    height: 41px;\r\n    position: absolute;\r\n    right: -40px;\r\n    top: 28px;\r\n    width: 40px;\r\n    padding: 3px;\r\n    border-radius: 0 30px 30px 0\r\n}\r\n\r\n#colors-switcher ul.color-style {\r\n    list-style: none outside none;\r\n    overflow: hidden;\r\n    padding: 0;\r\n    border-radius: 0px;\r\n}\r\n\r\n.list-item-inline {\r\n    display: inline-block;\r\n}\r\n\r\n#colors-switcher ul.color-style li.list-inline-item:not(:last-child) {\r\n    margin-right: 0px;\r\n    margin-bottom: 0\r\n}\r\n\r\n#colors-switcher ul.color-style li a {\r\n    cursor: pointer;\r\n    display: block;\r\n    height: 35px;\r\n    width: 35px;\r\n    border-radius: 50%;\r\n    margin: 3px\r\n}\r\n\r\n#colors-switcher ul.color-style .blue {\r\n    background-color: #1e4071;\r\n}\r\n\r\n#colors-switcher ul.color-style .aqua {\r\n    background-color: #54a8c7;\r\n}\r\n\r\n#colors-switcher ul.color-style .fuchsia {\r\n    background-color: #e668b3;\r\n}\r\n\r\n#colors-switcher ul.color-style .grape {\r\n    background-color: #605dba;\r\n}\r\n\r\n#colors-switcher ul.color-style .green {\r\n    background-color: #45c4a0;\r\n}\r\n\r\n\r\n#colors-switcher ul.color-style .leaf {\r\n    background-color: #7cb798;\r\n}\r\n\r\n#colors-switcher ul.color-style .navy {\r\n    background-color: #343f52;\r\n}\r\n\r\n#colors-switcher ul.color-style .orange {\r\n    background-color: #f78b77;\r\n}\r\n\r\n#colors-switcher ul.color-style .pink {\r\n    background-color: #d16b86;\r\n}\r\n\r\n#colors-switcher ul.color-style .purple {\r\n    background-color: #747ed1;\r\n}\r\n\r\n#colors-switcher ul.color-style .red {\r\n    background-color: #e2626b;\r\n}\r\n\r\n#colors-switcher ul.color-style .sky {\r\n    background-color: #5eb9f0;\r\n}\r\n\r\n#colors-switcher ul.color-style .violet {\r\n    background-color: #a07cc5;\r\n}\r\n\r\n.product-preview-image-section-sm .swiper-controls .swiper-navigation .swiper-button {\r\n    display: none;\r\n}\r\n\r\n.swiper-style4 .swiper-controls .swiper-navigation .swiper-button {\r\n    display: none;\r\n}\r\n\r\n.swiper-navigation .swiper-button {\r\n    display: none;\r\n}\r\n\r\n.whatsapp-icon {\r\n    position: fixed;\r\n    left: 20px;\r\n    bottom: 65px;\r\n    z-index: 9999;\r\n    width: 45px;\r\n    height: 45px;\r\n    /* box-shadow: rgba(99, 99, 99, 0.2) 0px 0px 3px 0px; */\r\n}\r\n\r\n.whatsapp-icon .btn {\r\n    padding: 0 !important;\r\n}\r\n\r\n.whatsapp-icon .btn img {\r\n    max-width: 100%;\r\n    max-height: 100%;\r\n}\r\n\r\n/* chat css */\r\n\r\n.chat-hide-show {\r\n    display: none !important;\r\n}\r\n\r\n.chat-min {\r\n    /* height: 73vh !important; */\r\n    height: 100% !important;\r\n    max-height: 850px;\r\n}\r\n\r\n.image-box-100 {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: baseline;\r\n    width: 100px;\r\n    height: 100px;\r\n}\r\n\r\n.image-box-100 img {\r\n    max-width: 100%;\r\n    max-height: 100%;\r\n}\r\n\r\n.chat-min>.select2-container {\r\n    width: 100% !important;\r\n}\r\n\r\n.chat-max {\r\n    height: 88vh !important;\r\n}\r\n\r\n.chat-theme-dark {\r\n    background-color: #1A1D21 !important;\r\n    border-top: 2px solid #17a2b8 !important;\r\n}\r\n\r\n.chat-theme-light {\r\n    background-color: #F8F8FA !important;\r\n    border-top: 2px solid #383F45 !important;\r\n}\r\n\r\n.chat-theme-light .chat-left .person-group-chat {\r\n    background: #8e8e8e;\r\n    padding: 1px 5px;\r\n    border-radius: 3px;\r\n    position: absolute;\r\n    color: #ececec;\r\n    top: -22px;\r\n    left: 0;\r\n    font-size: 12px;\r\n}\r\n\r\n.chat-theme-light .chat-right .person-group-chat {\r\n    background: #8e8e8e;\r\n    padding: 1px 5px;\r\n    border-radius: 3px;\r\n    position: absolute;\r\n    color: #ececec;\r\n    top: -22px;\r\n    right: 0;\r\n    font-size: 12px;\r\n}\r\n\r\n.chat-avtar-search {\r\n    border-radius: 50%;\r\n    border: none;\r\n    width: 50px;\r\n    height: 50px;\r\n    background: #99e2ff;\r\n    text-align: center;\r\n    line-height: 52px;\r\n    font-size: 26px;\r\n    font-weight: 800;\r\n    color: #777777;\r\n    margin: 0px 16px 0px 0px;\r\n}\r\n\r\n.chat-avtar-main {\r\n    border-radius: 50%;\r\n    border: none;\r\n    width: 45px;\r\n    height: 45px;\r\n    background: #e1ebff;\r\n    text-align: center;\r\n    line-height: 52px;\r\n    font-size: 26px;\r\n    font-weight: 800;\r\n    color: #777777;\r\n}\r\n\r\n.chat-box .chat-content .chat-item>.chat-avtar {\r\n    float: left;\r\n    /*    border-radius: 50%;\r\n    border: none;\r\n    width: 50px;\r\n    height: 50px;\r\n    background: #e1ebff;\r\n    text-align: center;\r\n    line-height: 52px;\r\n    font-size: 26px;\r\n    font-weight: 800;\r\n    color: #777777;*/\r\n    -webkit-user-drag: none !important;\r\n}\r\n\r\n.chat-box .chat-content .chat-item.chat-right .chat-avtar {\r\n    float: right;\r\n    /*    border-radius: 50%;\r\n    border: none;\r\n    width: 50px;\r\n    height: 50px;\r\n    background: #ffe1e1;\r\n    text-align: center;\r\n    line-height: 52px;\r\n    font-size: 26px;\r\n    font-weight: 800;\r\n    color: #777777;*/\r\n    -webkit-user-drag: none;\r\n}\r\n\r\n.chat-theme-light .delete-msg {\r\n    position: absolute;\r\n    color: white;\r\n    top: 0;\r\n    right: 3px;\r\n}\r\n\r\n.chat-theme-dark .delete-msg {\r\n    position: absolute;\r\n    color: white;\r\n    top: 0;\r\n    right: 3px;\r\n}\r\n\r\n.chat-theme-light .download-msg {\r\n    position: absolute;\r\n    color: black;\r\n    top: 0;\r\n    left: 3px;\r\n}\r\n\r\n.chat-theme-dark .download-msg {\r\n    position: absolute;\r\n    color: black;\r\n    top: 0;\r\n    left: 3px;\r\n}\r\n\r\n.chat_media_img {\r\n    grid-area: img;\r\n}\r\n\r\n.chat_media_file {\r\n    grid-area: file;\r\n    color: #b13c3c;\r\n}\r\n\r\n.chat_media_size {\r\n    grid-area: size;\r\n}\r\n\r\n\r\n.chat-theme-light .chat-files {\r\n    text-align: center;\r\n    display: grid;\r\n    grid-template-areas:\r\n        'img file file file size size';\r\n    grid-gap: 10px;\r\n    background-color: #cccccc;\r\n    border-radius: 3px;\r\n    padding: 10px;\r\n    color: black;\r\n    margin: 1px;\r\n}\r\n\r\n.chat-theme-dark .chat-files {\r\n    text-align: center;\r\n    display: grid;\r\n    grid-template-areas:\r\n        'img file file file size size';\r\n    grid-gap: 10px;\r\n    background-color: #cccccc;\r\n    border-radius: 3px;\r\n    padding: 10px;\r\n    color: black;\r\n    margin: 1px;\r\n}\r\n\r\n.chat-files-search {\r\n    text-align: center;\r\n    display: grid;\r\n    grid-template-areas:\r\n        'img file file file size fa-download';\r\n    grid-gap: 10px;\r\n    background-color: #cccccc;\r\n    border-radius: 3px;\r\n    padding: 10px;\r\n    color: black;\r\n    margin: 1px;\r\n}\r\n\r\n.chat-theme-light .chat-image-view {\r\n    position: relative;\r\n    background-color: #cccccc;\r\n    border-radius: 3px;\r\n    padding: 5px;\r\n    color: black;\r\n    margin: 1px;\r\n}\r\n\r\n\r\n.chat-theme-light .msg_text_media {\r\n    display: grid;\r\n}\r\n\r\n.chat-theme-dark .chat-image-view {\r\n    position: relative;\r\n    background-color: #cccccc;\r\n    border-radius: 3px;\r\n    padding: 5px;\r\n    color: black;\r\n    margin: 1px;\r\n}\r\n\r\n.chat-theme-dark .msg_text_media {\r\n    display: grid;\r\n}\r\n\r\n.chat-theme-light .download-btn-styling {\r\n    background: #27ab45;\r\n    color: black;\r\n    padding: 7px;\r\n    border-radius: 3px;\r\n    display: none;\r\n    margin: 8px 5px 0px 0px;\r\n}\r\n\r\n.chat-theme-dark .download-btn-styling {\r\n    background: #27ab45;\r\n    color: black;\r\n    padding: 7px;\r\n    border-radius: 3px;\r\n    display: none;\r\n    margin: 8px 5px 0px 0px;\r\n}\r\n\r\n.chat-image-view:hover .download-btn-styling {\r\n    display: block;\r\n}\r\n\r\n.chat-files:hover .download-btn-styling {\r\n    display: block;\r\n}\r\n\r\n\r\n.chat-theme-light .go-to-bottom-btn {\r\n    cursor: pointer;\r\n    padding: 6px;\r\n    width: 30px;\r\n    height: 30px;\r\n    border-radius: 50%;\r\n    color: white !important;\r\n    border: none;\r\n    position: absolute;\r\n    top: -48%;\r\n    right: 0px;\r\n    -webkit-transform: translate(-50%, -50%);\r\n    transform: translate(-50%, -50%);\r\n    box-shadow: 0px 4px 7px 4px #00000036;\r\n    display: none;\r\n    z-index: 2;\r\n}\r\n\r\n.chat-theme-dark .go-to-bottom-btn {\r\n    cursor: pointer;\r\n    padding: 6px;\r\n    width: 30px;\r\n    height: 30px;\r\n    border-radius: 50%;\r\n    color: white !important;\r\n    border: none;\r\n    position: absolute;\r\n    top: -48%;\r\n    right: 0px;\r\n    -webkit-transform: translate(-50%, -50%);\r\n    transform: translate(-50%, -50%);\r\n    box-shadow: 0px 4px 7px 4px #00000036;\r\n    display: none;\r\n    z-index: 2;\r\n}\r\n\r\n.chat-theme-dark .chat-preview-btn {\r\n    position: absolute;\r\n    top: 30%;\r\n    right: 60px;\r\n    color: #868686 !important;\r\n}\r\n\r\n.chat-theme-light .chat-preview-btn {\r\n    position: absolute;\r\n    top: 30%;\r\n    right: 60px;\r\n    color: #414141 !important;\r\n}\r\n\r\n.chat-theme-dark .chat-preview-btn:hover {\r\n    color: #5a5a5a !important;\r\n}\r\n\r\n.chat-theme-light .chat-preview-btn:hover {\r\n    color: #303030 !important;\r\n}\r\n\r\n.chat-theme-dark .chat-time {\r\n    color: #d8d8d8 !important;\r\n}\r\n\r\n.chat-theme-light .new-msg-rcv {\r\n    font-weight: 1000 !important;\r\n    color: #383F45 !important;\r\n}\r\n\r\n.chat-theme-dark .new-msg-rcv {\r\n    font-weight: 1000 !important;\r\n    color: #FFFFFF !important;\r\n}\r\n\r\n.chat-theme-light .chat-bg {\r\n    background-image: url(../images/bg-chat.jpg) !important;\r\n}\r\n\r\n.chat-theme-light .text-successg {\r\n    color: #39E500 !important;\r\n}\r\n\r\n.chat-theme-dark .chat-bg {\r\n    background-color: #303335 !important;\r\n}\r\n\r\n.chat-theme-dark .text-success {\r\n    color: #39E500 !important;\r\n}\r\n\r\n.chat-theme-dark .chat-search-box {\r\n    background-color: #1a1d21 !important;\r\n    border: 1px solid #a6a7ab !important;\r\n    border-radius: .25rem !important;\r\n    margin-right: 8px !important;\r\n    height: 30px !important;\r\n    width: -webkit-fill-available;\r\n}\r\n\r\n.chat-theme-dark .chat-search-box:hover {\r\n    background-color: #363b42 !important;\r\n    border: 1px solid #c9cacc !important;\r\n}\r\n\r\n.chat-theme-dark .chat-search-box:focus {\r\n    background-color: #363b42 !important;\r\n    border: 1px solid #c9cacc !important;\r\n    color: #c9cacc !important;\r\n}\r\n\r\n.chat-theme-light .chat-search-box {\r\n    border-radius: .25rem !important;\r\n    margin-right: 8px !important;\r\n    height: 30px !important;\r\n    width: -webkit-fill-available;\r\n}\r\n\r\n.chat-theme-light .chat-search-box:hover {\r\n    background-color: #f2f2f7;\r\n    border-color: #d9dae4;\r\n}\r\n\r\n.chat-theme-dark .chat-search-btn {\r\n    background-color: #1a1d21 !important;\r\n    border-color: #a6a7ab !important;\r\n}\r\n\r\n.chat-scroll {\r\n    overflow: scroll !important;\r\n    outline: none !important;\r\n}\r\n\r\n/* width */\r\n.chat-scroll::-webkit-scrollbar {\r\n    width: 7px;\r\n}\r\n\r\n/* Track */\r\n.chat-scroll::-webkit-scrollbar-track {\r\n    border-radius: 7px;\r\n}\r\n\r\n/* Handle */\r\n.chat-scroll::-webkit-scrollbar-thumb {\r\n    background: rgb(66, 66, 66);\r\n    border: 1px solid rgb(255, 255, 255);\r\n    border-radius: 5px;\r\n}\r\n\r\n.chat-theme-dark .active {\r\n    font-weight: 700 !important;\r\n    background: #3abaf4;\r\n    padding: 3px 15px;\r\n    color: #FFFDF9 !important;\r\n}\r\n\r\n.chat-theme-dark .active:hover {\r\n    background: #3abaf4 !important;\r\n    color: #FFFDF9 !important;\r\n}\r\n\r\n.chat-theme-light .active {\r\n    font-weight: 700 !important;\r\n    background: #3abaf4;\r\n    padding: 3px 15px;\r\n    color: #FFFDF9 !important;\r\n}\r\n\r\n.chat-theme-light .active:hover {\r\n    background: #3abaf4 !important;\r\n    color: #FFFDF9 !important;\r\n}\r\n\r\n.chat-theme-dark .chat-person {\r\n    font-weight: 700;\r\n    color: #ababab;\r\n    padding: 3px 15px;\r\n}\r\n\r\n.chat-theme-dark code {\r\n    background: #e8e8e8;\r\n    padding: 6px 8px;\r\n    border-radius: 4px;\r\n}\r\n\r\n.chat-theme-light code {\r\n    background: #e8e8e8;\r\n    padding: 6px 8px;\r\n    border-radius: 4px;\r\n}\r\n\r\n.chat-theme-dark .chat-person:hover {\r\n    background: rgb(0, 0, 0);\r\n    cursor: pointer;\r\n}\r\n\r\n.chat-theme-light .chat-person {\r\n    font-weight: 500;\r\n    color: #4f5961;\r\n    padding: 3px 15px;\r\n}\r\n\r\n.chat-theme-light .chat-person:hover {\r\n    background: #FFFFFF;\r\n    cursor: pointer;\r\n}\r\n\r\n.chat-theme-dark .text-color {\r\n    color: #ababab !important;\r\n\r\n}\r\n\r\n.chat-theme-light .text-color {\r\n    color: #383F45 !important;\r\n}\r\n\r\n.chat-theme-dark .text-color h4 {\r\n    color: #ababab !important;\r\n}\r\n\r\n.chat-theme-light .text-color h4 {\r\n    color: #383F45 !important;\r\n}\r\n\r\n.chat-theme-dark .theme-inputs {\r\n    background-color: #1a1d21 !important;\r\n    border: 1px solid #a6a7ab !important;\r\n    border-radius: 6px !important;\r\n    color: #c9cacc !important;\r\n    height: auto !important;\r\n    white-space: pre-wrap !important;\r\n}\r\n\r\n.chat-theme-light .theme-inputs {\r\n    border: 1px solid #383F45 !important;\r\n    border-radius: 6px !important;\r\n    height: auto !important;\r\n    white-space: pre-wrap !important;\r\n}\r\n\r\n.chat-card-header {\r\n    line-height: 9px !important;\r\n    min-height: 0px !important;\r\n    padding: 5px 8px !important;\r\n    border-bottom: 0px !important;\r\n}\r\n\r\n.chat-card-header h4 {\r\n    font-size: 17px !important;\r\n    font-weight: 500;\r\n}\r\n\r\n.chat-list-unstyled-border li {\r\n    border-bottom: 0px !important;\r\n    padding-bottom: 0px !important;\r\n    margin-bottom: 0px !important;\r\n}\r\n\r\n.chat-card-body {\r\n    padding-top: 0px !important;\r\n    padding-bottom: 0px !important;\r\n    flex: 0 !important;\r\n}\r\n\r\n.chat-img-undrag {\r\n    user-select: none;\r\n    -moz-user-select: none;\r\n    -webkit-user-drag: none;\r\n    -webkit-user-select: none;\r\n    -ms-user-select: none;\r\n}\r\n\r\n\r\n.chat_divider {\r\n    padding: 8px 10px;\r\n    text-align: center;\r\n    font-size: medium;\r\n    color: brown;\r\n    margin: 0 0 20px 0px;\r\n    display: flex;\r\n    align-items: center;\r\n    text-align: center;\r\n}\r\n\r\n.chat_divider::before,\r\n.chat_divider::after {\r\n    content: '';\r\n    flex: 1;\r\n    border-bottom: 1px solid #cf9a5e;\r\n}\r\n\r\n.chat_divider::before {\r\n    margin-right: .25em;\r\n}\r\n\r\n.chat_divider::after {\r\n    margin-left: .25em;\r\n}\r\n\r\n\r\n.chat_loader {\r\n    padding: 8px 10px;\r\n    text-align: center;\r\n    font-size: medium;\r\n    color: brown;\r\n    margin: 0 0 20px 0px;\r\n    display: flex;\r\n    align-items: center;\r\n    text-align: center;\r\n    display: none;\r\n}\r\n\r\n.chat_loader::before,\r\n.chat_loader::after {\r\n    content: '';\r\n    flex: 1;\r\n    border-bottom: 1px solid #cf9a5e;\r\n}\r\n\r\n.chat_loader::before {\r\n    margin-right: .25em;\r\n}\r\n\r\n.chat_loader::after {\r\n    margin-left: .25em;\r\n}\r\n\r\n\r\n#chat-input-textarea-result {\r\n    background-color: rgba(117, 117, 117, 0.36);\r\n    position: absolute;\r\n    bottom: 51px;\r\n    border: 1.5px dashed rgb(119, 122, 125) !important;\r\n    border-radius: 6px !important;\r\n    height: auto;\r\n}\r\n\r\n.badge-chat {\r\n    vertical-align: middle;\r\n    border-radius: 5px;\r\n    float: right;\r\n    background-color: #fc544b;\r\n    color: white;\r\n    /* display: inline-block; if you get any error from this class then first uncmnt this and check */\r\n    padding: .30em 1em;\r\n    font-size: 75%;\r\n    font-weight: 700;\r\n    line-height: 1;\r\n    text-align: center;\r\n    white-space: nowrap;\r\n    margin-top: 2px;\r\n}\r\n\r\n#chat-button.opened,\r\n#chat-iframe.opened {\r\n    transition: all 0.35s ease-in-out;\r\n    box-shadow: 0px 19px 10px 2px #00000014\r\n}\r\n\r\n#chat-iframe {\r\n    border-radius: 26px;\r\n}\r\n\r\n.chat-iframe {\r\n    display: none;\r\n    position: fixed;\r\n    bottom: 80px;\r\n    right: 20px;\r\n    width: 450px;\r\n    height: 600px;\r\n    border: none;\r\n    z-index: 999;\r\n}\r\n\r\n.fixed-icon {\r\n    position: fixed;\r\n    right: 15px;\r\n    bottom: 15px;\r\n    z-index: 99999;\r\n}\r\n\r\n.progress-wrap {\r\n    position: initial !important;\r\n}\r\n\r\n#chat-button {\r\n    transition: all 0.35s ease-in-out;\r\n    /* position: fixed;\r\n    bottom: 32px;\r\n    right: 83px; */\r\n    padding: 8px 14px;\r\n    background-color: #3cc766;\r\n    color: #fff;\r\n    font-size: 20px;\r\n    border-radius: 50%;\r\n    z-index: 999;\r\n    cursor: pointer;\r\n    /* height: 2.5rem;\r\n    width: 2.5rem; */\r\n    display: flex;\r\n    justify-content: center;\r\n}\r\n\r\n\r\n.avtar_img {\r\n    height: 45px !important;\r\n}\r\n\r\n\r\n#floating_chat_view {\r\n    margin: 22px;\r\n}\r\n\r\n.seller-profile-card {\r\n    border-radius: 50%;\r\n    width: 150px;\r\n    /* background-color: antiquewhite; */\r\n    height: 150px;\r\n\r\n}\r\n\r\n.seller-profile-card img {\r\n    max-width: 100% !important;\r\n    max-height: 100% !important;\r\n    object-fit: cover !important;\r\n    border-radius: 50%;\r\n}\r\n\r\n#pickup_from_store:active {\r\n    border: var(--primary-color) !important;\r\n    /* color: var(--primary-color) !important; */\r\n}\r\n\r\n.product-styl>.swiper-style4>.swiper-controls {\r\n    display: none;\r\n}\r\n\r\n.btn-xs,\r\n.btn-group-xs>.btn {\r\n    --bs-btn-padding-y: 0.25rem;\r\n    --bs-btn-padding-x: 0.8rem;\r\n    --bs-btn-font-size: 0.6rem;\r\n    --bs-btn-border-radius: 0.4rem;\r\n}\r\n\r\n.avatar {\r\n    font-size: 12px !important;\r\n    height: 42px !important;\r\n    width: 42px !important;\r\n}\r\n\r\n.default-style {\r\n    position: relative !important;\r\n    top: 0px !important;\r\n    left: 0% !important;\r\n}\r\n\r\n.fav_button_dif {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    transform: translateY(0);\r\n    letter-spacing: -0.01rem;\r\n    position: relative;\r\n    --bs-btn-padding-y: 0.4rem;\r\n    --bs-btn-padding-x: 1rem;\r\n    --bs-btn-font-size: 0.7rem;\r\n    --bs-btn-border-radius: 0.4rem;\r\n    /* --bs-btn-padding-x: 1.2rem;\r\n    --bs-btn-padding-y: 0.5rem;\r\n    --bs-btn-font-size: 0.8rem;\r\n    --bs-btn-border-radius: 0.4rem; */\r\n\r\n    --bs-btn-color: #e2626b;\r\n    --bs-btn-border-color: #e2626b;\r\n    --bs-btn-hover-color: #fff;\r\n    --bs-btn-hover-bg: #e2626b;\r\n    --bs-btn-hover-border-color: #e2626b;\r\n    --bs-btn-focus-shadow-rgb: 226, 98, 107;\r\n    --bs-btn-active-color: #fff;\r\n    --bs-btn-active-bg: #e2626b;\r\n    --bs-btn-active-border-color: #e2626b;\r\n    --bs-btn-active-shadow: 0rem 0.25rem 0.75rem rgba(30, 34, 40, 0.15);\r\n    --bs-btn-disabled-color: #e2626b;\r\n    --bs-btn-disabled-bg: transparent;\r\n    --bs-btn-disabled-border-color: #e2626b;\r\n    --bs-gradient: none;\r\n\r\n    --bs-btn-font-family: ;\r\n    --bs-btn-font-weight: 700;\r\n    --bs-btn-line-height: 1.7;\r\n    /* --bs-btn-color: #60697b; */\r\n    --bs-btn-bg: transparent;\r\n    --bs-btn-border-width: 2px;\r\n    /* --bs-btn-border-color: transparent; */\r\n    /* --bs-btn-hover-border-color: transparent; */\r\n    --bs-btn-box-shadow: unset;\r\n    --bs-btn-disabled-opacity: 0.65;\r\n    --bs-btn-focus-box-shadow: 0 0 0 0.25rem rgba(var(--bs-btn-focus-shadow-rgb), .5);\r\n    /* display: inline-block; */\r\n    padding: var(--bs-btn-padding-y) var(--bs-btn-padding-x);\r\n    /* font-family: var(--bs-btn-font-family); */\r\n    font-size: var(--bs-btn-font-size);\r\n    font-weight: var(--bs-btn-font-weight);\r\n    line-height: var(--bs-btn-line-height);\r\n    color: var(--bs-btn-color);\r\n    text-align: center;\r\n    white-space: nowrap;\r\n    vertical-align: middle;\r\n    /* cursor: pointer;\r\n    -webkit-user-select: none;\r\n    -moz-user-select: none;\r\n    -ms-user-select: none;\r\n    user-select: none; */\r\n    border: var(--bs-btn-border-width) solid var(--bs-btn-border-color);\r\n    border-radius: var(--bs-btn-border-radius);\r\n    background-color: var(--bs-btn-bg);\r\n    box-shadow: var(--bs-btn-box-shadow);\r\n    transition: all 0.2s ease-in-out;\r\n}\r\n\r\n.fav_button_dif:hover {\r\n    color: var(--bs-btn-hover-color);\r\n    background-color: var(--bs-btn-hover-bg);\r\n    border-color: var(--bs-btn-hover-border-color);\r\n}\r\n\r\n.product-page-div>.fa-heart:hover {\r\n    color: white !important;\r\n}\r\n\r\n.empty-compare {\r\n    height: 300px;\r\n    width: 300px;\r\n}\r\n\r\n.empty-compare img {\r\n    max-width: 100%;\r\n    max-height: 100%;\r\n}\r\n\r\n.no-promo-code-img {\r\n    height: 250px;\r\n    width: 270px;\r\n}\r\n\r\n.no-promo-code-img img {\r\n    max-width: 100%;\r\n    max-height: 100%;\r\n}\r\n\r\n.select2-container {\r\n    width: 100% !important;\r\n}\r\n\r\n.mobile-search .select2-container {\r\n    border-radius: 10px !important;\r\n}\r\n\r\n.mobile-search .select2-selection {\r\n    min-height: 28px !important;\r\n    border-radius: 10px !important;\r\n}\r\n\r\n#cart-count {\r\n    position: relative;\r\n    left: -8px;\r\n}\r\n\r\n.profile_image .avatar {\r\n    height: 110px !important;\r\n    width: 110px !important;\r\n    object-fit: cover;\r\n}\r\n\r\n.theme-krajee-svg.rating-xs .krajee-icon,\r\n.theme-krajee-svg.rating-xs .krajee-icon-clear {\r\n    width: 0.8rem !important;\r\n    height: 0.8rem !important;\r\n}\r\n\r\n.theme-krajee-svg .filled-stars .krajee-icon-star {\r\n    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiBoZWlnaHQ9IjMycHgiIHdpZHRoPSIzMnB4IiB2ZXJzaW9uPSIxLjEiIGlkPSJDYXBhXzEiIHZpZXdCb3g9IjAgMCA0OC45NCA0Ny45NCIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSIgZmlsbD0iI2FhYSIgc3Ryb2tlPSIjZmZmZiI+Cgo8ZyBpZD0iU1ZHUmVwb19iZ0NhcnJpZXIiIHN0cm9rZS13aWR0aD0iMCIvPgoKPGcgaWQ9IlNWR1JlcG9fdHJhY2VyQ2FycmllciIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBzdHJva2U9IiNDQ0NDQ0MiIHN0cm9rZS13aWR0aD0iMC4zODM1MTk5OTk5OTk5OTk5NyIvPgoKPGcgaWQ9IlNWR1JlcG9faWNvbkNhcnJpZXIiPiA8cGF0aCBzdHlsZT0iZmlsbDogI0ZGQ0IwMDsiIGQ9Ik0yNi4yODUsMi40ODZsNS40MDcsMTAuOTU2YzAuMzc2LDAuNzYyLDEuMTAzLDEuMjksMS45NDQsMS40MTJsMTIuMDkxLDEuNzU3IGMyLjExOCwwLjMwOCwyLjk2MywyLjkxLDEuNDMxLDQuNDAzbC04Ljc0OSw4LjUyOGMtMC42MDgsMC41OTMtMC44ODYsMS40NDgtMC43NDIsMi4yODVsMi4wNjUsMTIuMDQyIGMwLjM2MiwyLjEwOS0xLjg1MiwzLjcxNy0zLjc0NiwyLjcyMmwtMTAuODE0LTUuNjg1Yy0wLjc1Mi0wLjM5NS0xLjY1MS0wLjM5NS0yLjQwMywwbC0xMC44MTQsNS42ODUgYy0xLjg5NCwwLjk5Ni00LjEwOC0wLjYxMy0zLjc0Ni0yLjcyMmwyLjA2NS0xMi4wNDJjMC4xNDQtMC44MzctMC4xMzQtMS42OTItMC43NDItMi4yODVsLTguNzQ5LTguNTI4IGMtMS41MzItMS40OTQtMC42ODctNC4wOTYsMS40MzEtNC40MDNsMTIuMDkxLTEuNzU3YzAuODQxLTAuMTIyLDEuNTY4LTAuNjUsMS45NDQtMS40MTJsNS40MDctMTAuOTU2IEMyMi42MDIsMC41NjcsMjUuMzM4LDAuNTY3LDI2LjI4NSwyLjQ4NnoiLz4gPC9nPgoKPC9zdmc+') !important\r\n}\r\n\r\n.theme-krajee-svg .empty-stars .krajee-icon-star {\r\n    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiBoZWlnaHQ9IjMycHgiIHdpZHRoPSIzMnB4IiB2ZXJzaW9uPSIxLjEiIGlkPSJDYXBhXzEiIHZpZXdCb3g9IjAgMCA0OC45NCA0Ny45NCIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSIgZmlsbD0iI2FhYSIgc3Ryb2tlPSIjZmZmZiI+Cgo8ZyBpZD0iU1ZHUmVwb19iZ0NhcnJpZXIiIHN0cm9rZS13aWR0aD0iMCIvPgoKPGcgaWQ9IlNWR1JlcG9fdHJhY2VyQ2FycmllciIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBzdHJva2U9IiNDQ0NDQ0MiIHN0cm9rZS13aWR0aD0iMC4zODM1MTk5OTk5OTk5OTk5NyIvPgoKPGcgaWQ9IlNWR1JlcG9faWNvbkNhcnJpZXIiPiA8cGF0aCBzdHlsZT0iLyohIGZpbGw6ICNlYWJlMTI7ICovIiBkPSJNMjYuMjg1LDIuNDg2bDUuNDA3LDEwLjk1NmMwLjM3NiwwLjc2MiwxLjEwMywxLjI5LDEuOTQ0LDEuNDEybDEyLjA5MSwxLjc1NyBjMi4xMTgsMC4zMDgsMi45NjMsMi45MSwxLjQzMSw0LjQwM2wtOC43NDksOC41MjhjLTAuNjA4LDAuNTkzLTAuODg2LDEuNDQ4LTAuNzQyLDIuMjg1bDIuMDY1LDEyLjA0MiBjMC4zNjIsMi4xMDktMS44NTIsMy43MTctMy43NDYsMi43MjJsLTEwLjgxNC01LjY4NWMtMC43NTItMC4zOTUtMS42NTEtMC4zOTUtMi40MDMsMGwtMTAuODE0LDUuNjg1IGMtMS44OTQsMC45OTYtNC4xMDgtMC42MTMtMy43NDYtMi43MjJsMi4wNjUtMTIuMDQyYzAuMTQ0LTAuODM3LTAuMTM0LTEuNjkyLTAuNzQyLTIuMjg1bC04Ljc0OS04LjUyOCBjLTEuNTMyLTEuNDk0LTAuNjg3LTQuMDk2LDEuNDMxLTQuNDAzbDEyLjA5MS0xLjc1N2MwLjg0MS0wLjEyMiwxLjU2OC0wLjY1LDEuOTQ0LTEuNDEybDUuNDA3LTEwLjk1NiBDMjIuNjAyLDAuNTY3LDI1LjMzOCwwLjU2NywyNi4yODUsMi40ODZ6Ii8+IDwvZz4KCjwvc3ZnPg==') !important\r\n}\r\n\r\n.bottom-sheet {\r\n    position: fixed;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    background-color: #fff;\r\n    border-top-left-radius: 15px;\r\n    border-top-right-radius: 15px;\r\n    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);\r\n    transform: translateY(100%);\r\n    transition: transform 0.3s ease-out;\r\n    z-index: 1050;\r\n}\r\n\r\n.bottom-sheet.show {\r\n    transform: translateY(0);\r\n}\r\n\r\n.logo-fit {\r\n    object-fit: contain;\r\n}\r\n\r\n.image-fit {\r\n    object-fit: cover;\r\n}\r\n\r\n.payment-gateway-images {\r\n    height: 30px;\r\n}\r\n\r\n.under_maintenance {\r\n    max-width: 450px;\r\n}\r\n\r\n.tab_border {\r\n    border: none;\r\n}\r\n\r\n.product_other_images {\r\n    object-fit: contain;\r\n    height: 450px !important;\r\n    width: fit-content;\r\n}\r\n\r\n.product_main_image {\r\n    width: 114px;\r\n    margin-right: 10px;\r\n}\r\n\r\n.overflow-height {\r\n    height: 530px;\r\n}\r\n\r\n.slide_image {\r\n    width: 280px;\r\n    margin-right: 30px;\r\n}\r\n\r\n.gray_slale_cod {\r\n    filter: grayscale(100%);\r\n}\r\n\r\n#search_items {\r\n    list-style: none;\r\n    /* Remove bullets */\r\n    margin: 0;\r\n    padding: 0;\r\n    max-height: 300px;\r\n    /* Fixed height for search results */\r\n    overflow-y: scroll;\r\n    /* Adds scrollbar if content exceeds the height */\r\n}\r\n\r\n#search_items::-webkit-scrollbar {\r\n    width: 7px;\r\n}\r\n\r\n#search_items::-webkit-scrollbar-track {\r\n    border-radius: 7px;\r\n}\r\n\r\n#search_items::-webkit-scrollbar-thumb {\r\n    background: rgb(66, 66, 66);\r\n    border: 1px solid rgb(255, 255, 255);\r\n    border-radius: 5px;\r\n}\r\n\r\n.search-products {\r\n    min-width: 64%;\r\n}\r\n\r\n.w-62 {\r\n    width: 62% !important;\r\n}\r\n\r\n.item {\r\n    padding: 2px;\r\n    /* border-bottom: 1px solid #eee;  */\r\n}\r\n\r\n.item .item-title {\r\n    color: #000;\r\n    /* Text color */\r\n    text-decoration: none;\r\n}\r\n\r\n/* .item .item-title:hover {\r\n    text-decoration: underline;\r\n} */\r\n\r\n.empty {\r\n    color: #999;\r\n    font-style: italic;\r\n}\r\n\r\n.select2-selection__rendered {\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n}\r\n\r\n\r\n/* support ticket chat  */\r\n\r\n.direct-chat-text {\r\n    border-radius: .3rem;\r\n    background: #d2d6de;\r\n    border: 1px solid #d2d6de;\r\n    color: #444;\r\n    margin: 5px 0 0 50px;\r\n    padding: 5px 10px;\r\n    position: relative;\r\n}\r\n\r\n.direct-chat-text {\r\n    width: fit-content;\r\n}\r\n\r\n.right .direct-chat-text {\r\n    float: right;\r\n}\r\n\r\n.direct-chat-timestamp {\r\n    margin: 0 10px;\r\n}\r\n\r\n.direct-chat-text {\r\n    margin: 5px 0 0 10px;\r\n}\r\n\r\n.right .direct-chat-text {\r\n    margin-right: 10px;\r\n}\r\n\r\n.direct-chat-messages {\r\n    height: 384px;\r\n}\r\n\r\n.direct-chat-primary .right>.direct-chat-text {\r\n    background: #007bff;\r\n    border-color: #007bff;\r\n    color: #fff;\r\n}\r\n\r\n.direct-chat-name {\r\n    font-weight: 600;\r\n}\r\n\r\n.direct-chat-timestamp {\r\n    color: #697582;\r\n}\r\n\r\n.return-reason-card {\r\n    border: 1px solid #ddd;\r\n    border-radius: 10px;\r\n    padding: 15px;\r\n    transition: 0.3s;\r\n}\r\n.return-reason-card:hover {\r\n    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\r\n}\r\n.return-reason-card img {\r\n    width: 50px;\r\n    height: 50px;\r\n    object-fit: contain;\r\n}\r\n\r\n.cursor-pointer{\r\n    cursor: pointer;\r\n}", "/* .featured-section-title h3 {\r\n    font-size: 1.50vw;\r\n    color: #2d2a2a;\r\n    font-weight: 600;\r\n    line-height: 1.4;\r\n} */\r\n\r\n.featured-section-title .title-sm {\r\n    font-size: 20px;\r\n    color: #6a6a6a;\r\n    font-weight: 500;\r\n    margin-left: 0px;\r\n}\r\n\r\n.featured-section-view-more {\r\n    float: right;\r\n    font-size: 14px;\r\n}\r\n\r\n.centered-featured-section-view-more {\r\n    float: none !important;\r\n}\r\n\r\n.seller-image-container {\r\n    /* width: 400px; */\r\n    height: 240px;\r\n    margin: initial;\r\n    /* margin-right: 20px; */\r\n    position: relative;\r\n    vertical-align: middle;\r\n    /* line-height: 230px; */\r\n}\r\n.seller-image-container img{\r\n    max-width: 100%;\r\n    max-height: 100%;\r\n}\r\n.product-image-container {\r\n    width: 280px;\r\n    height: 230px;\r\n    margin: auto;\r\n    position: relative;\r\n    vertical-align: middle;\r\n    line-height: 230px;\r\n}\r\n\r\n.product-grid {\r\n    font-family: Raleway, sans-serif;\r\n    text-align: center;\r\n    padding: 0 0 81px;\r\n    border: 1px solid rgba(0, 0, 0, 0.1);\r\n    overflow: hidden;\r\n    position: relative;\r\n    z-index: 1;\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n}\r\n\r\n.product-grid .product-image {\r\n    position: relative;\r\n    transition: all 0.3s ease 0s;\r\n    width: 100%;\r\n    margin: auto;\r\n}\r\n\r\n.product-grid .rating-sm {\r\n    font-size: 14px !important;\r\n}\r\n\r\n.product-grid .product-image img {\r\n    max-width: 100%;\r\n    max-height: 100%;\r\n}\r\n\r\n.product-grid .swiper-slide img {\r\n    width: 100%;\r\n}\r\n\r\n.product-grid .swiper-slide {\r\n    /* Center slide text vertically */\r\n    display: -webkit-box;\r\n    display: -ms-flexbox;\r\n    display: -webkit-flex;\r\n    display: flex;\r\n    -webkit-box-pack: center;\r\n    -ms-flex-pack: center;\r\n    -webkit-justify-content: center;\r\n    justify-content: center;\r\n    -webkit-box-align: center;\r\n    -ms-flex-align: center;\r\n    -webkit-align-items: center;\r\n    align-items: center;\r\n}\r\n\r\n.product-grid .pic-1 {\r\n    opacity: 1;\r\n    transition: all 0.3s ease-out 0s;\r\n}\r\n\r\n.product-grid:hover .pic-1 {\r\n    opacity: 1;\r\n}\r\n\r\n.product-grid .pic-2 {\r\n    opacity: 0;\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    transition: all 0.3s ease-out 0s;\r\n}\r\n\r\n.product-grid:hover .pic-2 {\r\n    opacity: 1;\r\n}\r\n\r\n.product-grid .social {\r\n    width: 150px;\r\n    padding: 0;\r\n    margin: 0;\r\n    list-style: none;\r\n    opacity: 0;\r\n    transform: translateY(-50%) translateX(-50%);\r\n    position: absolute;\r\n    top: 60%;\r\n    left: 50%;\r\n    z-index: 1;\r\n    transition: all 0.3s ease 0s;\r\n}\r\n\r\n.product-grid:hover .social {\r\n    opacity: 1;\r\n    top: 50%;\r\n}\r\n\r\n.product-grid .social li {\r\n    display: inline-block;\r\n}\r\n\r\n.product-grid .social li a {\r\n    color: #fff;\r\n    background-color: #333;\r\n    font-size: 16px;\r\n    line-height: 40px;\r\n    text-align: center;\r\n    height: 40px;\r\n    width: 40px;\r\n    margin: 0 2px;\r\n    display: block;\r\n    position: relative;\r\n    transition: all 0.3s ease-in-out;\r\n}\r\n\r\n.product-grid .social li a:hover {\r\n    color: var(--font-color) !important;\r\n    background-color: var(--secondary-color);\r\n}\r\n\r\n.product-grid .social li a:after,\r\n.product-grid .social li a:before {\r\n    content: attr(data-tip);\r\n    color: #fff;\r\n    background-color: #000;\r\n    font-size: 12px;\r\n    letter-spacing: 1px;\r\n    line-height: 20px;\r\n    padding: 1px 5px;\r\n    white-space: nowrap;\r\n    opacity: 0;\r\n    transform: translateX(-50%);\r\n    position: absolute;\r\n    left: 50%;\r\n    top: -30px;\r\n}\r\n\r\n.product-grid .social li a:after {\r\n    content: '';\r\n    height: 15px;\r\n    width: 15px;\r\n    border-radius: 0;\r\n    transform: translateX(-50%) rotate(45deg);\r\n    top: -20px;\r\n    z-index: -1;\r\n}\r\n\r\n.product-grid .social li a:hover:after,\r\n.product-grid .social li a:hover:before {\r\n    opacity: 1;\r\n}\r\n\r\n.product-grid .product-discount-label,\r\n.product-grid .product-new-label {\r\n    color: #fff;\r\n    background-color: #ef5777;\r\n    font-size: 12px;\r\n    text-transform: uppercase;\r\n    padding: 2px 7px;\r\n    display: block;\r\n    position: absolute;\r\n    top: 10px;\r\n    left: 0;\r\n}\r\n\r\n.product-grid .product-discount-label {\r\n    background-color: #333;\r\n    left: auto;\r\n    left: 0;\r\n    top: 38px;\r\n}\r\n\r\n.product-wishlist {\r\n    right: 0;\r\n    top: 10px;\r\n    position: absolute;\r\n}\r\n\r\n.product-grid .rating {\r\n    color: #ffd200;\r\n    font-size: 12px;\r\n    padding: 12px 0 20px;\r\n    margin: 0;\r\n    list-style: none;\r\n    position: relative;\r\n    z-index: -1;\r\n}\r\n\r\n.product-grid .rating li.disable {\r\n    color: rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.product-grid .product-content {\r\n    background-color: #fff;\r\n    text-align: center;\r\n    padding: 0px 0 5px;\r\n    margin: 0 auto;\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 4px;\r\n    z-index: 1;\r\n    transition: all 0.3s;\r\n}\r\n\r\n.product-grid:hover .product-content {\r\n    bottom: 8px;\r\n}\r\n\r\n.product-grid .title {\r\n    font-size: 16px;\r\n    font-weight: 400;\r\n    letter-spacing: 0.5px;\r\n    text-transform: capitalize;\r\n    margin: auto;\r\n    transition: all 0.3s ease 0s;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    padding-left: 4px;\r\n    margin-top: 6px;\r\n}\r\n\r\n.title a {\r\n    color: #031838;\r\n}\r\n\r\n.product-grid .title a:hover {\r\n    color: #0a78cc;\r\n}\r\n\r\n.product-grid:hover .title a {\r\n    color: #0a78cc;\r\n}\r\n\r\n.product-grid .price {\r\n    color: #333;\r\n    font-size: 16px;\r\n    font-family: Montserrat, sans-serif;\r\n    font-weight: 505;\r\n    letter-spacing: 0.6px;\r\n    margin-bottom: 3px;\r\n    text-align: center;\r\n    transition: all 0.3s;\r\n    padding-top: 8px;\r\n    padding-bottom: 8px;\r\n}\r\n\r\n.list-view-price {\r\n    color: #333;\r\n    font-size: 16px;\r\n    font-family: Montserrat, sans-serif;\r\n    font-weight: 700;\r\n    letter-spacing: 0.6px;\r\n    margin-bottom: 0px;\r\n    transition: all 0.3s;\r\n}\r\n\r\n.product-grid .price span,\r\n.striped-price {\r\n    color: red;\r\n    font-size: 13px;\r\n    font-weight: 400;\r\n    text-decoration: line-through;\r\n    margin-left: 3px;\r\n    display: inline-block;\r\n}\r\n\r\n.add-to-cart {\r\n    color: var(--font-color) !important;\r\n    font-size: 12px;\r\n    font-weight: 600;\r\n    padding: 5px 15px;\r\n    background: #0e7dd1;\r\n}\r\n\r\n\r\n/* add-to-cart*/\r\n\r\n.product-image-swiper .add-fav,\r\n.product-style-1 .add-fav,\r\n.product-style-2 .add-fav {\r\n    background-color: #fff;\r\n    z-index: 2;\r\n    top: 0;\r\n    right: 0;\r\n    position: absolute;\r\n    overflow: hidden;\r\n    border-bottom-left-radius: 58px 58px;\r\n}\r\n\r\n.product-image-swiper .add-fav>button,\r\n.product-style-1 .add-fav>button,\r\n.product-style-2 .add-fav>button {\r\n    color: #adadad;\r\n    font-size: 0.9rem;\r\n    padding-left: 11px;\r\n    padding-right: 6px;\r\n    background: white;\r\n}\r\n\r\n\r\n/* product-style-default */\r\n\r\n.product-style-default .product-image {\r\n    height: 220px;\r\n}\r\n\r\n.product-style-default .product-image .product-image-container {\r\n    height: 180px;\r\n}\r\n\r\n\r\n/*  product-style-1  */\r\n\r\n.product-style-1 {\r\n    margin: auto;\r\n    padding-bottom: 60px !important;\r\n}\r\n\r\n.product-style-1 .product-grid {\r\n    margin-top: 5px !important;\r\n}\r\n\r\n.product-style-1 .col-4 {\r\n    padding: 0px;\r\n}\r\n\r\n.product-style-1 .product-image {\r\n    height: 140px;\r\n}\r\n\r\n.product-style-1 .product-image-container {\r\n    max-width: 100px;\r\n    height: 125px;\r\n    margin: auto;\r\n    position: relative;\r\n    line-height: 145px;\r\n}\r\n\r\n.product-style-1 .add-fav>button {\r\n    font-size: 1.0rem;\r\n}\r\n\r\n.style-3-product-right-lg .product-grid {\r\n    height: 100%;\r\n    top: 35px;\r\n    border: 1px solid rgb(14 125 209);\r\n}\r\n\r\n.style-3-product-right-lg .product-grid .product-image {\r\n    height: 90%;\r\n}\r\n\r\n.style-3-product-right-lg .product-grid .product-image-container {\r\n    height: 100%;\r\n    max-width: 100%;\r\n}\r\n\r\n.style-3-product-right-lg .product-image-container img {\r\n    width: 100%;\r\n}\r\n\r\n\r\n/*  product-style-2  */\r\n\r\n.product-style-2 {\r\n    background-color: transparent !important;\r\n    box-shadow: none;\r\n}\r\n\r\n.product-style-2 .product-grid {\r\n    background-color: #fff !important;\r\n    width: 100%;\r\n}\r\n\r\n.product-style-2 div[class^=\"col\"] {\r\n    padding: 0px;\r\n}\r\n\r\n.product-style-2 .add-fav>button {\r\n    font-size: 1.0rem;\r\n}\r\n\r\n.product-style-2 .section-title {\r\n    padding-top: 40px;\r\n    padding-bottom: 30px;\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n}\r\n\r\n.product-style-2 .product-image {\r\n    height: 220px;\r\n}\r\n\r\n.product-style-2 .product-image-container {\r\n    height: 180px;\r\n}\r\n\r\n.product-style-2 .title-sm {\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n}\r\n\r\n.product-style-2 .title-sm {\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n    line-height: initial;\r\n}\r\n\r\n.product-style-2 .featured-section-view-more {\r\n    padding-top: 40px;\r\n    padding-bottom: 30px;\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n    float: none !important;\r\n    line-height: initial;\r\n}\r\n\r\n.product-style-2 .featured-section-title {\r\n    padding-left: 10px;\r\n}\r\n\r\n.product-style-2 .featured-section {\r\n    height: 226.5px;\r\n}\r\n\r\n@media only screen and (max-width: 600px) {\r\n    .product-grid {\r\n        margin-bottom: 30px;\r\n    }\r\n    .product-style-2 .product-grid {\r\n        margin-bottom: 0px !important;\r\n    }\r\n    .product-style-1-left .featured-section-title {\r\n        padding-top: 35px;\r\n    }\r\n}\r\n\r\n@media only screen and (max-width: 990px) {\r\n    .product-grid {\r\n        margin-bottom: 30px;\r\n    }\r\n}\r\n\r\n\r\n/* dark mode styling */\r\n\r\n.darkmode--activated .price {\r\n    color: #ffffff;\r\n}\r\n\r\n.darkmode--activated .product-grid {\r\n    border: 1px solid #1d1c1c;\r\n    background-color: #131313 !important;\r\n}\r\n\r\n.darkmode--activated .add-fav>button,\r\n.darkmode--activated .add-fav {\r\n    color: #a2a2a2;\r\n    background: #191919;\r\n}\r\n\r\n.darkmode--activated .social li a,\r\n.darkmode--activated .product-discount-label {\r\n    background-color: #191919;\r\n}\r\n\r\n.darkmode--activated .title-sm {\r\n    color: #b1a2a2;\r\n}"]}