<?php
try {
    $mysqli = new mysqli('localhost', 'root', '', 'ritzm');
    if ($mysqli->connect_error) {
        echo 'Connection failed: ' . $mysqli->connect_error . PHP_EOL;
        exit;
    }
    echo 'Database connection successful' . PHP_EOL;
    
    $result = $mysqli->query('SELECT * FROM themes WHERE is_default = 1');
    if ($result && $result->num_rows > 0) {
        $theme = $result->fetch_assoc();
        echo 'Current default theme: ' . $theme['name'] . ' (' . $theme['slug'] . ')' . PHP_EOL;
        echo 'Status: ' . ($theme['status'] ? 'Active' : 'Inactive') . PHP_EOL;
    } else {
        echo 'No default theme found' . PHP_EOL;
    }
    
    echo PHP_EOL . 'All themes:' . PHP_EOL;
    $result = $mysqli->query('SELECT * FROM themes ORDER BY id');
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            echo 'ID: ' . $row['id'] . ', Name: ' . $row['name'] . ', Slug: ' . $row['slug'] . ', Default: ' . ($row['is_default'] ? 'Yes' : 'No') . ', Status: ' . ($row['status'] ? 'Active' : 'Inactive') . PHP_EOL;
        }
    }
    
    $mysqli->close();
} catch (Exception $e) {
    echo 'Error: ' . $e->getMessage() . PHP_EOL;
}
?>
